/**
******************************************************************************
* @file      Handshake_Mgr.c
* <AUTHOR>
* @date      2024-11-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/
#include "Comm.h"
#include "model.h"
#include "Handshake_Mgr.h"
#include "TargetUart.h"

Handshake_t gHandshake __attribute__((section(".UartDmaSection"))) = {0};

/*******************************************************************************************
 * @brief Handshake Manager init
 * @param
 * @return
*******************************************************************************************/
void Handshake_Mgr_Init(void)
{
    int i = 0;
    memset(&gHandshake, 0x00, sizeof(Handshake_t));

    for(i=0; i<HANDSHAKE_TICK_SET_NUM; i++)
    {
        gHandshake.tx_tick_set[i] = HANDSHAKE_TICK_SET_MS;
    }
    gHandshake.tx_tick_set[0] = 1;
}

/*******************************************************************************************
 * @brief Handshake Manager reset
 * @param parameter reset
 * @return
*******************************************************************************************/
void Handshake_Mgr_Reset(int n)
{
    memset(&gHandshake.tx[n], 0x00, sizeof(Handshake_Tx_t));
    memset(&gHandshake.rx[n], 0x00, sizeof(Handshake_Rx_t));
}

/*******************************************************************************************
 * @brief Handshake send enqueue
 * @param Sending function
 * @return
*******************************************************************************************/
int Handshake_Send_EnQueue(u8 uart_no, u8 *packet_data, u16 len, u16 crc16, u16 cmd)
{
    Handshake_t *pHS = &gHandshake;
    int ret = -1;
    int front = 0;
    int tmp_front;

    tmp_front = pHS->front;
    front = pHS->front;

    tmp_front += 1;
    if(tmp_front >= HANDSHAKE_MAX_NUM)
    {
        tmp_front = 0;
    }
    if(tmp_front == pHS->rear)
    {
        Handshake_Mgr_Init();
        DEBUG_HANDSHAKE_LOG("[ERROR] in Queue Full\r\n");

        front = pHS->front;
    }

    pHS->tx[front].cmd = cmd;
    pHS->tx[front].len = len;
    memcpy(pHS->tx[front].packet_data, packet_data, len);
    
    pHS->rx[front].crc = crc16;
    pHS->rx[front].crc_flag = 1;
    pHS->rx[front].cmd = cmd;
    // DEBUG_HANDSHAKE_LOG("[OK] in Queue -- front : %d, crc : %02x %02x\r\n", front, ((uint8_t)(crc16 & 0x00FF)), ((crc16 & 0xFF00) >> 8));

    pHS->front += 1;
    if(pHS->front >= HANDSHAKE_MAX_NUM)
    {
        pHS->front = 0;
    }
    ret = 1;

    return ret;
}

/*******************************************************************************************
 * @brief Handshake send dnqueue
 * @param Sending function
 * @return
*******************************************************************************************/
int Handshake_Send_DeQueue(u8 uart_no, u8 *packet_data)
{
    // Handshake_t *pHS = &gHandshake;
    // int ret = -1;
    // int i = 0;
    // int cmp = 0;
    // int len = 0;

    // for(i=0; i<HANDSHAKE_MAX_NUM; i++)
    // {
    //     len = strlen(pHS->tx[i].packet_data);
    //     cmp = strncmp(pHS->tx[i].packet_data, packet_data, len);
    //     if(cmp == 0)
    //     {
    //         Handshake_Mgr_Reset(i);
    //         ret = 1;
    //         break;
    //     }
    // }

    // return ret; 

    return 0;
}

/*******************************************************************************************
 * @brief Handshake backend main task
 * @param
 * @return
*******************************************************************************************/
void Handshake_Backend_Task(u8 uart_no)
{
    u8 sysMode = g_hSysStatus.m_pStat->mode.system;
    Handshake_t *pHS = &gHandshake;
    int rear = 0;
    int i = 0;

    rear = pHS->rear;

    if(pHS->front != pHS->rear)
    {
        pHS->tx_tick_cnt += 1;
        if(pHS->tx_tick_cnt >= pHS->tx_tick_set[pHS->tx_send_cnt])
        {
            TBD_uart_send_data(uart_no, pHS->tx[rear].packet_data, pHS->tx[rear].len);

            // if(pHS->tx[rear].cmd == I_COMM_RX_SYSTEM_SETTING_REQUEST)
            // {
            //     DEBUG_HANDSHAKE_LOG("[OK] icomm send cmd:%d crc:%d count:%d\r\n", pHS->tx[rear].cmd, pHS->tx[rear].len, pHS->tx_send_cnt);
            // }

#ifdef EN_DBG_COMM_PARA_MSG
            Comm_Debug_Tx_Msg((uint8_t *)pHS->tx[rear].packet_data, pHS->tx[rear].len);
#endif
            pHS->tx_send_cnt += 1;
            pHS->tx_tick_cnt = 0;
            if(pHS->tx_send_cnt >= HANDSHAKE_TICK_SET_NUM)
            {
                DEBUG_HANDSHAKE_LOG("[ERROR] Sending rear : %d, count : %d\r\n", rear, pHS->tx_send_cnt);
                Set_Tx_Icomm_Handshake_Status(pHS->tx[rear].cmd, COMM_STATE_TIMEOUT);
                TBD_init_setting_uarts(TARGET_UART_NAVTEX_CONTROL, TARGET_BAUD_UART_NAVTEX_CONTROL, 0);

                if(sysMode != SYS_BOOT_RUN)
                {
                    Handshake_Mgr_Init();
                }
                else
                {
                    pHS->tx_timeout_cnt += 2;
                    if(pHS->tx_timeout_cnt >= 2)
                    {
                        Handshake_Mgr_Init();
                        pHS->tx_timeout_cnt = 2;
                    }
                }

                pHS->tx_send_cnt = 0;
            }
        }
    }
}

void Handshake_Backend_Response_Recv_Timeout(void)
{
    int i = 0;
    
    // Icomm Recv Timeout
    for(i=0; i<I_COMM_RX_TYPE_MAX; i++)
    {
        if(gIcommStatus_Rx[i].flag == COMM_STATE_RESET && gIcommStatus_Rx[i].timeout_tick != 0)
        {
            gIcommStatus_Rx[i].cur_tick += 1;
            if(gIcommStatus_Rx[i].cur_tick >= gIcommStatus_Rx[i].timeout_tick)
            {
                gIcommStatus_Rx[i].cur_tick = 0;
                Set_Rx_Icomm_Handshake_Status(i, COMM_STATE_TIMEOUT);
                DEBUG_HANDSHAKE_LOG("[ERROR] Receiving Timeout cmd : %d\r\n", i);
            }
        }
        else
        {
            gIcommStatus_Rx[i].cur_tick = 0;
        }
    }
}

void Handshake_Backend_Response_Recv(u16 cmd, u16 crc)
{
    Handshake_t *pHS = &gHandshake;
    int rear = 0;

    rear = pHS->rear;

    // DEBUG_HANDSHAKE_LOG("[Input] in CRC -- rear : %d, crc : %02x %02x\r\n", rear, ((uint8_t)(crc & 0x00FF)), ((crc & 0xFF00) >> 8));
    // DEBUG_HANDSHAKE_LOG("[Check] crc_flag : %d, crc : %02x %02x\r\n", pHS->rx[rear].crc_flag, ((uint8_t)(pHS->rx[rear].crc & 0x00FF)), ((pHS->rx[rear].crc & 0xFF00) >> 8));

    if(pHS->rx[rear].crc_flag == 1)
    {
        if(pHS->rx[rear].crc == crc && pHS->rx[rear].cmd == cmd)
        {
            Set_Tx_Icomm_Handshake_Status(cmd, COMM_STATE_SET);
            Handshake_Mgr_Reset(rear);
            pHS->tx_tick_cnt = 0;
            pHS->tx_send_cnt = 0;

            // if(cmd == I_COMM_RX_SYSTEM_SETTING_REQUEST)
            // {
            //     DEBUG_HANDSHAKE_LOG("[OK] CRC Matching -- rear:%d, cmd:%d, crc:%d\r\n", rear, cmd, crc);
            // }

            pHS->rx[rear].crc_flag = 0;
            pHS->rear += 1;
            if(pHS->rear >= HANDSHAKE_MAX_NUM)
            {
                pHS->rear = 0;
            }
        }
        else
        {
            DEBUG_HANDSHAKE_LOG("[ERROR] CRC Matching -- rear:%d, cmd:%d, crc:%d\r\n", rear, cmd, crc);
        }
    }
}
