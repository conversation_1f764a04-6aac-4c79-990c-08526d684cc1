/**
******************************************************************************
* @file      Tcomm_rx_datalink.c
* <AUTHOR>
* @date      2024-07-14
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "model.h"
#include "Testing.h"
#include "NavModem.h"

void Testing_Rx_Gpio_Control(Tcomm_Protocol_Rx_s *pRx)
{
    if(pRx->st.dt.Gpio.Disc == 1) // 4mosc sel
    {
        HAL_GPIO_WritePin(DO_Selector_4M_GPIO_Port, DO_Selector_4M_Pin, pRx->st.dt.Gpio.OnOff);		
    }
    else if(pRx->st.dt.Gpio.Disc == 2) // alarm relay
    {
        HAL_GPIO_WritePin(DO_Alarm_relay_GPIO_Port, DO_Alarm_relay_Pin, pRx->st.dt.Gpio.OnOff);
    }
    else if(pRx->st.dt.Gpio.Disc == 3) // select 4m
    {
        HAL_GPIO_WritePin(DO_4MOSC_SEL_GPIO_Port, DO_4MOSC_SEL_Pin, pRx->st.dt.Gpio.OnOff);
    }
    else if(pRx->st.dt.Gpio.Disc == 4) // antenna power
    {
        HAL_GPIO_WritePin(DO_ANT_POW_ON_GPIO_Port, DO_ANT_POW_ON_Pin, pRx->st.dt.Gpio.OnOff);
    }

    TBD_uart_send_data(TARGET_UART_DEBUG, &pRx->Buffer[3], pRx->st.hd.Frm_Len+3);
}

void Testing_Rx_RF_Control(Tcomm_Protocol_Rx_s *pRx)
{
    // if(pRx->st.dt.RF_Control.Disc == 1) // dot
    // {
    //     gNavtexTx.TxEnable_Mode = FSK_TX_TYPE_DOT;
    // }
    // else if(pRx->st.dt.RF_Control.Disc == 2) // mark
    // {
    //     gNavtexTx.TxEnable_Mode = FSK_TX_TYPE_MARK;
    // }
    // else if(pRx->st.dt.RF_Control.Disc == 3) // space
    // {
    //     gNavtexTx.TxEnable_Mode = FSK_TX_TYPE_SPACE;
    // }
    // else if(pRx->st.dt.RF_Control.Disc == 4) // msg
    // {
    //     gNavtexTx.TxEnable_Mode = FSK_TX_TYPE_MSG;
    // }
    // else if(pRx->st.dt.RF_Control.Disc == 5) // stop
    // {
    //     gNavtexTx.TxEnable_Mode = FSK_TX_TYPE_STOP;
    // }

    // TBD_uart_send_data(TARGET_UART_DEBUG, &pRx->Buffer[3], pRx->st.hd.Frm_Len+3);
}

void Testing_Rx_Parsing(Tcomm_Protocol_Rx_s *pRx)
{
    switch(pRx->st.hd.Cmd)
    {
        case T_COMM_RX_TYPE_GPIO_CONTROL:
            Testing_Rx_Gpio_Control(pRx);
        break;

        case T_COMM_RX_TYPE_RF_CONTROL:
            Testing_Rx_RF_Control(pRx);
        break;

        default:
        break;
    }
}
