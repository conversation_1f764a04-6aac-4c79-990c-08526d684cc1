/**
******************************************************************************
* @file      mt25ql128aba_conf.h
* <AUTHOR>
* @date      2023-4-19
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef __MT25QL128ABA_CONF_H__
#define __MT25QL128ABA_CONF_H__

#ifdef __cplusplus
 extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32h7xx.h"
#include "stm32h7xx_hal.h"


#define CONF_MT25QL_READ_ENHANCE      0                       /* MMP performance enhance reade enable/disable */

#define CONF_QSPI_ODS                   MT25TL01G_CR_ODS_15

#define CONF_QSPI_DUMMY_CLOCK                 8U

/* Dummy cycles for STR read mode */
#define MT25QL_DUMMY_CYCLES_READ_QUAD			8U
#define MT25QL_DUMMY_CYCLES_READ					10U

/* Dummy cycles for DTR read mode */
#define MT25QL_DUMMY_CYCLES_READ_DTR       	6U
#define MT25QL_DUMMY_CYCLES_READ_QUAD_DTR  	8U

#ifdef __cplusplus
 	}
#endif

#endif	/* __MT25QL128ABA_CONF_H__ */

