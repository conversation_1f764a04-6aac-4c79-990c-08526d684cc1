/*
 * crc_utils.c
 *
 *  Created on: Apr 1, 2025
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#include "crc_utils.h"

/**
 * @brief CRC-16 CCITT (Initial: 0xFFFF, Poly: 0x1021, No Reverse) 계산
 * @param data CRC를 계산할 데이터 배열 포인터
 * @param length 데이터 배열의 길이 (바이트 단위)
 * @retval 계산된 16비트 CRC 값
 */
uint16_t CalculateCrc16Ccitt(const uint8_t *data, uint16_t length)
{
	uint16_t crc = 0xFFFF; // 초기값
	uint16_t i = 0;
	uint8_t j = 0;

	// 데이터 길이만큼 반복
	for (i = 0; i < length; ++i)
	{
		// 현재 데이터 바이트와 XOR 연산 (상위 8비트)
		crc ^= (uint16_t) (data[i] << 8);

		// 8비트만큼 반복
		for (j = 0; j < 8; ++j)
		{
			// 최상위 비트(MSB)가 1이면 Poly (0x1021)와 XOR 연산
			if ((crc & 0x8000) != 0)
			{
				crc = (uint16_t) ((crc << 1) ^ 0x1021);
			}
			else // MSB가 0이면 왼쪽으로 1비트 시프트
			{
				crc <<= 1;
			}
		}
	}
	return crc; // 최종 계산된 CRC 값 반환
}
