/*
 * protocol.c
 *
 *  Created on: Apr 1, 2025
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#include "protocol.h"
#include "crc_utils.h" // CRC 계산 함수 사용
#include "string.h"    // memcpy, memset 사용
#include <stdlib.h>    // Math.Max 대신 사용
//#include "icom_ctrl.h"

#include "userdef.h"
typedef unsigned short ushort;

/**
 * @brief 통신 프레임 생성 함수
 */
uint16_t ConstructFrame(uint8_t *frame_buf, uint16_t buffer_size,
                        uint16_t cmd, uint16_t sub_cmd,
                        const uint8_t *payload, uint16_t payload_len,
                        uint16_t sequence)
{
    // 입력 유효성 검사
    if (frame_buf == NULL)
    {
        return 0; // 버퍼가 NULL이면 생성 불가
    }

    // 페이로드 최대 길이 검사
    if (payload_len > PROTOCOL_MAX_PAYLOAD_SIZE)
    {
        DM("[FWU] ConstructFrame Error: Payload length (%u) exceeds maximum (%u)\r\n", payload_len, PROTOCOL_MAX_PAYLOAD_SIZE);
        return 0;
    }

    // 1. 페이로드 패딩 계산
    uint16_t padding_len = (4 - (payload_len % 4)) % 4;
    uint16_t padded_payload_len = payload_len + padding_len;

    // 2. 프레임 길이(값) 계산: Len(2) + Seq(2) + Cmd(2) + Sub(2) + PaddedPayload
    uint16_t frameLength_value = (uint16_t)(2 + 2 + 2 + 2 + padded_payload_len);

    // 3. 필요한 총 프레임 크기 계산: Start(1) + Len(2) + Seq(2) + Cmd(2) + Sub(2) + PaddedPayload + CRC(2)
    uint16_t total_frame_size = 1 + 2 + 2 + 2 + 2 + padded_payload_len + 2;

    // 4. 버퍼 크기 확인
    if (total_frame_size > buffer_size)
    {
        DM("[FWU] ConstructFrame Error: Buffer too small (%u needed, %u provided)\r\n", total_frame_size, buffer_size);
        return 0; // 버퍼 크기 부족
    }

    // 5. 프레임 헤더 구성
    frame_buf[PROTO_IDX_START] = PROTOCOL_START_BYTE;                   // 시작 바이트
    frame_buf[PROTO_IDX_LEN_L] = (uint8_t)(frameLength_value & 0xFF);   // 길이 Low
    frame_buf[PROTO_IDX_LEN_H] = (uint8_t)(frameLength_value >> 8);     // 길이 High
    frame_buf[PROTO_IDX_SEQ_L] = (uint8_t)(sequence & 0xFF);            // 시퀀스 번호 Low
    frame_buf[PROTO_IDX_SEQ_H] = (uint8_t)(sequence >> 8);              // 시퀀스 번호 High
    frame_buf[PROTO_IDX_CMD_L] = (uint8_t)(cmd & 0xFF);                 // 명령어 Low
    frame_buf[PROTO_IDX_CMD_H] = (uint8_t)(cmd >> 8);                   // 명령어 High
    frame_buf[PROTO_IDX_SUB_L] = (uint8_t)(sub_cmd & 0xFF);             // 서브 명령어 Low
    frame_buf[PROTO_IDX_SUB_H] = (uint8_t)(sub_cmd >> 8);               // 서브 명령어 High

    // 6. 페이로드 복사 및 패딩 추가
    if (payload != NULL && payload_len > 0)
    {
        memcpy(frame_buf + PROTO_IDX_PAYLOAD, payload, payload_len); // 실제 페이로드 복사
    }
    if (padding_len > 0)
    {
        // 패딩 영역을 0x00으로 채움
        memset(frame_buf + PROTO_IDX_PAYLOAD + payload_len, 0x00, padding_len);
    }

    // 7. CRC 계산 (범위: Frame Length부터 Padded Payload 끝까지)
    // 시작 인덱스: PROTO_IDX_LEN_L (2)
    // 길이: frameLength_value
    uint16_t crc = CalculateCrc16Ccitt(frame_buf + PROTO_IDX_LEN_L, frameLength_value);

    // 8. CRC 추가 (Little Endian: Low Byte 먼저)
    // CRC 시작 위치 = Start(1) + Len(2) + Seq(2) + Cmd(2) + Sub(2) + PaddedPayload
    uint16_t crc_index = PROTO_IDX_PAYLOAD + padded_payload_len;
    if (crc_index + 1 >= total_frame_size) // 버퍼 오버플로우 방지
    {
         DM("[FWU] ConstructFrame Error: CRC index calculation error.\r\n");
         return 0;
    }
    frame_buf[crc_index]     = (uint8_t)(crc & 0xFF); // CRC Low
    frame_buf[crc_index + 1] = (uint8_t)(crc >> 8);   // CRC High

    // 9. 최종 프레임 길이 반환
    return total_frame_size;
}

/**
 * @brief 수신된 데이터 버퍼에서 통신 프레임 파싱 및 검증 (2바이트 Length)
 * @param buffer 수신된 데이터 버퍼 포인터
 * @param buffer_len 수신된 데이터 길이
 * @param pInfo 파싱 결과를 저장할 구조체 포인터
 * @retval ProtocolStatus_t 파싱 및 검증 결과 상태
 */
ProtocolStatus_t ParseFrame(const uint8_t *buffer, uint16_t buffer_len, ProtocolInfo_t *pInfo)
{
    // 입력 유효성 검사
    if (buffer == NULL)
    {
        return PROTOCOL_ERROR_BUFFER_NULL;
    }
    if (pInfo == NULL)
    {
        return PROTOCOL_ERROR_INFO_NULL;
    }

    // 1. 최소 프레임 길이 확인: Start(1)+Len(2)+Seq(2)+Cmd(2)+Sub(2)+CRC(2) = 11
    if (buffer_len < PROTOCOL_MIN_FRAME_SIZE)
    {
        return PROTOCOL_ERROR_INCOMPLETE; // 최소 길이 미달
    }

    // 2. 시작 바이트 확인
    if (buffer[PROTO_IDX_START] != PROTOCOL_START_BYTE)
    {
        return PROTOCOL_ERROR_BAD_START; // 시작 바이트 불일치
    }

    // 3. 프레임 길이 값 읽기 (Little Endian)
    // 값 = Len(2)+Seq(2)+Cmd(2)+Sub(2)+UnpaddedPayload 길이
    ushort frameLength_value = (ushort)(buffer[PROTO_IDX_LEN_L] | (buffer[PROTO_IDX_LEN_H] << 8));

    // 4. 길이 값 유효성 확인 (최소 8: Len+Seq+Cmd+Sub)
    if (frameLength_value < 8)
    {
        DM("[FWU] ParseFrame Error: Invalid length value (%u < 3)\r\n", frameLength_value);
        return PROTOCOL_ERROR_BAD_LENGTH; // 비정상적인 길이 값
    }
    // 길이 값 상한 확인 (오버플로우 방지)
     if (frameLength_value > PROTOCOL_MAX_LENGTH_VALUE)
     {
         DM("[FWU] ParseFrame Error: Length value (%u) exceeds maximum (%u)\r\n", frameLength_value, PROTOCOL_MAX_LENGTH_VALUE);
         return PROTOCOL_ERROR_BAD_LENGTH;
     }

    // 5. 수신된 버퍼가 최소 필요 길이(CRC 포함)를 만족하는지 확인
    // 최소 필요 길이 = Start(1) + frameLength값(Len~Payload) + CRC(2)
    // 중요: 여기서 buffer_len은 현재 버퍼에 있는 총 바이트 수임
    uint32_t minimum_required_len = 1 + frameLength_value + 2;
    if (buffer_len < minimum_required_len)
    {
        // 현재 버퍼에 CRC까지 포함한 완전한 프레임 데이터가 없음
        return PROTOCOL_ERROR_INCOMPLETE;
    }

    // 6. CRC 검증
    // 범위: Seq부터 Unpadded Payload 끝까지 (총 frameLength_value 바이트)
    // 시작 인덱스: PROTO_IDX_LEN_L (1)
    uint16_t calculated_crc = CalculateCrc16Ccitt(buffer + PROTO_IDX_LEN_L, frameLength_value);

    // 수신된 CRC 값 추출 (Little Endian: Low + High)
    // CRC 위치: 시작(1)+내용(frameLength_value)+CRC_L(1)+CRC_H(1)
    uint16_t crc_index = 1 + frameLength_value;
    // CRC 인덱스가 버퍼 범위 내에 있는지 추가 확인 (minimum_required_len 검사로 이미 확인됨)
    ushort received_crc = (ushort)(buffer[crc_index] | (buffer[crc_index + 1] << 8));

    if (calculated_crc != received_crc) // CRC 비교
    {
        DM("[FWU] ParseFrame CRC Error: Calc=0x%04X, Rcv=0x%04X\r\n", calculated_crc, received_crc);
        // CRC 오류 발생 시 어떤 데이터로 계산했는지 로그 추가 가능
        // DM("[FWU] Data for CRC (len=%u): ", frameLength_value);
        //for (int i=0; i<frameLength_value; ++i)
        //	DM("[FWU] %02X ", buffer[PROTO_IDX_SEQ+i]);
        //DM("\r\n");

        return PROTOCOL_ERROR_BAD_CRC; // CRC 불일치
    }

    // 7. CRC 검증 통과 시 정보 추출
    pInfo->sequence    = (ushort)(buffer[PROTO_IDX_SEQ_L] | buffer[PROTO_IDX_SEQ_H] << 8);
    pInfo->command.val     = (ushort)(buffer[PROTO_IDX_CMD_L] | buffer[PROTO_IDX_CMD_H] << 8);
    pInfo->sub_command = (ushort)(buffer[PROTO_IDX_SUB_L] | buffer[PROTO_IDX_SUB_H] << 8);
    // 페이로드 길이 계산: frameLength 값 - 헤더 8바이트(Len, Seq, Cmd, Sub)
    pInfo->payload_len = (uint16_t)(frameLength_value >= PROTOCOL_MIN_LENGTH_VALUE ? frameLength_value - PROTOCOL_MIN_LENGTH_VALUE : 0);
    // 페이로드 시작 위치 포인터 설정 (길이가 0이면 NULL)
    pInfo->payload     = (pInfo->payload_len > 0) ? (uint8_t*)(buffer + PROTO_IDX_PAYLOAD) : NULL;

    // 8. 성공 반환
    return PROTOCOL_OK;
}
