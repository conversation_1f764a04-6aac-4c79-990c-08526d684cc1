Configuration	Receiver_Navtex
STM32CubeMX 	6.11.1
Date	10/05/2024
MCU	STM32H743BITx



PERIPHERALS	MODES	FUNCTIONS	PINS
ADC1	IN3 Single-ended	ADC1_INP3	PA6
ADC1	IN5 Single-ended	ADC1_INP5	PB1
ADC1	IN9 Single-ended	ADC1_INP9	PB0
ADC3	Vrefint Channel	ADC3_Vref_Input	VP_ADC3_Vref_Input
DAC1	only external pin	DAC1_OUT1	PA4
DAC1	only external pin	DAC1_OUT2	PA5
ETH	RMII	ETH_CRS_DV	PA7
ETH	RMII	ETH_MDC	PC1
ETH	RMII	ETH_MDIO	PA2
ETH	RMII	ETH_REF_CLK	PA1
ETH	RMII	ETH_RXD0	PC4
ETH	RMII	ETH_RXD1	PC5
ETH	RMII	ETH_TXD0	PG13
ETH	RMII	ETH_TXD1	PG12
ETH	RMII	ETH_TX_EN	PG11
FDCAN1	Activated	FDCAN1_RX	PH14
FDCAN1	Activated	FDCAN1_TX	PH13
FMC:SDRAM 1	SDCKE0+SDNE0	FMC_SDCKE0	PH2
FMC:SDRAM 1	SDCKE0+SDNE0	FMC_SDNE0	PC2_C
FMC:SDRAM 1	4 banks	FMC_BA0	PG4
FMC:SDRAM 1	4 banks	FMC_BA1	PG5
FMC:SDRAM 1	13 bits	FMC_A0	PF0
FMC:SDRAM 1	13 bits	FMC_A1	PF1
FMC:SDRAM 1	13 bits	FMC_A2	PF2
FMC:SDRAM 1	13 bits	FMC_A3	PF3
FMC:SDRAM 1	13 bits	FMC_A4	PF4
FMC:SDRAM 1	13 bits	FMC_A5	PF5
FMC:SDRAM 1	13 bits	FMC_A6	PF12
FMC:SDRAM 1	13 bits	FMC_A7	PF13
FMC:SDRAM 1	13 bits	FMC_A8	PF14
FMC:SDRAM 1	13 bits	FMC_A9	PF15
FMC:SDRAM 1	13 bits	FMC_A10	PG0
FMC:SDRAM 1	13 bits	FMC_SDCLK	PG8
FMC:SDRAM 1	13 bits	FMC_SDNCAS	PG15
FMC:SDRAM 1	13 bits	FMC_SDNRAS	PF11
FMC:SDRAM 1	13 bits	FMC_SDNWE	PH5
FMC:SDRAM 1	13 bits	FMC_A11	PG1
FMC:SDRAM 1	13 bits	FMC_A12	PG2
FMC:SDRAM 1	16 bits	FMC_D0	PD14
FMC:SDRAM 1	16 bits	FMC_D1	PD15
FMC:SDRAM 1	16 bits	FMC_D2	PD0
FMC:SDRAM 1	16 bits	FMC_D3	PD1
FMC:SDRAM 1	16 bits	FMC_D4	PE7
FMC:SDRAM 1	16 bits	FMC_D5	PE8
FMC:SDRAM 1	16 bits	FMC_D6	PE9
FMC:SDRAM 1	16 bits	FMC_D7	PE10
FMC:SDRAM 1	16 bits	FMC_D8	PE11
FMC:SDRAM 1	16 bits	FMC_D9	PE12
FMC:SDRAM 1	16 bits	FMC_D10	PE13
FMC:SDRAM 1	16 bits	FMC_D11	PE14
FMC:SDRAM 1	16 bits	FMC_D12	PE15
FMC:SDRAM 1	16 bits	FMC_D13	PD8
FMC:SDRAM 1	16 bits	FMC_D14	PD9
FMC:SDRAM 1	16 bits	FMC_D15	PD10
FMC:SDRAM 1	16-bit byte enable	FMC_NBL0	PE0
FMC:SDRAM 1	16-bit byte enable	FMC_NBL1	PE1
HRTIM	No external Output	HRTIM_VS_hrtimTimerANoOutput	VP_HRTIM_VS_hrtimTimerANoOutput
HRTIM	No external Output	HRTIM_VS_hrtimTimerBNoOutput	VP_HRTIM_VS_hrtimTimerBNoOutput
I2C2	I2C	I2C2_SCL	PB10
I2C2	I2C	I2C2_SDA	PB11
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_BK1_IO0	PD11
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_BK1_IO1	PD12
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_BK1_IO2	PE2
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_BK1_IO3	PD13
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_BK1_NCS	PG6
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_CLK	PB2
RCC	Crystal/Ceramic Resonator	RCC_OSC_IN	PH0-OSC_IN (PH0)
RCC	Crystal/Ceramic Resonator	RCC_OSC_OUT	PH1-OSC_OUT (PH1)
RCC	Crystal/Ceramic Resonator	RCC_OSC32_IN	PC14-OSC32_IN (OSC32_IN)
RCC	Crystal/Ceramic Resonator	RCC_OSC32_OUT	PC15-OSC32_OUT (OSC32_OUT)
RTC	Activate Clock Source	RTC_VS_RTC_Activate	VP_RTC_VS_RTC_Activate
RTC	Activate Calendar	RTC_VS_RTC_Calendar	VP_RTC_VS_RTC_Calendar
SYS	TIM1	SYS_VS_tim1	VP_SYS_VS_tim1
TIM3	Internal Clock	TIM3_VS_ClockSourceINT	VP_TIM3_VS_ClockSourceINT
UART4	Asynchronous	UART4_RX	PA11
UART4	Asynchronous	UART4_TX	PA12
UART5	Asynchronous	UART5_RX	PB12
UART5	Asynchronous	UART5_TX	PB13
UART7	Asynchronous	UART7_RX	PF6
UART7	Asynchronous	UART7_TX	PF7
USART1	Asynchronous	USART1_RX	PB15
USART1	Asynchronous	USART1_TX	PB14
USART2	Asynchronous	USART2_RX	PD6
USART2	Asynchronous	USART2_TX	PD5
USART3	Asynchronous	USART3_RX	PC11
USART3	Asynchronous	USART3_TX	PC10



Pin Nb	PINs	FUNCTIONs	LABELs
1	PE2	QUADSPI_BK1_IO2	
8	PC13	GPIO_Output	DO_ETH_RESET
9	PC14-OSC32_IN (OSC32_IN)	RCC_OSC32_IN	
10	PC15-OSC32_OUT (OSC32_OUT)	RCC_OSC32_OUT	
16	PF0	FMC_A0	
17	PF1	FMC_A1	
18	PF2	FMC_A2	
22	PF3	FMC_A3	
23	PF4	FMC_A4	
24	PF5	FMC_A5	
27	PF6	UART7_RX	
28	PF7	UART7_TX	
32	PH0-OSC_IN (PH0)	RCC_OSC_IN	
33	PH1-OSC_OUT (PH1)	RCC_OSC_OUT	
36	PC1	ETH_MDC	
37	PC2_C	FMC_SDNE0	
43	PA0	GPIO_Output	DO_FSK_CHECK_INT
44	PA1	ETH_REF_CLK	
45	PA2	ETH_MDIO	
46	PH2	FMC_SDCKE0	
49	PH5	FMC_SDNWE	
53	PA4	DAC1_OUT1	AO_FSK_OUT
54	PA5	DAC1_OUT2	AO_FREQ_INT
55	PA6	ADC1_INP3	AI_ANT_POWER
56	PA7	ETH_CRS_DV	
57	PC4	ETH_RXD0	
58	PC5	ETH_RXD1	
61	PB0	ADC1_INP9	AI_FSK_LOC
62	PB1	ADC1_INP5	AI_FSK_INT
63	PB2	QUADSPI_CLK	
70	PF11	FMC_SDNRAS	
71	PF12	FMC_A6	
74	PF13	FMC_A7	
75	PF14	FMC_A8	
76	PF15	FMC_A9	
77	PG0	FMC_A10	
78	PG1	FMC_A11	
79	PE7	FMC_D4	
80	PE8	FMC_D5	
81	PE9	FMC_D6	
84	PE10	FMC_D7	
85	PE11	FMC_D8	
86	PE12	FMC_D9	
87	PE13	FMC_D10	
88	PE14	FMC_D11	
89	PE15	FMC_D12	
90	PB10	I2C2_SCL	
91	PB11	I2C2_SDA	
100	PH10	GPIO_Output	DO_ANT_POW_ON
102	PH12	GPIO_Input	DI_ETH_nINT
104	PB12	UART5_RX	
105	PB13	UART5_TX	
106	PB14	USART1_TX	
107	PB15	USART1_RX	
108	PD8	FMC_D13	
109	PD9	FMC_D14	
110	PD10	FMC_D15	
111	PD11	QUADSPI_BK1_IO0	
112	PD12	QUADSPI_BK1_IO1	
113	PD13	QUADSPI_BK1_IO3	
116	PD14	FMC_D0	
117	PD15	FMC_D1	
129	PG2	FMC_A12	
131	PG4	FMC_BA0	
132	PG5	FMC_BA1	
133	PG6	QUADSPI_BK1_NCS	
134	PG7	GPIO_Output	TP_PG7
135	PG8	FMC_SDCLK	
140	PC8	GPIO_Output	TP_PC8
145	PA11	UART4_RX	
146	PA12	UART4_TX	
151	PH13	FDCAN1_TX	
152	PH14	FDCAN1_RX	
154	PI0	GPIO_Output	DO_FSK_CHECK_LOC
155	PI1	GPIO_Output	DO_Selector_4M
156	PI2	GPIO_Output	DO_Alarm_relay
160	PA15 (JTDI)	GPIO_Output	TP_PA15
161	PC10	USART3_TX	
162	PC11	USART3_RX	
164	PD0	FMC_D2	
165	PD1	FMC_D3	
166	PD2	GPIO_Output	TP_PD2
167	PD3	GPIO_Output	TP_PD3
168	PD4	GPIO_Output	TP_LED_1
169	PD5	USART2_TX	
172	PD6	USART2_RX	
173	PD7	GPIO_Output	TP_LED_2
174	PJ12	GPIO_Output	TP_LED_3
179	PG10	GPIO_Output	DO_4MOSC_SEL
180	PG11	ETH_TX_EN	
181	PG12	ETH_TXD1	
182	PG13	ETH_TXD0	
191	PG15	FMC_SDNCAS	
200	PE0	FMC_NBL0	
201	PE1	FMC_NBL1	
PERIPHERALS	MODES	FUNCTIONS	PINS
ADC1	IN3 Single-ended	ADC1_INP3	PA6
ADC1	IN5 Single-ended	ADC1_INP5	PB1
ADC1	IN9 Single-ended	ADC1_INP9	PB0
ADC3	Vrefint Channel	ADC3_Vref_Input	VP_ADC3_Vref_Input
DAC1	only external pin	DAC1_OUT1	PA4
DAC1	only external pin	DAC1_OUT2	PA5
ETH	RMII	ETH_CRS_DV	PA7
ETH	RMII	ETH_MDC	PC1
ETH	RMII	ETH_MDIO	PA2
ETH	RMII	ETH_REF_CLK	PA1
ETH	RMII	ETH_RXD0	PC4
ETH	RMII	ETH_RXD1	PC5
ETH	RMII	ETH_TXD0	PG13
ETH	RMII	ETH_TXD1	PG12
ETH	RMII	ETH_TX_EN	PG11
FDCAN1	Activated	FDCAN1_RX	PH14
FDCAN1	Activated	FDCAN1_TX	PH13
FMC:SDRAM 1	SDCKE0+SDNE0	FMC_SDCKE0	PH2
FMC:SDRAM 1	SDCKE0+SDNE0	FMC_SDNE0	PC2_C
FMC:SDRAM 1	4 banks	FMC_BA0	PG4
FMC:SDRAM 1	4 banks	FMC_BA1	PG5
FMC:SDRAM 1	13 bits	FMC_A0	PF0
FMC:SDRAM 1	13 bits	FMC_A1	PF1
FMC:SDRAM 1	13 bits	FMC_A2	PF2
FMC:SDRAM 1	13 bits	FMC_A3	PF3
FMC:SDRAM 1	13 bits	FMC_A4	PF4
FMC:SDRAM 1	13 bits	FMC_A5	PF5
FMC:SDRAM 1	13 bits	FMC_A6	PF12
FMC:SDRAM 1	13 bits	FMC_A7	PF13
FMC:SDRAM 1	13 bits	FMC_A8	PF14
FMC:SDRAM 1	13 bits	FMC_A9	PF15
FMC:SDRAM 1	13 bits	FMC_A10	PG0
FMC:SDRAM 1	13 bits	FMC_SDCLK	PG8
FMC:SDRAM 1	13 bits	FMC_SDNCAS	PG15
FMC:SDRAM 1	13 bits	FMC_SDNRAS	PF11
FMC:SDRAM 1	13 bits	FMC_SDNWE	PH5
FMC:SDRAM 1	13 bits	FMC_A11	PG1
FMC:SDRAM 1	13 bits	FMC_A12	PG2
FMC:SDRAM 1	16 bits	FMC_D0	PD14
FMC:SDRAM 1	16 bits	FMC_D1	PD15
FMC:SDRAM 1	16 bits	FMC_D2	PD0
FMC:SDRAM 1	16 bits	FMC_D3	PD1
FMC:SDRAM 1	16 bits	FMC_D4	PE7
FMC:SDRAM 1	16 bits	FMC_D5	PE8
FMC:SDRAM 1	16 bits	FMC_D6	PE9
FMC:SDRAM 1	16 bits	FMC_D7	PE10
FMC:SDRAM 1	16 bits	FMC_D8	PE11
FMC:SDRAM 1	16 bits	FMC_D9	PE12
FMC:SDRAM 1	16 bits	FMC_D10	PE13
FMC:SDRAM 1	16 bits	FMC_D11	PE14
FMC:SDRAM 1	16 bits	FMC_D12	PE15
FMC:SDRAM 1	16 bits	FMC_D13	PD8
FMC:SDRAM 1	16 bits	FMC_D14	PD9
FMC:SDRAM 1	16 bits	FMC_D15	PD10
FMC:SDRAM 1	16-bit byte enable	FMC_NBL0	PE0
FMC:SDRAM 1	16-bit byte enable	FMC_NBL1	PE1
HRTIM	No external Output	HRTIM_VS_hrtimTimerANoOutput	VP_HRTIM_VS_hrtimTimerANoOutput
HRTIM	No external Output	HRTIM_VS_hrtimTimerBNoOutput	VP_HRTIM_VS_hrtimTimerBNoOutput
I2C2	I2C	I2C2_SCL	PB10
I2C2	I2C	I2C2_SDA	PB11
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_BK1_IO0	PD11
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_BK1_IO1	PD12
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_BK1_IO2	PE2
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_BK1_IO3	PD13
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_BK1_NCS	PG6
QUADSPI	Bank1 with Quad SPI Lines	QUADSPI_CLK	PB2
RCC	Crystal/Ceramic Resonator	RCC_OSC_IN	PH0-OSC_IN (PH0)
RCC	Crystal/Ceramic Resonator	RCC_OSC_OUT	PH1-OSC_OUT (PH1)
RCC	Crystal/Ceramic Resonator	RCC_OSC32_IN	PC14-OSC32_IN (OSC32_IN)
RCC	Crystal/Ceramic Resonator	RCC_OSC32_OUT	PC15-OSC32_OUT (OSC32_OUT)
RTC	Activate Clock Source	RTC_VS_RTC_Activate	VP_RTC_VS_RTC_Activate
RTC	Activate Calendar	RTC_VS_RTC_Calendar	VP_RTC_VS_RTC_Calendar
SYS	TIM1	SYS_VS_tim1	VP_SYS_VS_tim1
TIM3	Internal Clock	TIM3_VS_ClockSourceINT	VP_TIM3_VS_ClockSourceINT
UART4	Asynchronous	UART4_RX	PA11
UART4	Asynchronous	UART4_TX	PA12
UART5	Asynchronous	UART5_RX	PB12
UART5	Asynchronous	UART5_TX	PB13
UART7	Asynchronous	UART7_RX	PF6
UART7	Asynchronous	UART7_TX	PF7
USART1	Asynchronous	USART1_RX	PB15
USART1	Asynchronous	USART1_TX	PB14
USART2	Asynchronous	USART2_RX	PD6
USART2	Asynchronous	USART2_TX	PD5
USART3	Asynchronous	USART3_RX	PC11
USART3	Asynchronous	USART3_TX	PC10



Pin Nb	PINs	FUNCTIONs	LABELs
1	PE2	QUADSPI_BK1_IO2	
8	PC13	GPIO_Output	DO_ETH_RESET
9	PC14-OSC32_IN (OSC32_IN)	RCC_OSC32_IN	
10	PC15-OSC32_OUT (OSC32_OUT)	RCC_OSC32_OUT	
16	PF0	FMC_A0	
17	PF1	FMC_A1	
18	PF2	FMC_A2	
22	PF3	FMC_A3	
23	PF4	FMC_A4	
24	PF5	FMC_A5	
27	PF6	UART7_RX	
28	PF7	UART7_TX	
32	PH0-OSC_IN (PH0)	RCC_OSC_IN	
33	PH1-OSC_OUT (PH1)	RCC_OSC_OUT	
36	PC1	ETH_MDC	
37	PC2_C	FMC_SDNE0	
43	PA0	GPIO_Output	DO_FSK_CHECK_INT
44	PA1	ETH_REF_CLK	
45	PA2	ETH_MDIO	
46	PH2	FMC_SDCKE0	
49	PH5	FMC_SDNWE	
53	PA4	DAC1_OUT1	AO_FSK_OUT
54	PA5	DAC1_OUT2	AO_FREQ_INT
55	PA6	ADC1_INP3	AI_ANT_POWER
56	PA7	ETH_CRS_DV	
57	PC4	ETH_RXD0	
58	PC5	ETH_RXD1	
61	PB0	ADC1_INP9	AI_FSK_LOC
62	PB1	ADC1_INP5	AI_FSK_INT
63	PB2	QUADSPI_CLK	
70	PF11	FMC_SDNRAS	
71	PF12	FMC_A6	
74	PF13	FMC_A7	
75	PF14	FMC_A8	
76	PF15	FMC_A9	
77	PG0	FMC_A10	
78	PG1	FMC_A11	
79	PE7	FMC_D4	
80	PE8	FMC_D5	
81	PE9	FMC_D6	
84	PE10	FMC_D7	
85	PE11	FMC_D8	
86	PE12	FMC_D9	
87	PE13	FMC_D10	
88	PE14	FMC_D11	
89	PE15	FMC_D12	
90	PB10	I2C2_SCL	
91	PB11	I2C2_SDA	
100	PH10	GPIO_Output	DO_ANT_POW_ON
102	PH12	GPIO_Input	DI_ETH_nINT
104	PB12	UART5_RX	
105	PB13	UART5_TX	
106	PB14	USART1_TX	
107	PB15	USART1_RX	
108	PD8	FMC_D13	
109	PD9	FMC_D14	
110	PD10	FMC_D15	
111	PD11	QUADSPI_BK1_IO0	
112	PD12	QUADSPI_BK1_IO1	
113	PD13	QUADSPI_BK1_IO3	
116	PD14	FMC_D0	
117	PD15	FMC_D1	
129	PG2	FMC_A12	
131	PG4	FMC_BA0	
132	PG5	FMC_BA1	
133	PG6	QUADSPI_BK1_NCS	
134	PG7	GPIO_Output	TP_PG7
135	PG8	FMC_SDCLK	
140	PC8	GPIO_Output	TP_PC8
145	PA11	UART4_RX	
146	PA12	UART4_TX	
151	PH13	FDCAN1_TX	
152	PH14	FDCAN1_RX	
154	PI0	GPIO_Output	DO_FSK_CHECK_LOC
155	PI1	GPIO_Output	DO_Selector_4M
156	PI2	GPIO_Output	DO_Alarm_relay
160	PA15 (JTDI)	GPIO_Output	TP_PA15
161	PC10	USART3_TX	
162	PC11	USART3_RX	
164	PD0	FMC_D2	
165	PD1	FMC_D3	
166	PD2	GPIO_Output	TP_PD2
167	PD3	GPIO_Output	TP_PD3
168	PD4	GPIO_Output	TP_LED_1
169	PD5	USART2_TX	
172	PD6	USART2_RX	
173	PD7	GPIO_Output	TP_LED_2
174	PJ12	GPIO_Output	TP_LED_3
179	PG10	GPIO_Output	DO_4MOSC_SEL
180	PG11	ETH_TX_EN	
181	PG12	ETH_TXD1	
182	PG13	ETH_TXD0	
191	PG15	FMC_SDNCAS	
200	PE0	FMC_NBL0	
201	PE1	FMC_NBL1	



SOFTWARE PROJECT

Project Settings : 
Project Name : Receiver_Navtex
Project Folder : C:\Code\NAVTEX\Firmware\Receiver_Navtex
Toolchain / IDE : STM32CubeIDE
Firmware Package Name and Version : STM32Cube FW_H7 V1.11.2


Code Generation Settings : 
STM32Cube MCU packages and embedded software packs : Copy only the necessary library files
Generate peripheral initialization as a pair of '.c/.h' files per peripheral : No
Backup previously generated files when re-generating : No
Delete previously generated files when not re-generated : Yes
Set all free pins as analog (to optimize the power consumption) : No


Toolchains Settings : 
Compiler Optimizations : 





