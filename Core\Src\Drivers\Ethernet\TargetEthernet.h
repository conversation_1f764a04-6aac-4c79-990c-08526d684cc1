/**
******************************************************************************
* @file      TargetEthernet.h
* <AUTHOR>
* @date      2023-1-10
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef _TARGET_ETHERNET_H_
#define _TARGET_ETHERNET_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32h7xx_hal.h"
#include "TargetTCP.h"
#include "TargetUDP.h"

#include "lwipopts.h"

#define TBD_ETH_DMA_DESC_ADDR	0x30000000					// Device not cacheable for ETH DMA descriptors(ETH_DMADescTypeDef  DMARxDscrTab)
#define TBD_LWIP_HEAP_ADDR		LWIP_RAM_HEAP_POINTER	// Normal Non Cacheable for LwIP RAM heap which contains the Tx buffers

void TargetEthernet_Init(void const *argument);

#ifdef __cplusplus
}
#endif

#endif	/* _TARGET_SDRAM_H_ */