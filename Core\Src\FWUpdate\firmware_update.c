/*
 * firmware_update.c
 *
 *  Created on: Apr 1, 2025
 *      Author: <PERSON><PERSON><PERSON>
 */

#include "firmware_update.h"
#include <stdio.h>
#include <string.h>       // memcpy, memset 등 사용
#include <stdlib.h>       // abs 사용 (혹은 직접 구현)
#include <errno.h>        // errno 사용
#include "flash_if.h"     // 플래시 인터페이스 함수 헤더
#include "protocol.h"     // 프로토콜 처리 함수 헤더
#include "crc_utils.h"    // CRC 계산 함수 헤더
#include "main.h"         // HAL 및 기타 기본 헤더 포함 가정
#include "cmsis_os.h"     // FreeRTOS API
#include "lwip/sockets.h" // lwIP 소켓 API
#include "lwip/inet.h"    // inet_addr 등 사용
#include "sha256.h"
//#include "icom_ctrl.h"
#include "system_status.h"
#include "ext_can_ctrl.h"
#include "FW_Update.h"
#include "model.h"

extern CRC_HandleTypeDef hcrc;

// --- RTOS 핸들 ---
osThreadId FwuTaskHandle;       // 펌웨어 업데이트 태스크 핸들

// --- 전역 변수 ---
static volatile FirmwareUpdateState_t g_fwu_state = FWU_STATE_IDLE; // 현재 상태
static int g_udp_socket = -1;                                       // UDP 리스닝 및 응답 소켓 (18931 바인딩)
static int g_tcp_socket = -1;                                       // TCP 통신 소켓
static struct sockaddr_in g_pc_addr;                                // 연결할 PC 주소 정보
static uint32_t g_current_page_write_addr = 0;                      // 현재 기록 중인 플래시 섹터 시작 주소
static uint32_t g_bytes_in_page_buffer = 0;                         // RAM 버퍼에 채워진 바이트 수
static uint32_t g_expected_next_relative_addr = 0;                  // 예상 다음 상대 주소
static uint8_t g_last_received_tcp_seq = 0;                         // TCP ACK 생성용 마지막 수신 시퀀스
static uint32_t g_total_bytes_written = 0;                          // 총 기록된 바이트 수
static uint32_t g_last_activity_tick = 0;                           // 마지막 TCP 활동 시간 (타임아웃용)
static volatile uint8_t g_flash_op_pending = 0;                     // 플래시 작업 진행 중 플래그
static uint16_t g_expected_pc_sequence = 0;                         // PC로부터 다음에 받을 시퀀스 번호
static uint8_t g_sequence_initialized = 0;                          // 시퀀스 번호 추적 시작 여부 (0: 시작 안함, 1: 시작됨)
static uint8_t g_can_metainfo_received = 0;
static uint8_t g_timeout_scale = 2;                                 // 타임아웃 스케일 설정

static FWU_DeviceInfo_t g_self_dev_info;
static uint8_t g_self_dev_param;

static fwu_device_param_t g_tgt_dev_param_t;
static uint8_t g_tgt_dev_param;

static FWU_UpdateInfo_t g_up_info;                                  // 업데이트할 장치의 정보

// --- SHA-256 컨텍스트 변수 ---
static SHA256_CTX g_sha256_ctx;
static uint8_t g_calculated_hash[SHA256_HASH_SIZE];

// --- RAM 버퍼 선언 (RAM_D2 영역 - 128KB) ---
// static uint8_t g_flash_page_buffer[FWU_FLASH_PAGE_SIZE] __attribute__((section(".ram_d2_bss"))) __attribute__((aligned(32)));
// static uint8_t g_flash_page_buffer[2] __attribute__((section(".ram_d2_bss"))) __attribute__((aligned(32)));
static uint8_t g_flash_page_buffer[FWU_FLASH_PAGE_SIZE] __attribute__((section("DTCM_RAM"))) __attribute__((aligned(32)));

// --- TCP 수신 버퍼 관련 ---
#define RX_BUFFER_SIZE (FWU_BUFFER_SIZE * 2) // 프레임이 나뉘는 경우 대비
static uint8_t tcp_rx_buffer[RX_BUFFER_SIZE];
static uint16_t tcp_rx_buffer_len = 0;
#if FWU_UART_RELAY_SIZE != 1024
static uint8_t uart_rx_buffer[FWU_UART_BUFFER_SIZE];
#endif
//static uint8_t* g_recv_uart_frame = NULL;
//static volatile uint16_t g_recv_from_uart_len = 0;

// --- CAN 메시지 전송을 위한 함수 포인터
//typedef int (*can_abu_send_cmd)(fwu_device_param_t, uint32_t, uint8_t, uint8_t, uint8_t*);

// --- 내부 함수 프로토타입 ---
static int setup_udp_listener(void);
static void handle_udp_receive(int sock);
static void send_device_search_response_udp(FWU_DeviceInfo_t deviceInfo, int sock, const struct sockaddr_in *dest_addr, uint16_t sequence);
static inline void send_udp_message(int sock, const struct sockaddr_in *dest_addr, uint8_t* p_frame_buf, uint16_t frame_len);
static int start_tcp_connection(void);
static void handle_tcp_connection(int sock);
static void process_tcp_data(uint8_t *data, int len);
static int handle_binary_chunk_frame(ProtocolInfo_t *pInfo);
static int handle_binary_complete_frame(ProtocolInfo_t *pInfo);
#if FWU_UART_RELAY_SIZE != 1024
static int handle_proxy_binary_chunk_frame(ProtocolInfo_t *pInfo);
#endif
static int handle_proxy_binary_complete_frame(ProtocolInfo_t *pInfo);
static int handle_error_frame(ProtocolInfo_t *pInfo); // 오류 프레임 처리 함수
static void send_tcp_ack(int sock, uint16_t cmd, uint16_t sub_cmd, uint16_t sequence);
static void send_tcp_error(int sock, uint32_t error_code, uint16_t sequence);
static uint8_t const_send_icom_fwu_cmd(uint16_t cmd, uint16_t sub_cmd, uint8_t* p_data, uint16_t len);
static uint32_t calculate_crc32(const uint8_t* data, uint32_t length);
static uint32_t calculate_crc32_sw(const uint8_t* data, uint32_t length);
static int handle_can_binary_chunk_frame(ProtocolInfo_t *pInfo);
static int handle_can_binary_complete_frame(ProtocolInfo_t *pInfo);
static void reset_update_state(void);
static void close_tcp_socket(void);
static void sha256_init_wrapper(void);
static void sha256_update_wrapper(uint8_t *data, uint32_t len);
static void sha256_final_wrapper(uint8_t *hash_out);
static char* BitConverter_ToString(uint8_t *data, int len); // 로그용 헬퍼

////
const FWU_DeviceInfo_t g_self_dev_meta_info __attribute__((section(".meta_info_main"))) = {
		.DeviceParam.DeviceType = FWU_DEVICE_NAVTEX,
		.DeviceParam.SubDeviceType = FWU_SUB_DEVICE_RECEIVER,
		.DeviceParam.DeviceNumber = 0,
		.MajorVersion = SYSTEM_SW_VER_MAJOR,
		.MinorVersion = SYSTEM_SW_VER_MINOR,
		.PatchVersion = SYSTEM_SW_VER_REV,
		.IpAddress = 0,
		.SubnetMask = 0,
};

/** 
  * @brief  
  * @param  
  * @retval 
*/
int TBD_can_tx(FDCAN_HandleTypeDef *pHandle, uint32_t can_id, uint8_t *p_data, uint8_t data_size)
{
	int8_t ret = 1;
// 	uint32_t data_len_code = 0;
// 	FDCAN_TxHeaderTypeDef  packet_header;

// 	if(pHandle == NULL)
// 	{
// 		return 0;
// 	}
	
// 	data_len_code = TBD_can_get_data_length_code(data_size);

// 	if (data_len_code == 0xFFFFFFFF)
// 	{
// 		return 0;
// 	}
// 	else
// 	{
// #ifdef CAN_TX_DEBUG
// 		DM("<===[CAN_TX]id=0x%lx\r\n",can_id);
// #endif
		
// 		packet_header.Identifier = can_id;
// 		packet_header.IdType = FDCAN_EXTENDED_ID;
// 		packet_header.TxFrameType = FDCAN_DATA_FRAME;
// 		packet_header.DataLength = FDCAN_DLC_BYTES_8;
// 		packet_header.ErrorStateIndicator = FDCAN_ESI_ACTIVE;
// 		packet_header.BitRateSwitch = FDCAN_BRS_OFF;
// 		packet_header.FDFormat = FDCAN_CLASSIC_CAN;
// 		packet_header.TxEventFifoControl = FDCAN_NO_TX_EVENTS;
// 		packet_header.MessageMarker = 0;

// 		if (HAL_FDCAN_AddMessageToTxFifoQ(pHandle, &packet_header, p_data) != HAL_OK)
// 		{
// 			DM("CAN TX ERROR!!!!\r\n");
// 			return 0;
// 		}

// #ifdef CAN_TX_DEBUG
// 		for(int i = 0; i<MAX_CAN_DATA_SIZE; i++)
// 		{
// 			if(i == (MAX_CAN_DATA_SIZE-1))
// 			{
// 				DM("0x%02x\r\n", p_data[i]);
// 			}
// 			else
// 			{
// 				DM("0x%02x,", p_data[i]);
// 			}
// 		}
// #endif		
// 	}

	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
void TBD_can_init_msg(can_msg_s *pMsg)
{
	// if(pMsg != NULL)
	// {
	// 	pMsg->id = 0;
	// 	pMsg->id_type = 0;
	// 	memset((void *)pMsg->data,0x00,MAX_CAN_DATA_SIZE);
	// }		
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
int TBD_abcan_get_msg(can_msg_s *pMsg)
{
	// int ret = TBD_FAIL;
	// if(pMsg != NULL)
	// {
	// 	if(TBD_can_dequeue_msg(&g_abcan_msg_queue, pMsg) != TBD_FAIL)
	// 	{
	// 		ret = TBD_SUCCESS;
	// 	}
	// }
	
	// return ret;
}

/**
 * @brief
 * @param
 * @return
*/
void icom_send_req_device_fw_info(void)
{
	Internal_Tx_FWU_Request_Device_Fw_Info();
	DM("==>[ICOM]SEND FWU REQUEST\r\n");
}

/**
 * @brief
 * @param
 * @return
*/
int icom_send_fwu_cmd(uint8_t* frame_buf, uint32_t frame_len)
{
	// inc
	// start byte
	Internal_Tx_FWU_Cmd(frame_buf, frame_len);

	return 1;
}

static uint8_t const_send_icom_fwu_cmd(uint16_t cmd, uint16_t sub_cmd, uint8_t* p_data, uint16_t len)
{
	Internal_Tx_FWU_Cmd_ex(cmd, sub_cmd, p_data, len);

	return 1;
}

/**
 * @brief 펌웨어 업데이트 모듈 및 RTOS 태스크 초기화
 */
int FirmwareUpdate_Init(void)
{
	if (FwuTaskHandle != NULL)
		return 0;

	DM("[FWU] Firmware Update Module Init...\r\n");

	// --- 장치 정보 설정 ---
	g_self_dev_info.DeviceParam.DeviceType 		= g_self_dev_meta_info.DeviceParam.DeviceType;
	g_self_dev_info.DeviceParam.SubDeviceType 	= g_self_dev_meta_info.DeviceParam.SubDeviceType;
	g_self_dev_info.DeviceParam.DeviceNumber 	= g_self_dev_meta_info.DeviceParam.DeviceNumber;
	g_self_dev_info.MajorVersion 				= g_self_dev_meta_info.MajorVersion;
	g_self_dev_info.MinorVersion 				= g_self_dev_meta_info.MinorVersion;
	g_self_dev_info.PatchVersion 				= g_self_dev_meta_info.PatchVersion;

	if (netif_default != NULL)
	{
		g_self_dev_info.IpAddress = ip4_addr_get_u32(netif_ip4_addr(netif_default));
		g_self_dev_info.SubnetMask = ip4_addr_get_u32(netif_ip4_netmask(netif_default));
	}
	else
	{
		g_self_dev_info.IpAddress = 0;
		g_self_dev_info.SubnetMask = 0;
	}

	reset_update_state(); // 상태 및 변수 초기화
	if (setup_udp_listener() < 0)
	{
		DM("[FWU] Error: Failed to setup UDP listener socket.\r\n");
		return -1;
	}

	// RTOS 태스크 생성
	osThreadDef(fwuTask, FirmwareUpdate_Task, osPriorityRealtime, 0, configMINIMAL_STACK_SIZE * 8); // 스택 크기 확인 필요
	FwuTaskHandle = osThreadCreate(osThread(fwuTask), NULL);

    //xTaskCreate(FirmwareUpdate_Task, "fwuTask", configMINIMAL_STACK_SIZE * 8, NULL, osPriorityNormal, &xFwuTaskHandle);

	if (FwuTaskHandle == NULL)
	{
		DM("[FWU] Error: Failed to create Firmware Update Task.\r\n");
		if (g_udp_socket != -1)
		{
			lwip_close(g_udp_socket); // 생성된 UDP 소켓 닫기
		}
		return -1;
	}

	DM("[FWU] Firmware Update Task Created.\r\n");
	return 0;
}

/**
 * @brief UDP 리스닝 소켓 설정
 */
static int setup_udp_listener(void)
{
	struct sockaddr_in addr;
	int opt = 1; // SO_BROADCAST 옵션용

	// 기존 소켓 닫기
	if (g_udp_socket != -1)
	{
		lwip_close(g_udp_socket);
		g_udp_socket = -1;
	}

	// UDP 소켓 생성
	g_udp_socket = lwip_socket(AF_INET, SOCK_DGRAM, 0);
	if (g_udp_socket < 0)
	{
		DM("[FWU] Error: Cannot create UDP socket. errno=%d\r\n", errno);
		return -1;
	}

	// SO_BROADCAST 옵션 활성화 (브로드캐스트 수신 위해)
	if (lwip_setsockopt(g_udp_socket, SOL_SOCKET, SO_BROADCAST, &opt, sizeof(opt)) < 0)
	{
		DM("[FWU] Warning: Failed to set SO_BROADCAST option. errno=%d\r\n", errno);
		// 실패해도 계속 진행 시도
	}

	// 주소 설정
	memset(&addr, 0, sizeof(addr));
	addr.sin_family = AF_INET;
	addr.sin_port = htons(FWU_UDP_PORT); // 장치 리스닝 포트
	addr.sin_addr.s_addr = htonl(INADDR_ANY); // 모든 IP에서 수신

	// 소켓 바인딩
	if (lwip_bind(g_udp_socket, (struct sockaddr*) &addr, sizeof(addr)) < 0)
	{
		DM("[FWU] Error: Cannot bind UDP socket to port %d. errno=%d\r\n", FWU_UDP_PORT, errno);
		lwip_close(g_udp_socket);
		g_udp_socket = -1;
		return -1;
	}

	// Non-blocking 모드 설정
	if (lwip_ioctl(g_udp_socket, FIONBIO, &opt) < 0)
	{
		DM("[FWU] Warning: Failed to set UDP socket non-blocking.\r\n");
	}

	DM("[FWU] UDP Listener/Sender socket started on port %d.\r\n", FWU_UDP_PORT);
	return 0; // 성공
}

/**
 * @brief UDP 데이터 수신 처리 (태스크 내에서 주기적으로 호출됨)
 */
static void handle_udp_receive(int sock)
{
	uint8_t buffer[256];
	struct sockaddr_in sender_addr;
	socklen_t sender_addr_len = sizeof(sender_addr);
	int len = lwip_recvfrom(sock, buffer, sizeof(buffer), 0, (struct sockaddr*) &sender_addr, &sender_addr_len);

	if (len > 0) // 데이터 수신 성공
	{
		char sender_ip_str[IP4ADDR_STRLEN_MAX];
		inet_ntoa_r(sender_addr.sin_addr, sender_ip_str, sizeof(sender_ip_str));
		DM("[FWU] [%s:%d] UDP Received %d bytes: %s\r\n", sender_ip_str, ntohs(sender_addr.sin_port), len, BitConverter_ToString(buffer, len));

		ProtocolInfo_t info;
		if (ParseFrame(buffer, (uint16_t)len, &info) == PROTOCOL_OK)
		{
			if (info.command.val == FWU_CMD_DEVICE_SEARCH && info.sub_command == FWU_SUB_DEVICE_SEARCH)
			{
				DM("[FWU]   -> Device Search Request. Responding...\r\n");
				sender_addr.sin_port = htons(FWU_PC_UDP_PORT);
				sender_addr.sin_addr.s_addr = (u32_t)IPADDR_BROADCAST;

				// 우선 자신(Transceiver)의 정보 전송
				send_device_search_response_udp(g_self_dev_info, sock, &sender_addr, info.sequence);

				// 연결된 Controller, CAN Device(Box, Unit) 정보 전송
				// 1. controller에 펌웨어 버전 응답 요청 전송, 응답은 uart 메시지 tcp로 relay
				icom_send_req_device_fw_info();
				// 2. 연결된 can 장비의 정보 udp로 전송
				if (g_hSysStatus.m_pStat->dev_stat.au.conn)
				{
					FWU_DeviceInfo_t au_dev_info;
					au_dev_info.DeviceParam.DeviceType = FWU_DEVICE_VHF;
					au_dev_info.DeviceParam.SubDeviceType = FWU_SUB_DEVICE_ALARM_UNIT;
					au_dev_info.DeviceParam.DeviceNumber = 0;
					au_dev_info.MajorVersion = g_hSysStatus.m_pStat->dev_stat.au.ver_maj;
					au_dev_info.MinorVersion = g_hSysStatus.m_pStat->dev_stat.au.ver_min;
					au_dev_info.PatchVersion = g_hSysStatus.m_pStat->dev_stat.au.ver_patch;
					send_device_search_response_udp(au_dev_info, sock, &sender_addr, info.sequence);
				}
				for (uint8_t i = 0; i < MAX_ALARM_BOX_CNT; i++)
				{
					if (g_hSysStatus.m_pStat->dev_stat.ab[i].conn)
					{
						FWU_DeviceInfo_t ab_dev_info;
						ab_dev_info.DeviceParam.DeviceType = FWU_DEVICE_VHF;
						ab_dev_info.DeviceParam.SubDeviceType = FWU_SUB_DEVICE_ALARM_BOX;
						ab_dev_info.DeviceParam.DeviceNumber = i;
						ab_dev_info.MajorVersion = g_hSysStatus.m_pStat->dev_stat.ab[i].ver_maj;
						ab_dev_info.MinorVersion = g_hSysStatus.m_pStat->dev_stat.ab[i].ver_min;
						ab_dev_info.PatchVersion = g_hSysStatus.m_pStat->dev_stat.ab[i].ver_patch;
						send_device_search_response_udp(ab_dev_info, sock, &sender_addr, info.sequence);
					}
				}
				// TODO:연결된 Remote Control Handset 정보 전송
			}
			else if (info.command.byte.L == FWU_CMD_MASTER_UPDATE_READY_REQ
				  && info.sub_command == FWU_SUB_MASTER_UPDATE_READY_REQ)
			{
				g_tgt_dev_param = info.command.byte.H;
				memcpy(&g_tgt_dev_param_t, &g_tgt_dev_param, sizeof(fwu_device_param_t));

				if (g_tgt_dev_param_t.DeviceType == FWU_DEVICE_NAVTEX)
				{
					if (g_fwu_state == FWU_STATE_IDLE)
					{
						if (info.payload_len >= 12) // IP+Subnet
						{
							memset(&g_pc_addr, 0, sizeof(g_pc_addr));
							g_pc_addr.sin_family = AF_INET;
							g_pc_addr.sin_port = htons(FWU_PC_TCP_SERVER_PORT);
							memcpy(&g_pc_addr.sin_addr, &info.payload[4], sizeof(struct in_addr));

							char pc_ip_str[IP4ADDR_STRLEN_MAX];
							inet_ntoa_r(g_pc_addr.sin_addr, pc_ip_str, sizeof(pc_ip_str));
							DM("[FWU]   -> Update Ready Request from PC: %s. Preparing TCP connection...\r\n", pc_ip_str);
							g_fwu_state = FWU_STATE_READY;
						}
						else
						{
							DM("[FWU]   -> Update Ready Request payload incomplete.\r\n");
						}
					}
					else
					{
						DM("[FWU]   -> Ignoring F0/01 request: Update already in progress (State: %d).\r\n", g_fwu_state);
					}

					// send update info
					g_up_info.up_major = info.payload[1];
					g_up_info.up_minor = info.payload[2];
					g_up_info.up_patch = info.payload[3];

					//g_up_info.device.DeviceParam = g_tgt_dev_param_t;
					g_up_info.device.DeviceParam = g_tgt_dev_param_t;

					switch (g_tgt_dev_param_t.SubDeviceType)
					{
					case FWU_SUB_DEVICE_TRANSCEIVER:
						g_up_info.device = g_self_dev_info;
						break;
					case FWU_SUB_DEVICE_ALARM_BOX:
						g_up_info.device.MajorVersion = g_hSysStatus.m_pStat->dev_stat.ab[g_up_info.device.DeviceParam.DeviceNumber].ver_maj;
						g_up_info.device.MinorVersion = g_hSysStatus.m_pStat->dev_stat.ab[g_up_info.device.DeviceParam.DeviceNumber].ver_min;
						g_up_info.device.PatchVersion = g_hSysStatus.m_pStat->dev_stat.ab[g_up_info.device.DeviceParam.DeviceNumber].ver_patch;
						break;
					case FWU_SUB_DEVICE_ALARM_UNIT:
						g_up_info.device.MajorVersion = g_hSysStatus.m_pStat->dev_stat.au.ver_maj;
						g_up_info.device.MinorVersion = g_hSysStatus.m_pStat->dev_stat.au.ver_min;
						g_up_info.device.PatchVersion = g_hSysStatus.m_pStat->dev_stat.au.ver_patch;
						break;
					case FWU_SUB_DEVICE_CONTROLLER:
					case FWU_SUB_DEVICE_REMOTE_HANDSET:
					default:
						break;
					}
					const_send_icom_fwu_cmd(FWU_CMD_UPDATE_STATUS, FWU_SUB_UPDATE_STATUS_START,
							(uint8_t*)&g_up_info, sizeof(g_up_info));
				}
				else
				{
					DM("[FWU]   -> Other Update Ready Request.\r\n");
					//DM("[FWU] dev type:%d sub:%d devnum:%d\r\n", g_tgt_dev_param_t.DeviceType, g_tgt_dev_param_t.SubDeviceType, g_tgt_dev_param_t.DeviceNumber);
				}

			}
			else
			{
				DM("[FWU]   -> Received unknown command over UDP: %04X/%04X\r\n", info.command.val, info.sub_command);
			}
		}
		else
		{
			DM("[FWU]   -> Invalid UDP frame received.\r\n");
		}
	}
	else if (len < 0)
	{
		if (errno != EWOULDBLOCK)
		{
			DM("[FWU] UDP recvfrom error: %d (%s)\r\n", errno, strerror(errno));
		}
	}
}

/**
 * @brief 장치 검색 UDP 응답 전송 (FA/00)
 */
static void send_device_search_response_udp(FWU_DeviceInfo_t deviceInfo, int sock, const struct sockaddr_in *dest_addr, uint16_t sequence)
{
	uint8_t frame_buf[128];

	struct netif *default_netif = netif_default;
	if (default_netif != NULL)
	{
		deviceInfo.IpAddress = ip4_addr_get_u32(netif_ip4_addr(default_netif));
		deviceInfo.SubnetMask = ip4_addr_get_u32(netif_ip4_netmask(default_netif));
	}
	else
	{
		deviceInfo.IpAddress = 0;
		deviceInfo.SubnetMask = 0;
	}

	uint8_t payload[sizeof(FWU_DeviceInfo_t)];
	memcpy(payload, &deviceInfo, sizeof(FWU_DeviceInfo_t));

	// --- 응답 프레임 생성 ---
	uint16_t frame_len = ConstructFrame(frame_buf, sizeof(frame_buf), FWU_CMD_DEVICE_SEARCH_ACK, FWU_SUB_DEVICE_SEARCH_ACK, payload, sizeof(payload), sequence);

	if (frame_len > 0)
	{
		send_udp_message(sock, dest_addr, frame_buf, frame_len);
	}
	else
	{
		DM("[FWU] Error constructing Device Search Response frame.\r\n");
	}
}

static inline void send_udp_message(int sock, const struct sockaddr_in *dest_addr, uint8_t* p_frame_buf, uint16_t frame_len)
{
	static uint8_t frame_buf[128]; // 응답 프레임 버퍼

	memcpy(frame_buf, p_frame_buf, frame_len);
	DM("[FWU] Prepared FA/00 Frame (len=%u): %s\r\n", frame_len, BitConverter_ToString(frame_buf, frame_len));

	// --- *** 데이터 캐시 클린 추가 *** ---
	// SCB_CleanDCache_by_Addr((uint32_t*)frame_buf, frame_len); // frame_len 길이만큼 클린
	// 또는 더 넓은 범위 (버퍼 전체) 클린:
	SCB_CleanDCache_by_Addr((uint32_t*) frame_buf, sizeof(frame_buf));

	int sent_len = lwip_sendto(sock, frame_buf, frame_len, 0, (struct sockaddr*) dest_addr, sizeof(struct sockaddr_in));
	if (sent_len < 0) // 오류 발생
	{
		DM("[FWU] Error sending Device Search Response: ret=%d, errno=%d (%s)\r\n", sent_len, errno, strerror(errno));
	}
	else if (sent_len != frame_len) // 부분 전송?
	{
		DM("[FWU] Warning: Sent partial Device Search Response? ret=%d, expected=%u\r\n", sent_len, frame_len);
	}
	else // 성공
	{
		char dest_ip_str[IP4ADDR_STRLEN_MAX];
		inet_ntoa_r(dest_addr->sin_addr, dest_ip_str, sizeof(dest_ip_str));
		// 목적지 포트는 dest_addr에 설정된 FWU_PC_UDP_PORT (18930) 이어야 함
		DM("[FWU] Sent Device Search Response (FA/00) to %s:%d\r\n", dest_ip_str, ntohs(dest_addr->sin_port));
	}
}

/**
 * @brief PC에 TCP 연결 시작
 */
static int start_tcp_connection(void)
{
	if (g_fwu_state != FWU_STATE_READY)
	{
		return 0;
	}
	if (g_tcp_socket != -1)
	{
		lwip_close(g_tcp_socket);
		g_tcp_socket = -1;
	}
	g_tcp_socket = lwip_socket(AF_INET, SOCK_STREAM, 0);
	if (g_tcp_socket < 0)
	{
		DM("[FWU] Error: Cannot create TCP socket.\r\n");
		g_fwu_state = FWU_STATE_ERROR;
		return -1;
	}
	int opt = 1;
	if (lwip_ioctl(g_tcp_socket, FIONBIO, &opt) < 0)
	{
		DM("[FWU] Warning: Failed to set TCP socket non-blocking.\r\n");
	}
	char pc_ip_str[IP4ADDR_STRLEN_MAX];
	inet_ntoa_r(g_pc_addr.sin_addr, pc_ip_str, sizeof(pc_ip_str));
	DM("[FWU] Attempting TCP connection to %s:%d...\r\n", pc_ip_str, ntohs(g_pc_addr.sin_port));
	int ret = lwip_connect(g_tcp_socket, (struct sockaddr*) &g_pc_addr, sizeof(g_pc_addr));
	if (ret == 0 || errno == EINPROGRESS)
	{
		DM("[FWU] TCP connection initiated.\r\n");
		g_fwu_state = FWU_STATE_CONNECTING;
		g_last_activity_tick = HAL_GetTick();
		return 0;
	}
	else
	{
		DM("[FWU] TCP connect initiation failed: %d (%s)\r\n", errno, strerror(errno));
		lwip_close(g_tcp_socket);
		g_tcp_socket = -1;
		g_fwu_state = FWU_STATE_ERROR;
		return -1;
	}
}

/**
 * @brief TCP 연결 상태 확인 및 데이터 처리
 */
static void handle_tcp_connection(int sock)
{
	int ret;
	fd_set read_fds;
	fd_set write_fds;
	fd_set error_fds;
	struct timeval tv;
	tv.tv_sec = 0;
	tv.tv_usec = 0;

	if (g_fwu_state == FWU_STATE_CONNECTING)
	{
		FD_ZERO(&write_fds);
		FD_SET(sock, &write_fds);
		FD_ZERO(&error_fds);
		FD_SET(sock, &error_fds);
		ret = lwip_select(sock + 1, NULL, &write_fds, &error_fds, &tv);
		if (ret > 0)
		{
			int opt_val;
			socklen_t opt_len = sizeof(opt_val);
			if (FD_ISSET(sock, &write_fds) || FD_ISSET(sock, &error_fds))
			{
				if (lwip_getsockopt(sock, SOL_SOCKET, SO_ERROR, &opt_val, &opt_len) == 0 && opt_val == 0)
				{
					DM("[FWU] TCP Connected to PC.\r\n");
					g_fwu_state = FWU_STATE_CONNECTED;
					g_last_activity_tick = HAL_GetTick();

					//업데이트 전송 요청 송신 (장비에 따라 구분)
					uint8_t frame_buf[32];
					uint16_t frame_len;
					if (g_self_dev_param == g_tgt_dev_param)
					{
						frame_len = ConstructFrame(frame_buf, sizeof(frame_buf),
													(g_tgt_dev_param << 8) | FWU_CMD_SLAVE_UPDATE_SEND_REQ,
													FWU_SUB_SLAVE_UPDATE_SEND_REQ,
													NULL, 0, 0);
						if (frame_len > 0)
						{
							ret = lwip_send(sock, frame_buf, frame_len, 0);
							DM("[FWU] Send F1/04 Frame (len=%u): %s\r\n", frame_len, BitConverter_ToString(frame_buf, frame_len));
							if (ret != frame_len)
							{
								DM("[FWU] TCP send error for F1/04: ret=%d, errno=%d\r\n", ret, errno);
								reset_update_state();
								return;
							}
							else
							{
								// --- SHA 컨텍스트 초기화 ---
								sha256_init_wrapper();
								DM("[FWU] Sent F1/04 (Update Send Req) to PC.\r\n");
								g_bytes_in_page_buffer = 0;
								g_expected_next_relative_addr = 0; // 상대 주소 기준
								g_current_page_write_addr = 0;
								g_total_bytes_written = 0;
								tcp_rx_buffer_len = 0;
								memset(g_flash_page_buffer, 0xFF, FWU_FLASH_PAGE_SIZE); /* g_inactive_bank_addr은 첫 청크 수신 시 설정 */
								DM("[FWU] Target Inactive Flash Bank CPU Address Space: 0x%08lX\r\n", FWU_INACTIVE_BANK_START_ADDR);
								DM("[FWU] Waiting for data...\r\n");
							}
						}
						else
						{
							DM("[FWU] Error constructing F1/04 frame.\r\n");
							reset_update_state();
							return;
						}
					}
					else if (g_tgt_dev_param_t.SubDeviceType == FWU_SUB_DEVICE_CONTROLLER)
					{
						// --- SHA 컨텍스트 초기화 ---
						sha256_init_wrapper();
						DM("[FWU] Start Controller FW Update.\r\n");

						if (!const_send_icom_fwu_cmd((g_tgt_dev_param << 8) | FWU_CMD_MASTER_UPDATE_READY_REQ,
								FWU_SUB_MASTER_UPDATE_READY_REQ, NULL, 0))
						{
							reset_update_state();
							return;
						}
					}
					else if (g_tgt_dev_param_t.SubDeviceType == FWU_SUB_DEVICE_ALARM_UNIT
            				|| g_tgt_dev_param_t.SubDeviceType == FWU_SUB_DEVICE_ALARM_BOX)
					{
						// --- SHA 컨텍스트 초기화 ---
						sha256_init_wrapper();
						char str_dev[10];
						if (g_tgt_dev_param_t.SubDeviceType == FWU_SUB_DEVICE_ALARM_UNIT)
							sprintf(str_dev, "Unit");
						else
							sprintf(str_dev, "Box %i", g_tgt_dev_param_t.DeviceNumber + 1);
						DM("[FWU] Start Alarm %s FW Update.\r\n", str_dev);
						// 업데이트 전송 요청 메시지 바로 전송
						send_tcp_ack(g_tcp_socket, FWU_CMD_SLAVE_UPDATE_SEND_REQ, FWU_SUB_SLAVE_UPDATE_SEND_REQ, 0);
					}
				}
				else
				{
					DM("[FWU] TCP connect failed (SO_ERROR: %d - %s).\r\n", opt_val, strerror(opt_val));
					reset_update_state();
					return;
				}
			}
		}
		else if (ret < 0)
		{
			DM("[FWU] TCP select error during connect: %d (%s)\r\n", errno, strerror(errno));
			reset_update_state();
			return;
		}
		if (HAL_GetTick() - g_last_activity_tick > FWU_DEFAULT_TCP_TIMEOUT_MS)
		{
			DM("[FWU] TCP connection attempt timed out.\r\n");
			reset_update_state();
			return;
		}
	}
	else if (g_fwu_state == FWU_STATE_CONNECTED
			|| g_fwu_state == FWU_STATE_SELF_RECEIVING
			|| g_fwu_state == FWU_STATE_UART_TGT_RECEIVING
			|| g_fwu_state == FWU_STATE_CAN_TGT_RECEIVING
			|| g_fwu_state == FWU_STATE_VERIFYING /* 검증 중에도 오류 수신 가능 */)
	{
		FD_ZERO(&read_fds);
		FD_SET(sock, &read_fds);
		FD_ZERO(&error_fds);
		FD_SET(sock, &error_fds);
		ret = lwip_select(sock + 1, &read_fds, NULL, &error_fds, &tv);
		if (ret > 0)
		{
			if (FD_ISSET(sock, &read_fds))
			{
				uint8_t tcp_send_buf[1500];
				int len = lwip_recv(sock, tcp_send_buf, sizeof(tcp_send_buf), 0);
				if (len > 0)
				{
					process_tcp_data(tcp_send_buf, len);
					g_last_activity_tick = HAL_GetTick();
				}
				else if (len == 0)
				{
					DM("[FWU] TCP Connection closed by PC during data transfer.\r\n");
					reset_update_state();
					return;
				}
				else
				{
					if (errno != EWOULDBLOCK)
					{
						DM("[FWU] TCP recv error: %d (%s)\r\n", errno, strerror(errno));
						reset_update_state();
						return;
					}
				}
			}
			else if (FD_ISSET(sock, &error_fds))
			{
				int opt_val;
				socklen_t opt_len = sizeof(opt_val);
				lwip_getsockopt(sock, SOL_SOCKET, SO_ERROR, &opt_val, &opt_len);
				DM("[FWU] TCP socket error detected: %d (%s)\r\n", opt_val, strerror(opt_val));
				reset_update_state();
				return;
			}
		}
		else if (ret < 0)
		{
			DM("[FWU] TCP select error during receive: %d (%s)\r\n", errno, strerror(errno));
			reset_update_state();
			return;
		}
		if (HAL_GetTick() - g_last_activity_tick > (FWU_DEFAULT_TCP_TIMEOUT_MS * g_timeout_scale))
		{
			DM("[FWU] TCP connection timeout due to inactivity.\r\n");
			send_tcp_error(sock, 0xffffff01, 0);
			reset_update_state();
			return;
		}
	}
	else if (g_fwu_state == FWU_STATE_WRITE_PAGE)
	{
		/* 플래시 쓰기 중 대기 */
		osDelay(50);
	}
}
/**
 * @brief 수신된 TCP 데이터 처리 및 프레임 파싱 (수신 버퍼 사용)
 */
static void process_tcp_data(uint8_t *data, int len)
{
    // 수신 버퍼에 데이터 추가
    if (tcp_rx_buffer_len + len > sizeof(tcp_rx_buffer))
    {
        DM("[FWU] ERROR: TCP RX Buffer Overflow! Flushing buffer.\r\n");
        tcp_rx_buffer_len = 0;
        reset_update_state();
        return;
    }
    memcpy(tcp_rx_buffer + tcp_rx_buffer_len, data, len);
    tcp_rx_buffer_len += len;

    // 버퍼에서 완전한 프레임 파싱 시도 루프
    ProtocolInfo_t parsed_info;
    uint16_t processed_len_total = 0;

    while (tcp_rx_buffer_len - processed_len_total >= PROTOCOL_MIN_FRAME_SIZE)
    {
        ProtocolStatus_t parse_status = ParseFrame(tcp_rx_buffer + processed_len_total,
                                                 tcp_rx_buffer_len - processed_len_total,
                                                 &parsed_info);

        if (parse_status == PROTOCOL_OK) // 완전한 프레임 파싱 성공
        {
            g_last_received_tcp_seq = parsed_info.sequence; // ACK 생성 위해 시퀀스 저장

            // 파싱된 프레임 길이 계산 (패딩 포함)
            //uint16_t frameLengthValue = (uint16_t)(3 + parsed_info.payload_len);
            uint16_t paddingLen = (4 - (parsed_info.payload_len % 4)) % 4;
            uint16_t paddedPayloadLen = parsed_info.payload_len + paddingLen;
            uint16_t totalParsedFrameLen = PROTOCOL_MIN_FRAME_SIZE + paddedPayloadLen;

            if (tcp_rx_buffer_len - processed_len_total < totalParsedFrameLen)
            {
                 DM("[FWU] DEBUG: Frame parsed OK, but full padded frame not yet in buffer. Waiting...\r\n");
                 break; // 데이터 더 필요
            }
            processed_len_total += totalParsedFrameLen; // 처리된 길이 누적

            // --- *** 시퀀스 번호 검증 로직 *** ---
            int result = -1; // 기본값 오류
            uint8_t is_data_cmd = (parsed_info.command.byte.L == FWU_CMD_MASTER_BINARY_CHUNK
            		            || parsed_info.command.byte.L == FWU_CMD_MASTER_BINARY_COMPLETE);

            if (is_data_cmd) // 데이터 관련 명령일 때만 검증
            {
                if (g_sequence_initialized) // 시퀀스 추적 중
                {
                    if (parsed_info.sequence != g_expected_pc_sequence) // 예상과 다르면 오류
                    {
                        DM("[FWU]   -> ERROR: Sequence number mismatch! Expected %u, Got %u.\r\n", g_expected_pc_sequence, parsed_info.sequence);
                        send_tcp_error(g_tcp_socket, 0xFFFFFF05, parsed_info.sequence); // Sequence Error
                        result = -1; // 오류 처리
                    }
                    else // 정상 시퀀스
                    {
                         // 처리가 성공하면 g_expected_pc_sequence 증가 (아래에서)
                         result = 0; // 일단 정상으로 간주
                    }
                }
                else // 첫 데이터 명령 수신
                {
                    g_expected_pc_sequence = parsed_info.sequence; // 현재 시퀀스를 기준으로 설정
                    g_sequence_initialized = 1; // 추적 시작
                    //DM("[FWU] DEBUG: Initial PC sequence %u received. Expecting %u next.\r\n", g_expected_pc_sequence, (uint16_t)(g_expected_pc_sequence + 1));
                    result = 0; // 첫 프레임은 정상으로 간주
                }
            }
            else if(parsed_info.command.val == FWU_CMD_ERROR && parsed_info.sub_command == FWU_SUB_ERROR)
            {
                 // 오류 프레임은 시퀀스 검사 없이 처리
                 result = 0; // 처리 함수 호출하도록 성공으로 설정
            }
            else
            {
                 // 기타 명령은 시퀀스 검사 안함 (필요시 추가)
                 result = 0;
            }

            // --- 시퀀스 검증 통과 시 프레임 처리 ---
            if (result == 0) // 시퀀스 정상이거나 검사 대상 아님
            {
            	if (parsed_info.command.byte.L == FWU_CMD_MASTER_BASE)
            	{
					fwu_device_param_t dev_param_t;
    				dev_param_t.val = parsed_info.command.byte.H;
    				
            		if(dev_param_t.val == g_self_dev_param)
            		{
            			if (parsed_info.sub_command == FWU_SUB_MASTER_BINARY_CHUNK)
            			{
            				result = handle_binary_chunk_frame(&parsed_info);
            			}
            			else if (parsed_info.sub_command == FWU_SUB_MASTER_BINARY_COMPLETE)
            			{
            				result = handle_binary_complete_frame(&parsed_info);
            				// 완료 명령 처리 후에는 루프 종료해야 함
            				break;
            			}
            		}
            		else if (dev_param_t.SubDeviceType == FWU_SUB_DEVICE_CONTROLLER)
            		{
            			if (parsed_info.sub_command == FWU_SUB_MASTER_BINARY_CHUNK)
            			{
#if FWU_UART_RELAY_SIZE != 1024
            				result = handle_proxy_binary_chunk_frame(&parsed_info);
#else
            				//수신받은 데이터 그대로 uart 전달
            				g_fwu_state = FWU_STATE_UART_TGT_WAIT_ACK; // 데이터 수신 중 상태
            				//DM("[FWU] Devided Payload %d : %s\r\n", i, BitConverter_ToString(frame_buf, frame_len));
            				DM("[FWU]   -> Relay Chunk Data (RelAddr=0x%lX)\r\n", *(uint32_t*)&parsed_info.payload[0]);
            				icom_send_fwu_cmd(data, len);
            				//DM("[FWU] Sended CRC : 0x%04X\r\n",(ushort)(data[parsed_info.payload_len + 1] | (data[parsed_info.payload_len + 2] << 8)));
            				g_last_activity_tick = HAL_GetTick();
            				uint8_t attempted_retry = 0;
            				while (g_fwu_state == FWU_STATE_UART_TGT_WAIT_ACK)
            				{
            					if (HAL_GetTick() - g_last_activity_tick > FWU_DEFAULT_UART_RETRY_MS)
            					{
            						attempted_retry++;

                    				DM("[FWU] DEBUG 794: %s\r\n", BitConverter_ToString(data, len));
                    				icom_send_fwu_cmd(data, len);
                    				DM("[FWU] Retry Sended CRC : 0x%04X\r\n",(ushort)(data[parsed_info.payload_len + 1] | (data[parsed_info.payload_len + 2] << 8)));
            						g_last_activity_tick = HAL_GetTick();
            					}
            					if (attempted_retry > FWU_MAX_RETRY_COUNT)
            					{
            						DM("[FWU] UART Proxy Retry timeout.\r\n");
            						reset_update_state();
            						return;
            					}
            					osDelay(50);
            				}
            				result = 0;
#endif
            			}
            			else if (parsed_info.sub_command == FWU_SUB_MASTER_BINARY_COMPLETE)
            			{
            				result = handle_proxy_binary_complete_frame(&parsed_info);
            				// 완료 명령 처리 후에는 루프 종료해야 함
            				break;
            			}
            		}
            		else if (dev_param_t.SubDeviceType == FWU_SUB_DEVICE_ALARM_UNIT
            				|| dev_param_t.SubDeviceType == FWU_SUB_DEVICE_ALARM_BOX)
            		{
            			if (parsed_info.sub_command == FWU_SUB_MASTER_BINARY_CHUNK)
            			{
            				result = handle_can_binary_chunk_frame(&parsed_info);
            			}
            			else if (parsed_info.sub_command == FWU_SUB_MASTER_BINARY_COMPLETE)
            			{
            				result = handle_can_binary_complete_frame(&parsed_info);
            				// 완료 명령 처리 후에는 루프 종료해야 함
            				break;
            			}
            		}
            	}
                else if (parsed_info.command.val == FWU_CMD_ERROR
                		&& parsed_info.sub_command == FWU_SUB_ERROR)
                {
                     result = handle_error_frame(&parsed_info);
                     break; // 오류 수신 후 루프 종료
                }
                else if (parsed_info.command.byte.L == FWU_CMD_UPDATE_STATUS)
                {
    				DM("[FWU] DEBUG 840: %s\r\n", BitConverter_ToString(data, len));
    				icom_send_fwu_cmd(data, len);
                }
                else
                {
                    DM("[FWU] Received unknown TCP command: %04X/%04X\r\n", parsed_info.command.val, parsed_info.sub_command);
                    send_tcp_error(g_tcp_socket, 0xFFFFFF06, parsed_info.sequence); // Command Error
                    result = -1; // 오류
                }
            }

            // --- 결과 처리 ---
            if (result == 0) // 프레임 처리 성공 시
            {
                 // 시퀀스 검사 대상 명령이었고 성공했다면 다음 예상 시퀀스 증가
                 if (is_data_cmd && g_sequence_initialized)
                 {
                     g_expected_pc_sequence++;
                 }
            }
            else // 처리 중 오류 발생 시
            {
                if (g_fwu_state != FWU_STATE_ERROR
                		&& g_fwu_state != FWU_STATE_WRITE_ERROR
						&& g_fwu_state != FWU_STATE_VERIFY_FAILED)
                {
                    reset_update_state(); // 상태 리셋
                }
                return; // 함수 종료
            }
        }
        else if (parse_status == PROTOCOL_ERROR_INCOMPLETE)
        {
            break; // 데이터 더 필요
        }
        else // 복구 불가능한 파싱 오류
        {
        	DM("[FWU] TCP Frame Parse Error: %d.\r\n", parse_status);
        	//tcp_rx_buffer_len = 0;
        	//processed_len_total = 0;
        	send_tcp_error(g_tcp_socket, 0xFFFFFFFF, 0); // 일반 오류
        	reset_update_state();
        	return;
        }
    } // end while

    // 처리된 데이터만큼 버퍼 정리
    if (processed_len_total > 0)
    {
        if (processed_len_total < tcp_rx_buffer_len)
        {
            memmove(tcp_rx_buffer, tcp_rx_buffer + processed_len_total, tcp_rx_buffer_len - processed_len_total);
        }
        tcp_rx_buffer_len -= processed_len_total;
    }
    else if (processed_len_total > tcp_rx_buffer_len)
    {
         DM("[FWU] ERROR: processed_len > tcp_rx_buffer_len!\r\n");
         tcp_rx_buffer_len = 0;
    }
}

/**
 * @brief 수신된 바이너리 청크 프레임 처리 (F0/04) - 상대 주소 사용
 */
static int handle_binary_chunk_frame(ProtocolInfo_t *pInfo)
{
	// 페이로드 유효성 검사 (최소 상대 주소 4바이트)
	if (pInfo->payload_len < 4)
	{
		DM("[FWU]   -> ERROR: Binary chunk payload too short (missing relative address).\r\n");
		send_tcp_error(g_tcp_socket, 0xFFFFFF04, pInfo->sequence); // Frame Length Error
		return -1;
	}

	// --- *** 상대 주소 읽기 *** ---
	uint32_t chunk_relative_address = 0;
	memcpy(&chunk_relative_address, pInfo->payload, sizeof(uint32_t)); // 상대 주소 추출
	uint8_t *chunk_data = pInfo->payload + sizeof(uint32_t);
	uint16_t chunk_data_len = pInfo->payload_len - sizeof(uint32_t);

	// --- 상태 확인 ---
	if (g_fwu_state != FWU_STATE_CONNECTED && g_fwu_state != FWU_STATE_SELF_RECEIVING)
	{
		DM("[FWU]   -> ERROR: Received binary chunk in unexpected state (%d).\r\n", g_fwu_state);
		send_tcp_error(g_tcp_socket, 0xFFFFFFFF, pInfo->sequence); // 일반 오류
		return -1;
	}

	g_fwu_state = FWU_STATE_SELF_RECEIVING; // 데이터 수신 중 상태

	// --- *** 실제 기록될 절대 주소 계산 *** ---
	uint32_t target_flash_address = FWU_INACTIVE_BANK_START_ADDR + chunk_relative_address;

	// --- 주소 유효성 및 첫 청크 처리 ---
	if (g_total_bytes_written == 0 && g_bytes_in_page_buffer == 0) // 첫 데이터 수신
	{
		// *** 첫 청크 주소 유효성/정렬 검사 (계산된 절대 주소 기준) ***
		if (!FLASH_IF_IsAddressValid(target_flash_address, chunk_data_len)) // 수정됨
		{
			DM("[FWU]   -> ERROR: First chunk target address/length invalid! Addr=0x%lX\r\n", target_flash_address);
			send_tcp_error(g_tcp_socket, 0xFFFFFF12, pInfo->sequence); // Address Error
			return -1;
		}

		// *** 예상 다음 주소를 상대 주소 기준으로 설정 ***
		g_expected_next_relative_addr = chunk_relative_address;
		// *** 현재 페이지(섹터) 시작 주소 계산 (절대 주소 기준) ***
		g_current_page_write_addr = target_flash_address & ~(FWU_FLASH_PAGE_SIZE - 1);
		g_bytes_in_page_buffer = 0; // 버퍼 초기화
		memset(g_flash_page_buffer, 0xFF, FWU_FLASH_PAGE_SIZE);

		// 첫 섹터 지우기 (절대 주소 기준)
		DM("[FWU]   -> First chunk received (RelAddr=0x%lX). Erasing target sector 0x%08lX...\r\n", chunk_relative_address, g_current_page_write_addr);
		g_flash_op_pending = 1;
		FLASH_StatusTypeDef erase_status = FLASH_IF_EraseSector(g_current_page_write_addr);
		g_flash_op_pending = 0;
		if (erase_status != FLASH_IF_OK)
		{
			DM("[FWU]   -> ERROR: Failed to erase initial flash sector!\r\n");
			send_tcp_error(g_tcp_socket, 0xFFFFFF21, pInfo->sequence);
			return -1;
		}
		DM("[FWU]      Initial sector erased. Expecting RelAddr=0x%lX\r\n", g_expected_next_relative_addr);
	}
	// *** 주소 연속성 검사 (상대 주소 기준) ***
	else if (chunk_relative_address != g_expected_next_relative_addr)
	{
		DM("[FWU]   -> ERROR: Address discontinuity! Expected RelAddr=0x%lX, Got RelAddr=0x%lX.\r\n", g_expected_next_relative_addr, chunk_relative_address);
		send_tcp_error(g_tcp_socket, 0xFFFFFF12, pInfo->sequence); // Address Error
		return -1;
	}

	// --- SHA-256 업데이트 (데이터 버퍼링 전에 수행) ---
	if (chunk_data_len > 0)
	{
		sha256_update_wrapper(chunk_data, chunk_data_len);
	}

	// --- 데이터 버퍼링 및 플래시 쓰기 ---
	uint16_t chunk_offset = 0;
	while (chunk_offset < chunk_data_len)
	{
		// *** 페이지(섹터) 변경 확인 및 지우기 (절대 주소 기준) ***
		uint32_t current_data_absolute_addr = FWU_INACTIVE_BANK_START_ADDR + g_expected_next_relative_addr; // 현재 쓸 절대 주소
		uint32_t current_data_page_addr = current_data_absolute_addr & ~(FWU_FLASH_PAGE_SIZE - 1);
		if (current_data_page_addr != g_current_page_write_addr) // 페이지(섹터) 변경
		{
			// 현재 버퍼 데이터 쓰기
			if (g_bytes_in_page_buffer > 0)
			{
				DM("[FWU]   -> Crossing page boundary. Writing current page (AbsAddr=0x%08lX)...\r\n", g_current_page_write_addr);
				g_fwu_state = FWU_STATE_WRITE_PAGE;
				g_flash_op_pending = 1;
				FLASH_StatusTypeDef ws = FLASH_IF_WriteData(g_current_page_write_addr, g_flash_page_buffer, g_bytes_in_page_buffer);
				g_flash_op_pending = 0;
				g_fwu_state = FWU_STATE_SELF_RECEIVING;
				if (ws != FLASH_IF_OK)
				{
					/* 오류 처리 */
					return -1;
				}
				g_total_bytes_written += g_bytes_in_page_buffer;
				DM("[FWU]   -> Flash page write complete. Total written: %lu bytes.\r\n", g_total_bytes_written);
			}
			// 새 페이지(섹터) 준비
			g_current_page_write_addr = current_data_page_addr;
			g_bytes_in_page_buffer = 0;
			memset(g_flash_page_buffer, 0xFF, FWU_FLASH_PAGE_SIZE);
			if (g_current_page_write_addr < (FWU_INACTIVE_BANK_START_ADDR + FLASH_BANK_SIZE)) // 뱅크 범위 내
			{
				DM("[FWU]   -> Erasing next flash page/sector (AbsAddr=0x%08lX)...\r\n", g_current_page_write_addr);
				g_flash_op_pending = 1;
				FLASH_StatusTypeDef es = FLASH_IF_EraseSector(g_current_page_write_addr);
				g_flash_op_pending = 0;
				if (es != FLASH_IF_OK)
				{
					/* 오류 처리 */
					return -1;
				}
			}
			else
			{
				DM("[FWU]   -> ERROR: Address out of inactive bank range during erase!\r\n");
				send_tcp_error(g_tcp_socket, 0xFFFFFF12, pInfo->sequence);
				return -1;
			}
		}

		// 버퍼링 로직
		uint32_t remaining_page_buffer_space = FWU_FLASH_PAGE_SIZE - g_bytes_in_page_buffer;
		uint32_t bytes_to_buffer_now = MIN(chunk_data_len - chunk_offset, remaining_page_buffer_space);
		if (bytes_to_buffer_now > 0)
		{
			// *** 버퍼 오프셋 계산 (상대 주소 기준) ***
			uint32_t buffer_offset = g_expected_next_relative_addr - (g_current_page_write_addr - FWU_INACTIVE_BANK_START_ADDR);
			if (buffer_offset >= FWU_FLASH_PAGE_SIZE || buffer_offset + bytes_to_buffer_now > FWU_FLASH_PAGE_SIZE)
			{
				DM("[FWU]   -> ERROR: Internal buffer offset calculation error or overflow!\r\n");
				send_tcp_error(g_tcp_socket, 0xFFFFFFFF, pInfo->sequence);
				return -1;
			}

			memcpy(g_flash_page_buffer + buffer_offset, chunk_data + chunk_offset, bytes_to_buffer_now);
			g_bytes_in_page_buffer += bytes_to_buffer_now;
			g_expected_next_relative_addr += bytes_to_buffer_now; // *** 예상 다음 상대 주소 업데이트 ***
			chunk_offset += bytes_to_buffer_now;
			DM("[FWU]   -> Buffered %lu bytes. Total in buffer: %lu / %d\r\n", bytes_to_buffer_now, g_bytes_in_page_buffer, FWU_FLASH_PAGE_SIZE);
		}
		else
		{
			/* 복사할 데이터 없음 */
		}

		// 버퍼가 꽉 찼으면 플래시에 쓰기 (로직 동일)
		if (g_bytes_in_page_buffer == FWU_FLASH_PAGE_SIZE)
		{
			/* ... 플래시 쓰기 및 다음 페이지 지우기 로직 ... */
			DM("[FWU]   -> Flash page buffer full. Writing current page (AbsAddr=0x%08lX)...\r\n", g_current_page_write_addr);
			g_fwu_state = FWU_STATE_WRITE_PAGE;
			g_flash_op_pending = 1;
			FLASH_StatusTypeDef ws = FLASH_IF_WriteData(g_current_page_write_addr, g_flash_page_buffer, g_bytes_in_page_buffer);
			g_flash_op_pending = 0;
			g_fwu_state = FWU_STATE_SELF_RECEIVING;
			if (ws != FLASH_IF_OK)
			{
				DM("[FWU]   -> ERROR: Failed to write flash page!\r\n");
				g_fwu_state = FWU_STATE_WRITE_ERROR;
				send_tcp_error(g_tcp_socket, 0xFFFFFF21, pInfo->sequence);
				return -1;
			}
			g_total_bytes_written += g_bytes_in_page_buffer;
			DM("[FWU]   -> Flash page write complete. Total written: %lu bytes.\r\n", g_total_bytes_written);
			g_current_page_write_addr += FWU_FLASH_PAGE_SIZE;
			g_bytes_in_page_buffer = 0;
			memset(g_flash_page_buffer, 0xFF, FWU_FLASH_PAGE_SIZE);
			if (g_current_page_write_addr < (FWU_INACTIVE_BANK_START_ADDR + FLASH_BANK_SIZE))
			{
				DM("[FWU]   -> Erasing next flash page/sector (AbsAddr=0x%08lX)...\r\n", g_current_page_write_addr);
				g_flash_op_pending = 1;
				FLASH_StatusTypeDef es = FLASH_IF_EraseSector(g_current_page_write_addr);
				g_flash_op_pending = 0;
				if (es != FLASH_IF_OK)
				{
					DM("[FWU]   -> ERROR: Failed to erase next flash sector!\r\n");
					g_fwu_state = FWU_STATE_WRITE_ERROR;
					send_tcp_error(g_tcp_socket, 0xFFFFFF21, pInfo->sequence);
					return -1;
				}
			}
			else
			{
				DM("[FWU]   -> Reached end of inactive bank.\r\n");
			}
		}
	} // end while (chunk_offset < chunk_data_len)

	// 성공 ACK (F1/05) 전송
	send_tcp_ack(g_tcp_socket, FWU_CMD_SLAVE_BINARY_CHUNK_ACK, FWU_SUB_SLAVE_BINARY_CHUNK_ACK, pInfo->sequence);
	return 0; // 성공
}

/**
 * @brief 수신된 바이너리 완료 프레임 처리 (F0/05) - SHA 검증 추가
 */
static int handle_binary_complete_frame(ProtocolInfo_t *pInfo)
{
	// 상태 전환, SHA 검증, ACK/Error 전송
	DM("[FWU]   -> Binary Transfer Complete message received.\r\n");
	if (g_fwu_state == FWU_STATE_ERROR || g_fwu_state == FWU_STATE_WRITE_ERROR)
	{
		const_send_icom_fwu_cmd(FWU_CMD_UPDATE_STATUS, FWU_SUB_UPDATE_ERROR, (uint8_t*)&g_up_info, sizeof(g_up_info));

		DM("[FWU]      Ignoring complete message due to previous error.\r\n");
		reset_update_state();
		return 0;
	}
	uint8_t entire_hash[SHA256_HASH_SIZE];
	if (pInfo->payload_len < SHA256_HASH_SIZE)
	{
		const_send_icom_fwu_cmd(FWU_CMD_UPDATE_STATUS, FWU_SUB_UPDATE_ERROR, (uint8_t*)&g_up_info, sizeof(g_up_info));

		DM("[FWU]   -> ERROR: Completion payload too short.\r\n");
		send_tcp_error(g_tcp_socket, 0xFFFFFF04, pInfo->sequence);
		reset_update_state();
		return -1;
	}
	memcpy(entire_hash, pInfo->payload, SHA256_HASH_SIZE);
	DM("[FWU]   -> Expected SHA-256: %s\r\n", BitConverter_ToString(entire_hash, SHA256_HASH_SIZE));
	g_fwu_state = FWU_STATE_COMPLETE;
	if (g_bytes_in_page_buffer > 0)
	{
		DM("[FWU]   -> Writing remaining %lu bytes...\r\n", g_bytes_in_page_buffer);
		g_fwu_state = FWU_STATE_WRITE_PAGE;
		g_flash_op_pending = 1;
		FLASH_StatusTypeDef ws = FLASH_IF_WriteData(g_current_page_write_addr, g_flash_page_buffer, g_bytes_in_page_buffer);
		g_flash_op_pending = 0;
		if (ws != FLASH_IF_OK)
		{
			DM("[FWU]   -> ERROR: Failed final write!\r\n");
			g_fwu_state = FWU_STATE_WRITE_ERROR;
			send_tcp_error(g_tcp_socket, 0xFFFFFF21, pInfo->sequence);
			return -1;
		}
		g_total_bytes_written += g_bytes_in_page_buffer;
		DM("[FWU]   -> Final write complete. Total: %lu bytes.\r\n", g_total_bytes_written);
	}
	g_fwu_state = FWU_STATE_VERIFYING;
	DM("[FWU]   -> Calculating final SHA-256...\r\n");
	sha256_final_wrapper(g_calculated_hash);
	DM("[FWU]   -> Calculated SHA-256: %s\r\n", BitConverter_ToString(g_calculated_hash, SHA256_HASH_SIZE));
	if (memcmp(entire_hash, g_calculated_hash, SHA256_HASH_SIZE) != 0)
	{
		DM("[FWU]   -> ERROR: SHA-256 verification FAILED!\r\n");
		g_fwu_state = FWU_STATE_VERIFY_FAILED;
		send_tcp_error(g_tcp_socket, 0xFFFFFF02, pInfo->sequence);
		reset_update_state();
		return -1;
	}
	DM("[FWU]   -> SHA-256 verification successful.\r\n");
	g_fwu_state = FWU_STATE_COMPLETE;
	send_tcp_ack(g_tcp_socket, FWU_CMD_SLAVE_BINARY_COMPLETE_ACK, FWU_SUB_SLAVE_BINARY_COMPLETE_ACK, pInfo->sequence);
	const_send_icom_fwu_cmd(FWU_CMD_UPDATE_STATUS, FWU_SUB_UPDATE_STATUS_FINISH, (uint8_t*)&g_up_info, sizeof(g_up_info));
	g_fwu_state = FWU_STATE_READY_TO_SWAP;
	DM("[FWU] Ready to swap bank and reboot.\r\n");
	return 0;
}


#if FWU_UART_RELAY_SIZE != 1024
static int handle_proxy_binary_chunk_frame(ProtocolInfo_t *pInfo)
{
	static uint16_t proxy_seq = 0;
	// 페이로드 유효성 검사 (최소 상대 주소 4바이트)
	if (pInfo->payload_len < 4)
	{
		DM("[FWU]   -> ERROR: Binary chunk payload too short (missing relative address).\r\n");
		send_tcp_error(g_tcp_socket, 0xFFFFFF04, pInfo->sequence); // Frame Length Error
		return -1;
	}

	// --- 상태 확인 ---
	if (g_fwu_state != FWU_STATE_CONNECTED && g_fwu_state != FWU_STATE_UART_TGT_RECEIVING)
	{
		DM("[FWU]   -> ERROR: Received binary chunk in unexpected state (%d).\r\n", g_fwu_state);
		send_tcp_error(g_tcp_socket, 0xFFFFFFFF, pInfo->sequence); // 일반 오류
		return -1;
	}

	g_fwu_state = FWU_STATE_UART_TGT_RECEIVING; // 데이터 수신 중 상태

	// --- 수신한 chunk 데이터를 FWU_UART_RELAY_SIZE 단위로 나누어 전송 ---
	// chunk 확인 및 sha-256 계산, 오류 발생시 코드 전송 등은 모두 Target board에서 진행, Trasceiver는 단순 chunk 축소하여 relay 하는 역할만 진행

	// 수신한 chunk 데이터 (상대주소 4바이트 + payload 1024 바이트)를 FWU_UART_RELAY_SIZE(128) 단위로 나누어 uart 전송 및 uart ack 대기 후 다음 chunk 전송 요청
	uint8_t resize_chunk_count = (pInfo->payload_len - 4/*상대주소 제외*/) / FWU_UART_RELAY_SIZE;
	uint8_t resize_chunk_left_len = (pInfo->payload_len - 4/*상대주소 제외*/) % FWU_UART_RELAY_SIZE;
	if (resize_chunk_count > 0)
	{
		// 나눠진 프로토콜 파일 생성
		ProtocolInfo_t resized_p_info[resize_chunk_count];
		uint32_t base_relAddr = *(uint32_t*)pInfo->payload;
		uint8_t frame_buf[PROTOCOL_MIN_FRAME_SIZE + FWU_UART_RELAY_SIZE + 4];
		uint16_t payload_size = FWU_UART_RELAY_SIZE;
		for(int i = 0; i <= resize_chunk_count; i++)
		{
			if (i == resize_chunk_count)
			{
				if (resize_chunk_left_len > 0)
					payload_size = resize_chunk_left_len;
				else
					break;
			}

			//payload와 payload_len 제외하고 복사
			memcpy(&resized_p_info[i], pInfo, PROTOCOL_MIN_LENGTH_VALUE);
			//payload 복사 및 상대주소 할당
			memcpy((uint8_t*)&uart_rx_buffer[i * (FWU_UART_RELAY_SIZE + 4)], &base_relAddr, sizeof(base_relAddr));
			memcpy(((uint8_t*)&uart_rx_buffer[i * (FWU_UART_RELAY_SIZE + 4)]) + sizeof(base_relAddr), (pInfo->payload + 4 + (i * FWU_UART_RELAY_SIZE)), payload_size);
			base_relAddr += FWU_UART_RELAY_SIZE;
			resized_p_info[i].payload = &uart_rx_buffer[i * (FWU_UART_RELAY_SIZE + 4)];
			resized_p_info[i].payload_len = payload_size + 4;

			// 프레임 uart 전송 및 응답 대기
			uint16_t frame_len = ConstructFrame(frame_buf, sizeof(frame_buf), resized_p_info[i].command.val, resized_p_info[i].sub_command,
					resized_p_info[i].payload, resized_p_info[i].payload_len, proxy_seq++);
			if (proxy_seq >= 0xFFFF)
				proxy_seq = 0;
			//DM("[FWU] Proxy Binary Chunk RelAdd=0x%1X\r\n", base_relAddr);
			g_fwu_state = FWU_STATE_UART_TGT_WAIT_ACK; // 데이터 수신 중 상태
			//DM("[FWU] Devided Payload %d : %s\r\n", i, BitConverter_ToString(frame_buf, frame_len));
			icom_send_fwu_cmd(frame_buf, frame_len);
			g_last_activity_tick = HAL_GetTick();
			uint8_t attempted_retry = 0;
			while (g_fwu_state == FWU_STATE_UART_TGT_WAIT_ACK)
			{
				if (HAL_GetTick() - g_last_activity_tick > FWU_DEFAULT_UART_RETRY_MS)
				{
					attempted_retry++;
					icom_send_fwu_cmd(frame_buf, frame_len);
					g_last_activity_tick = HAL_GetTick();
				}
				if (attempted_retry > FWU_MAX_RETRY_COUNT)
				{
					DM("[FWU] UART Proxy Retry timeout.\r\n");
					reset_update_state();
				}
				osDelay(50);
			}
		}
	}
	return 0; // 성공
}
#endif

static int handle_proxy_binary_complete_frame(ProtocolInfo_t *pInfo)
{
	// 상태 전환, SHA 검증, ACK/Error 전송
	DM("[FWU]   -> Binary Transfer Complete message received.\r\n");
	if (g_fwu_state == FWU_STATE_ERROR || g_fwu_state == FWU_STATE_WRITE_ERROR)
	{
		DM("[FWU]      Ignoring complete message due to previous error.\r\n");
		reset_update_state();
		return 0;
	}
	g_fwu_state = FWU_STATE_VERIFYING;
	const_send_icom_fwu_cmd(pInfo->command.val, pInfo->sub_command, pInfo->payload, pInfo->payload_len);
	g_last_activity_tick = HAL_GetTick();
	uint8_t attempted_retry = 0;
	while (g_fwu_state == FWU_STATE_VERIFYING)
	{
		g_timeout_scale = 6;
		if (HAL_GetTick() - g_last_activity_tick > FWU_DEFAULT_UART_RETRY_MS * g_timeout_scale) // 플래싱 완료까지 충분히 대기
		{
			attempted_retry++;
			const_send_icom_fwu_cmd(pInfo->command.val, pInfo->sub_command, pInfo->payload, pInfo->payload_len);
			g_last_activity_tick = HAL_GetTick();
		}
		if (attempted_retry > FWU_MAX_RETRY_COUNT)
		{
			DM("[FWU] UART Complete Proxy Retry timeout.\r\n");
			reset_update_state();
		}
		osDelay(50);
	}
	g_timeout_scale = 2;
	DM("[FWU] Flashing duration: %lu ms\r\n", HAL_GetTick() - g_last_activity_tick);
	g_last_activity_tick = HAL_GetTick();
	const_send_icom_fwu_cmd(FWU_CMD_UPDATE_STATUS, FWU_SUB_UPDATE_STATUS_FINISH, (uint8_t*)&g_up_info, sizeof(g_up_info));
	return 0;
}

/**
 * @brief CAN 장비의 chunk frame 처리
 */
static int handle_can_binary_chunk_frame(ProtocolInfo_t *pInfo)
{
	// 페이로드 유효성 검사 (최소 상대 주소 4바이트)
	if (pInfo->payload_len < 4)
	{
		DM("[FWU]   -> ERROR: Binary chunk payload too short (missing relative address).\r\n");
		send_tcp_error(g_tcp_socket, 0xFFFFFF04, pInfo->sequence); // Frame Length Error
		return -1;
	}

	// --- *** 상대 주소 읽기 *** ---
	uint32_t chunk_relative_address = 0;
	memcpy(&chunk_relative_address, pInfo->payload, sizeof(uint32_t)); // 상대 주소 추출
	uint8_t *chunk_data = pInfo->payload + sizeof(uint32_t);
	uint16_t chunk_data_len = pInfo->payload_len - sizeof(uint32_t);

	// --- 상태 확인 ---
	if (g_fwu_state != FWU_STATE_CONNECTED && g_fwu_state != FWU_STATE_CAN_TGT_RECEIVING)
	{
		DM("[FWU]   -> ERROR: Received binary chunk in unexpected state (%d).\r\n", g_fwu_state);
		send_tcp_error(g_tcp_socket, 0xFFFFFFFF, pInfo->sequence); // 일반 오류
		return -1;
	}

	g_fwu_state = FWU_STATE_CAN_TGT_RECEIVING; // CAN 데이터 수신 중 상태

	// --- 주소 유효성 및 첫 청크 처리 ---
	if (g_total_bytes_written == 0 && g_bytes_in_page_buffer == 0) // 첫 데이터 수신
	{
		// *** 첫 청크 주소 0인지 검사 ***
		if (chunk_relative_address != 0)
		{
			DM("[FWU]   -> ERROR: First chunk target address/length invalid! Addr=0x%lX\r\n", chunk_relative_address);
			send_tcp_error(g_tcp_socket, 0xFFFFFF12, pInfo->sequence); // Address Error
			return -1;
		}

		// *** 예상 다음 주소를 상대 주소 기준으로 설정 ***
		g_expected_next_relative_addr = chunk_relative_address;
		g_bytes_in_page_buffer = 0; // 버퍼 초기화
		memset(g_flash_page_buffer, 0xFF, FWU_FLASH_PAGE_SIZE);
	}
	// *** 주소 연속성 검사 (상대 주소 기준) ***
	else if (chunk_relative_address != g_expected_next_relative_addr)
	{
		DM("[FWU]   -> ERROR: Address discontinuity! Expected RelAddr=0x%lX, Got RelAddr=0x%lX.\r\n", g_expected_next_relative_addr, chunk_relative_address);
		send_tcp_error(g_tcp_socket, 0xFFFFFF12, pInfo->sequence); // Address Error
		return -1;
	}

	// --- SHA-256 업데이트 (데이터 버퍼링 전에 수행) ---
	if (chunk_data_len > 0)
	{
		sha256_update_wrapper(chunk_data, chunk_data_len);
	}

	// --- 데이터 버퍼링 ---
	uint16_t chunk_offset = 0;
	while (chunk_offset < chunk_data_len)
	{
		// 버퍼링 로직
		uint32_t remaining_page_buffer_space = FWU_FLASH_PAGE_SIZE - g_bytes_in_page_buffer;
		uint32_t bytes_to_buffer_now = MIN(chunk_data_len - chunk_offset, remaining_page_buffer_space);
		if (bytes_to_buffer_now > 0)
		{
			memcpy(g_flash_page_buffer + g_bytes_in_page_buffer, chunk_data + chunk_offset, bytes_to_buffer_now);
			g_bytes_in_page_buffer += bytes_to_buffer_now;
			g_expected_next_relative_addr += bytes_to_buffer_now; // *** 예상 다음 상대 주소 업데이트 ***
			chunk_offset += bytes_to_buffer_now;
			DM("[FWU]   -> Buffered %lu bytes. Total in buffer: %lu / %d\r\n", bytes_to_buffer_now, g_bytes_in_page_buffer, FWU_FLASH_PAGE_SIZE);
		}
		else
		{
			/* 복사할 데이터 없음 */
		}
	}

	// 버퍼링 완료 후 바로 성공 ACK (F1/05) 전송
	send_tcp_ack(g_tcp_socket, FWU_CMD_SLAVE_BINARY_CHUNK_ACK, FWU_SUB_SLAVE_BINARY_CHUNK_ACK, pInfo->sequence);
	return 0; // 성공
}

/**
 * @brief CAN 장비에 명령 전송 후 응답 대기 및 재시도
 */
static int handle_can_abu_cmd(fwu_device_param_t dev, uint32_t id, uint8_t cmd, uint8_t param1, uint8_t* p_param2)
{
	int ret = 1;
	uint8_t attempted_retry = 0;
	g_fwu_state = FWU_STATE_CAN_TGT_WAIT_ACK;
	do
	{
		ret = eCan_abu_send_msg(dev, id, cmd, param1, p_param2);
		if (ret)
		{
			osDelay(2);
			eCan_abu_process();
			ret = ulTaskNotifyTake(pdTRUE, pdMS_TO_TICKS(FWU_DEFAULT_CAN_RETRY_MS));
			if (!ret) //timeout
			{
				attempted_retry++;
				if (attempted_retry > FWU_MAX_RETRY_COUNT)
				{
					DM("[FWU] CAN Device Update Retry timeout.\r\n");
					return 1;
				}
			}
		}
		else osDelay(10); // 송신 실패시 딜레이 이후 다시 전송 시도
	}
	while (!ret);
	return 0;
}

/**
 * @brief 수신된 CAN 바이너리 완료 프레임 처리 (F0/03) - SHA 검증 추가
 */
static int handle_can_binary_complete_frame(ProtocolInfo_t *pInfo)
{
	// 상태 전환, SHA 검증, ACK/Error 전송
	DM("[FWU]   -> Binary Transfer Complete message received.\r\n");
	if (g_fwu_state == FWU_STATE_ERROR || g_fwu_state == FWU_STATE_WRITE_ERROR)
	{
		DM("[FWU]      Ignoring complete message due to previous error.\r\n");
		reset_update_state();
		return 0;
	}
	uint8_t entire_hash[SHA256_HASH_SIZE];
	if (pInfo->payload_len < SHA256_HASH_SIZE)
	{
		DM("[FWU]   -> ERROR: Completion payload too short.\r\n");
		send_tcp_error(g_tcp_socket, 0xFFFFFF04, pInfo->sequence);
		reset_update_state();
		return -1;
	}
	memcpy(entire_hash, pInfo->payload, SHA256_HASH_SIZE);
	DM("[FWU]   -> Expected SHA-256: %s\r\n", BitConverter_ToString(entire_hash, SHA256_HASH_SIZE));
	g_fwu_state = FWU_STATE_VERIFYING;
	DM("[FWU]   -> Calculating buffered SHA-256...\r\n");
	sha256_final_wrapper(g_calculated_hash);
	DM("[FWU]   -> Calculated SHA-256: %s\r\n", BitConverter_ToString(g_calculated_hash, SHA256_HASH_SIZE));
	if (memcmp(entire_hash, g_calculated_hash, SHA256_HASH_SIZE) != 0)
	{
		DM("[FWU]   -> ERROR: SHA-256 verification FAILED!\r\n");
		g_fwu_state = FWU_STATE_VERIFY_FAILED;
		send_tcp_error(g_tcp_socket, 0xFFFFFF02, pInfo->sequence);
		reset_update_state();
		return -1;
	}
	DM("[FWU]   -> SHA-256 verification successful.\r\n");

	// 버퍼링 된 펌웨어 CAN Device에 전송
	if (g_bytes_in_page_buffer > 0)
	{
		DM("[FWU]   -> Sending CAN Device FW. Total %lu bytes...\r\n", g_bytes_in_page_buffer);
		g_fwu_state = FWU_STATE_CAN_TGT_FLASHING;

		// CAN device 부트로더 진입 명령
		g_last_activity_tick = HAL_GetTick();
		if (handle_can_abu_cmd(g_tgt_dev_param_t, ABU_ENTRY_BL_CMD_ID, ABU_BL_CMD_ENTRY_BL, 0, NULL))
		{
			reset_update_state();
			return -1;
		}

		uint32_t calc_crc = 0xFFFFFFFF;
		g_current_page_write_addr = 0;

		// metainfo 있을 경우 metainfo 먼저 전송
		uint32_t metainfo = 0;
		memcpy(&metainfo, &g_flash_page_buffer[0], 4);
		if (metainfo == ABU_BL_METAINFO_DEV_AB || metainfo == ABU_BL_METAINFO_DEV_AU)
		{
			g_last_activity_tick = HAL_GetTick();
			if (handle_can_abu_cmd(g_tgt_dev_param_t, ABU_BL_CMD_ID, ABU_BL_CMD_FW_METAINFO, 0, (uint8_t *)&metainfo))
			{
				reset_update_state();
				return -1;
			}

			memcpy(&metainfo, &g_flash_page_buffer[4], 4);
			g_last_activity_tick = HAL_GetTick();
			if (handle_can_abu_cmd(g_tgt_dev_param_t, ABU_BL_CMD_ID, ABU_BL_CMD_FW_METAINFO, 1, (uint8_t *)&metainfo))
			{
				reset_update_state();
				return -1;
			}

			memcpy(&metainfo, &g_flash_page_buffer[8], 4);
			g_last_activity_tick = HAL_GetTick();
			if (handle_can_abu_cmd(g_tgt_dev_param_t, ABU_BL_CMD_ID, ABU_BL_CMD_FW_METAINFO, 2, (uint8_t *)&metainfo))
			{
				reset_update_state();
				return -1;
			}

			memcpy(&metainfo, &g_flash_page_buffer[12], 4);
			g_last_activity_tick = HAL_GetTick();
			if (handle_can_abu_cmd(g_tgt_dev_param_t, ABU_BL_CMD_ID, ABU_BL_CMD_FW_METAINFO, 3, (uint8_t *)&metainfo))
			{
				reset_update_state();
				return -1;
			}

			g_current_page_write_addr += 16;
		}

		uint8_t crc_idx = 0;
		while (g_bytes_in_page_buffer > g_current_page_write_addr)
		{
			for (int i = 0; i <= 0xFF; ++i)
			{
				// 1. 4 byte 단위로 전송
				g_last_activity_tick = HAL_GetTick();
				if (handle_can_abu_cmd(g_tgt_dev_param_t, ABU_BL_CMD_ID, ABU_BL_CMD_WRITE_BUFFER, i, &g_flash_page_buffer[g_current_page_write_addr]))
				{
					reset_update_state();
					return -1;
				}

				if (i == 0xFF)
				{
					calc_crc = calculate_crc32(&g_flash_page_buffer[g_current_page_write_addr - (i * 4)], (i+1) * 4);
				}
				g_current_page_write_addr += 4;
			}

			// 2. 1024 byte 단위로 crc 결과와 함께 flashing
			g_last_activity_tick = HAL_GetTick();
			if (handle_can_abu_cmd(g_tgt_dev_param_t, ABU_BL_CMD_ID, ABU_BL_CMD_FLASH_PAGE, crc_idx, (uint8_t *)&calc_crc))
			{
				reset_update_state();
				return -1;
			}
			crc_idx++;

			// 3. 전송 완료시 전체 crc 값과 함께 전송
			if (g_bytes_in_page_buffer <= g_current_page_write_addr)
			{
				memcpy(&metainfo, &g_flash_page_buffer[0], 4);
				if (metainfo == ABU_BL_METAINFO_DEV_AB || metainfo == ABU_BL_METAINFO_DEV_AU)
					calc_crc = calculate_crc32(&g_flash_page_buffer[16], g_current_page_write_addr - 16);
				else
					calc_crc = calculate_crc32(&g_flash_page_buffer[0], g_current_page_write_addr);
				g_last_activity_tick = HAL_GetTick();
				if (handle_can_abu_cmd(g_tgt_dev_param_t, ABU_BL_CMD_ID, ABU_BL_CMD_FLASH_CRC, crc_idx, (uint8_t *)&calc_crc))
				{
					reset_update_state();
					return -1;
				}
			}

		}

		g_total_bytes_written += g_bytes_in_page_buffer;
		DM("[FWU]   -> Final write complete. Total: %lu bytes.\r\n", g_total_bytes_written);

		// PC 프로그램에 완료 명령 전송
		send_tcp_ack(g_tcp_socket, FWU_CMD_SLAVE_BINARY_COMPLETE_ACK, FWU_SUB_SLAVE_BINARY_COMPLETE_ACK, pInfo->sequence);

		g_fwu_state = FWU_STATE_CAN_TGT_COMPLETE;
		DM("[FWU] Complete to update target CAN Device.\r\n");
	}
	else
	{
		DM("[FWU] No received data in buffer.\r\n");
		reset_update_state();
		return -1;
	}

	return 0;
}


/**
 * @brief PC로부터 오류 프레임(F1/FF) 수신 시 처리
 */
static int handle_error_frame(ProtocolInfo_t *pInfo)
{
	uint32_t error_code = 0;
	const char *error_desc = "Unknown Error Code";
	if (pInfo->payload_len >= 4)
	{
		memcpy(&error_code, pInfo->payload, 4);
		switch (error_code)
		{
		case 0xffffff01:
			error_desc = "Timeout";
			break;
		case 0xffffff02:
			error_desc = "CRC Error";
			break;
		case 0xffffff21:
			error_desc = "Binary Write Failed";
			break;
		default:
			break;
		}
	}
	DM("[FWU]   -> Received Error Frame from PC! Error Code: 0x%08lX (%s)\r\n", error_code, error_desc);
	reset_update_state();
	return 0;
}


// --- *** SHA-256 래퍼 함수 구현 *** ---
static void sha256_init_wrapper(void)
{
	sha256_init(&g_sha256_ctx);
	DM("[FWU] SHA256 context initialized.\r\n");
}

static void sha256_update_wrapper(uint8_t *data, uint32_t len)
{
	sha256_update(&g_sha256_ctx, data, len);
}

static void sha256_final_wrapper(uint8_t *hash_out)
{
	sha256_final(&g_sha256_ctx, hash_out);
	DM("[FWU]   -> SHA256 hash calculation finished.\r\n");
}

/**
 * @brief TCP ACK 메시지 전송
 */
static void send_tcp_ack(int sock, uint16_t cmd, uint16_t sub_cmd, uint16_t sequence)
{
	uint8_t frame_buf[32];
	uint16_t frame_len = ConstructFrame(frame_buf, sizeof(frame_buf), cmd, sub_cmd, NULL, 0, sequence);
	if (frame_len > 0)
	{
		int r = lwip_send(sock, frame_buf, frame_len, 0);
		if (r != frame_len)
		{
			DM("[FWU] TCP send error for ACK %04X/%04X: ret=%d, errno=%d\r\n", cmd, sub_cmd, r, errno);
		}
		else
		{
			DM("[FWU]   <- Sent ACK: %04X/%04X (Seq: %u)\r\n", cmd, sub_cmd, sequence);
		}
	}
	else
	{
		DM("[FWU] Error constructing ACK frame %04X/%04X\r\n", cmd, sub_cmd);
	}
}

/**
 * @brief TCP 오류 메시지 전송
 */
static void send_tcp_error(int sock, uint32_t error_code, uint16_t sequence)
{
	uint8_t error_payload[4];
	memcpy(error_payload, &error_code, 4);
	uint8_t frame_buf[64];
	uint16_t frame_len = ConstructFrame(frame_buf, sizeof(frame_buf), FWU_CMD_ERROR, FWU_SUB_ERROR, error_payload, 4, sequence);
	if (frame_len > 0)
	{
		lwip_send(sock, frame_buf, frame_len, 0);
		DM("[FWU]   <- Sent Error Frame: F1/FF, Code=0x%08lX (Seq: %u)\r\n", error_code, sequence);
	}
	else
	{
		DM("[FWU] Error constructing Error frame.\r\n");
	}
}

/**
 * @brief 펌웨어 업데이트 관련 상태 및 변수 초기화
 */
static void reset_update_state(void)
{
	DM("[FWU] Resetting firmware update state.\r\n");
	close_tcp_socket();
	g_fwu_state = FWU_STATE_IDLE; /* g_inactive_bank_addr 제거됨 */
	g_current_page_write_addr = 0;
	g_bytes_in_page_buffer = 0;
	g_expected_next_relative_addr = 0;
	g_last_received_tcp_seq = 0;
	g_total_bytes_written = 0;
	g_flash_op_pending = 0;
	tcp_rx_buffer_len = 0;
	g_last_activity_tick = 0;
	g_expected_pc_sequence = 0;
	g_sequence_initialized = 0;
	g_can_metainfo_received = 0;
	g_timeout_scale = 2;
	memcpy(&g_self_dev_param, &g_self_dev_info.DeviceParam, sizeof(g_self_dev_param));
	memset(&g_up_info, 0, sizeof(g_up_info));
	const_send_icom_fwu_cmd(FWU_CMD_UPDATE_STATUS, FWU_SUB_UPDATE_STATUS_RESET, NULL, 0);
}

/**
 * @brief TCP 소켓 닫기 (내부용)
 */
static void close_tcp_socket(void)
{
	if (g_tcp_socket != -1)
	{
		DM("[FWU] Closing TCP socket.\r\n");
		lwip_close(g_tcp_socket);
		g_tcp_socket = -1;
	}
}

/**
 * @brief 펌웨어 업데이트 RTOS 태스크 함수
 */
void FirmwareUpdate_Task(void const *argument)
{
	DM("[FWU] Firmware Update Task Started.\r\n");
	while (g_udp_socket < 0)
	{
		osDelay(100);
	}
	for (;;)
	{
		FirmwareUpdateState_t cs = g_fwu_state;
		if (g_flash_op_pending)
		{
			osDelay(50);
			continue;
		}
		if (cs == FWU_STATE_IDLE && g_udp_socket != -1)
		{
			handle_udp_receive(g_udp_socket);
		}
		cs = g_fwu_state;
		switch (cs)
		{
		case FWU_STATE_READY:
			start_tcp_connection();
			break;
		case FWU_STATE_CONNECTING:
		case FWU_STATE_CONNECTED:
		case FWU_STATE_SELF_RECEIVING:
		case FWU_STATE_UART_TGT_RECEIVING:
		case FWU_STATE_UART_TGT_WAIT_ACK:
		case FWU_STATE_CAN_TGT_RECEIVING:
		case FWU_STATE_CAN_TGT_WAIT_ACK:
		case FWU_STATE_VERIFYING:
		case FWU_STATE_COMPLETE:
		case FWU_STATE_CAN_TGT_ACK_ERROR:
		case FWU_STATE_CAN_TGT_FLASHING:
			if (g_tcp_socket != -1)
			{
				handle_tcp_connection(g_tcp_socket);
			}
			else if (cs != FWU_STATE_IDLE
					&& cs != FWU_STATE_ERROR
					&& cs != FWU_STATE_READY_TO_SWAP
					&& cs != FWU_STATE_VERIFY_FAILED)
			{
				DM("[FWU] TCP socket closed unexpectedly in state %d.\r\n", cs);
				reset_update_state();
			}
			break;
		case FWU_STATE_UART_TGT_COMPLETE:
		case FWU_STATE_CAN_TGT_COMPLETE:
			reset_update_state();
			break;
		case FWU_STATE_READY_TO_SWAP:
			DM("[FWU] Performing Bank Swap and Reboot...\r\n");
			osDelay(100);
			// FLASH_IF_TriggerBankSwapAndReboot();
			g_fwu_state = FWU_STATE_IDLE;
			break;
		case FWU_STATE_IDLE:
		case FWU_STATE_WRITE_PAGE:
			break;
		case FWU_STATE_WRITE_ERROR:
		case FWU_STATE_VERIFY_FAILED:
		case FWU_STATE_ERROR:
		default:
			DM("[FWU] In Error State (%d), resetting to IDLE after delay...\r\n", cs);
			osDelay(1000);
			reset_update_state();
			break;
		}
		osDelay(20);
	}
}

void FirmwareUpdate_proxy_device_search_response_udp(uint8_t *p_frame, uint16_t frame_len)
{
	struct sockaddr_in sender_addr;
	sender_addr.sin_family = AF_INET;
	sender_addr.sin_port = htons(FWU_PC_UDP_PORT);
	sender_addr.sin_addr.s_addr = (u32_t)IPADDR_BROADCAST;

	ProtocolInfo_t info;
	if (ParseFrame(p_frame, (uint16_t)frame_len, &info) == PROTOCOL_OK)
	{
		FWU_DeviceInfo_t deviceInfo;
		memcpy(&deviceInfo, info.payload, sizeof(FWU_DeviceInfo_t));
		send_device_search_response_udp(deviceInfo, g_udp_socket, &sender_addr, info.sequence);
	}
}

void FirmwareUpdate_proxy_fwu_to_tcp(uint8_t *p_frame, uint16_t frame_len)
{
	ProtocolInfo_t info;
	if (ParseFrame(p_frame, (uint16_t)frame_len, &info) == PROTOCOL_OK)
	{
		if (info.command.byte.L == FWU_CMD_SLAVE_BASE)
		{
			if (info.sub_command == FWU_SUB_SLAVE_BINARY_CHUNK_ACK)
			{
				g_fwu_state = FWU_STATE_UART_TGT_RECEIVING;
			}
			else if (info.sub_command == FWU_SUB_SLAVE_BINARY_COMPLETE_ACK)
			{
				g_fwu_state = FWU_STATE_UART_TGT_COMPLETE;
			}
			else if (info.sub_command == FWU_SUB_ERROR)
			{
				g_fwu_state = FWU_STATE_ERROR;
			}
			DM("[FWU] Recv form controller : %s\r\n", BitConverter_ToString(p_frame, frame_len));
			send_tcp_ack(g_tcp_socket, info.command.val, info.sub_command, info.sequence);

		}
		else if (info.command.byte.L == FWU_CMD_ENTRY_UPDATE_MODE)
		{
			if (info.sub_command == FWU_SUB_ENTRY_UPDATE_MODE_REQ)
			{
				FirmwareUpdate_Init();
				if (const_send_icom_fwu_cmd(FWU_CMD_ENTRY_UPDATE_MODE, FWU_SUB_ENTRY_UPDATE_MODE_ACK, NULL, 0))
				{
					DM("[FWU] Send Firmware Update Mode Request Ack to Controller.\r\n");
				}
				else
				{
					reset_update_state();
				}
			}
		}
		//send_device_search_response_udp(deviceInfo, g_udp_socket, &sender_addr, info.sequence);
	}
}

void FirmwareUpdate_proc_can_cmd(can_msg_s msg)
{
	if (g_fwu_state == FWU_STATE_CAN_TGT_WAIT_ACK)
	{
		switch (msg.id)
		{
			case ABU_ENTRY_BL_CMD_ACK_ID:
				DM("[FWU]   -> Recv CAN Bootloader entry ack.\r\n");
				// MyTask에 Notification을 보냄
				g_fwu_state = FWU_STATE_CAN_TGT_FLASHING;
				if (FwuTaskHandle != NULL)
				{
					xTaskNotifyGive(FwuTaskHandle); // FwuTask의 Notification Value를 1 증가시킴
				}
				break;
			case ABU_BL_ERROR_CODE_ID:
				DM("[FWU]   -> Recv CAN Bootloader error.\r\n");
				g_fwu_state = FWU_STATE_CAN_TGT_FLASHING;
				if (FwuTaskHandle != NULL)
				{
					xTaskNotifyGive(FwuTaskHandle); // FwuTask의 Notification Value를 1 증가시킴
				}
				break;
			case ABU_BL_CMD_ACK_ID:
				//DM("[FWU]   -> Recv CAN Bootloader cmd ack.\r\n");
				g_fwu_state = FWU_STATE_CAN_TGT_FLASHING;
				if (FwuTaskHandle != NULL)
				{
					xTaskNotifyGive(FwuTaskHandle); // FwuTask의 Notification Value를 1 증가시킴
				}
				break;
			default:
				DM("[FWU]    -> Recv CAN Bootloader Unknown msg.\r\n");
				break;
		}
	}
}

/**
 * @brief 현재 펌웨어 업데이트 상태 반환
 */
FirmwareUpdateState_t FirmwareUpdate_GetState(void)
{
	return g_fwu_state;
}

/**
 * @brief  HW CRC 연산 모듈을 이용한 표준 CRC-32 연산을 위한 래퍼 함수.
 * @param  data: CRC를 계산할 데이터 배열의 포인터
 * @param  length: 데이터 배열의 길이 (바이트 단위)
 * @retval 계산된 CRC-32 값
 */
static uint32_t calculate_crc32(const uint8_t* data, uint32_t length)
{
  // HAL_CRC_Calculate 함수는 이전 계산 결과에 영향을 받지 않고 새로 계산을 시작합니다.
  // 마지막 결과에 비트 NOT 연산(~)을 적용해야 표준 CRC-32 결과와 일치합니다.
  return ~HAL_CRC_Calculate(&hcrc, (uint32_t*)data, length);
}

/**
 * @brief  입력된 데이터 배열에 대한 CRC-32 값을 소프트웨어적으로 계산합니다.
 * @param  data: CRC를 계산할 데이터 배열의 포인터
 * @param  length: 데이터 배열의 길이 (바이트 단위)
 * @retval 계산된 CRC-32 값
 */
static uint32_t calculate_crc32_sw(const uint8_t* data, uint32_t length)
{
    const uint32_t polynomial = 0xEDB88320;
    uint32_t crc = 0xFFFFFFFF;

    for (uint32_t i = 0; i < length; ++i)
    {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; ++j) {
            if (crc & 1) {
                crc = (crc >> 1) ^ polynomial;
            } else {
                crc = (crc >> 1);
            }
        }
    }

    return ~crc;
}

/**
 * @brief  바이트 배열을 로그 표시를 위한 문자열로 변환
 * @param  data: 문자열로 변환할 배열 데이터
 * @param  len: 배열의 길이
 * @retval 변환된 문자열
 */
static char* BitConverter_ToString(uint8_t *data, int len)
{
	static char s[128];
	int m = MIN(len, (sizeof(s) - 4) / 3);
	if (m < 0)
		m = 0;
	int k = 0;
	for (int i = 0; i < m; i++)
	{
		sprintf(s + k, "%02X-", data[i]);
		k += 3;
	}
	if (len > 0 && m > 0)
		s[k - 1] = '\0';
	else
		s[0] = '\0';
	if (len > m)
		strcat(s, "...");
	return s;
}
