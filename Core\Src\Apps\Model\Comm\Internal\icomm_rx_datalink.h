/**
******************************************************************************
* @file      icomm_rx_datalink.h
* <AUTHOR>
* @date      2024-06-14
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_INTERNAL_ICOMM_RX_DATALINK_H_
#define SRC_APPS_MODEL_COMM_INTERNAL_ICOMM_RX_DATALINK_H_

#include "Common.h"

#define I_COMM_RX_DELAY 2

void Internal_Rx_System_Parameter_Sending_Success(Icomm_Protocol_Rx_s *pRx);
void Internal_Rx_Diag_Req(Icomm_Protocol_Rx_s *pRx);
void Internal_Rx_Ins_Data(Icomm_Protocol_Rx_s *pRx);
void Internal_Rx_Gpio_Set(Icomm_Protocol_Rx_s *pRx);
void Internal_Rx_Fsk_Req_DeMod_Test(Icomm_Protocol_Rx_s *pRx);
void Internal_Rx_Can_Send(Icomm_Protocol_Rx_s *pRx);
void Internal_Rx_System_Error_Value_Request(Icomm_Protocol_Rx_s *pRx);
void Internal_Rx_Bam_Data(Icomm_Protocol_Rx_s *pRx);
void Internal_Rx_System_All_Parameter_Send(Icomm_Protocol_Rx_s *pRx);
void Internal_Rx_FWU(Icomm_Protocol_Rx_s *pRx);
void Internal_Rx_System_Reset_Request(Icomm_Protocol_Rx_s *pRx);

void Internal_Rx_Parsing(Icomm_Protocol_Rx_s *pRx, u16 crc);

#endif /* SRC_APPS_MODEL_COMM_INTERNAL_ICOMM_RX_DATALINK_H_ */
