/**
******************************************************************************
* @file      TargetDac.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_DRIVERS_TARGETDAC_H_
#define SRC_DRIVERS_TARGETDAC_H_

typedef struct
{
    u16 Value[2];
} sDrvDacComponent;
extern sDrvDacComponent gDrvDac;

void TBD_init_dac(void);
void TBD_Dac_Test_Task(void);

#endif /* SRC_DRIVERS_TARGETDAC_H_ */
