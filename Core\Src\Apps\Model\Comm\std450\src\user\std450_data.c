///@file     std450_data.c
///@brief    450 internal data struct and behavior code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <limits.h>

#include "user/std450_data.h"
#include "lwip/sys.h"

/// @brief list mutex lock.
/// @param list linked list pointer.
void std450_list_mutex_lock(std450_list_t* list)
{
    assert(list);
    sys_mutex_lock(&(list->mutex));
}

/// @brief list mutex unlock.
/// @param list linked list pointer.
void std450_list_mutex_unlock(std450_list_t* list)
{
    assert(list);
    sys_mutex_unlock(&(list->mutex));
}

/// @brief list signal wait.
/// @param list linked list pointer.
void std450_list_semapore_cond_wait(std450_list_t* list)
{
    assert(list);
    sys_sem_wait(&list->sem);
}

/// @brief list signal wait.
/// @param list linked list pointer.
/// @param msec wait time milisecond.
/// @return Normal (1), Timeout or Error(0)
int std450_list_semapore_cond_tm_wait(std450_list_t* list, int msec)
{
    int rtn = 0;

    assert(list);
    if(sys_arch_sem_wait(&list->sem, msec) == 0)
    {
        rtn = 1;
    }
    else
    {
        rtn = 0;
    }

    return rtn;
}

/// @brief list signal.
/// @param list linked list pointer.
void std450_list_semapore_cond_signal(std450_list_t* list)
{
    assert(list);
    sys_sem_signal(&list->sem);
}

/// @brief list empty check.
/// @param list linked list pointer.
int std450_list_empty(std450_list_t *list)
{
    assert(list);
    return (list->count == 0);
}

/// @brief list size check.
/// @param list linked list pointer.
unsigned int std450_list_size(std450_list_t *list)
{
    assert(list);
    return list->count;
}

/// @brief Data set in linked list.
/// @param list linked list pointer.
/// @param data Set data.
/// @param func Data set rule.
void std450_list_set(std450_list_t *list, void* data)
{
    if (list != NULL && data != NULL)
    {
        std450_list_mutex_lock(list);

        std450_node_t *now = (std450_node_t*)MEM_MALLOC(sizeof(std450_node_t));
        //DEBUG_LOG_DATA("list set now:Allocated memory address: %p\r\n", now);
        DEBUG_LOG_DATA("[OK] Data Queue is pushed\r\n");
        if (now != NULL)
        {
            now->data = data;
            now->next = NULL;

            if (std450_list_size(list) == 0)
            {
                list->front = now;
            } 
            else 
            {
                list->rear->next = now;
            }

            list->rear = now;
            list->count++;
        }

        std450_list_mutex_unlock(list);
    }
}

/// @brief Data get in linked list.
/// @param list linked list pointer.
/// @param data Set data.
/// @param func Data get rule.
void *std450_list_get(std450_list_t *list)
{
    void *pdata = NULL;

    if (list != NULL && std450_list_size(list) > 0)
    {
        std450_list_mutex_lock(list);

        std450_node_t *now = list->front;
        if (now != NULL)
        {
            list->front = now->next;
            pdata = now->data;
            MEM_FREE(now);

            now = NULL;
            list->count--;
        }

        std450_list_mutex_unlock(list);
    }

    return pdata;
}

/// @brief Linked list reset
/// @param list linked list pointer.
void std450_list_reset(std450_list_t* list)
{
    if (list != NULL)
    {
        while(std450_list_size(list) > 0)
        {
            std450_node_t *now = list->front;
            if (now != NULL)
            {
                list->front = now->next;

                if(now->data != NULL)
                {
                    MEM_FREE(now->data);
                    now->data = NULL;
                }

                MEM_FREE(now);
                now = NULL;
                list->count--;
            }
        }
    }
}

/// @brief Linked list allocate.
/// @return linked list pointer.
void std450_list_init(std450_list_t *list)
{
    assert(list);
    std450_list_reset(list);
    sys_mutex_new(&(list->mutex));
    sys_sem_new(&(list->sem), 1);
}

/// @brief Linked list memory allocate.
/// @param list linked list pointer
void std450_list_free(std450_list_t *list)
{
    assert(list);
    std450_list_reset(list);
    sys_mutex_unlock(&(list->mutex));
    sys_mutex_free(&(list->mutex));
    sys_sem_free(&(list->sem));
}

/// @brief Circular queue allocate.
/// @param cqueue_size make ircular queue size
/// @return Allocate circular queue pointer.
std450_cqueue_t* std450_cqueue_init(unsigned short cqueue_size)
{
    std450_cqueue_t* cbuf;
    unsigned short size = 0;

    if(cqueue_size > USHRT_MAX)
    {
        size = USHRT_MAX;
    }
    else
    {
        size = cqueue_size;
    }

    if(NULL == (cbuf = MEM_MALLOC(sizeof(std450_cqueue_t))))
    {
        return 0;
    }
    else
    {
        cbuf->max = size;
        cbuf->buf = MEM_MALLOC(size);
        memset(cbuf->buf, 0x00, size);
        std450_cqueue_reset(cbuf);
    }

    return cbuf;
}

/// @brief Circular queue free.
/// @param cbuf Allocated queue.
void std450_cqueue_free(std450_cqueue_t* cbuf)
{
    if(cbuf != NULL)
    {
        if(cbuf->buf != NULL)
        {
            MEM_FREE(cbuf->buf);
            cbuf->buf = NULL;
        }

        MEM_FREE(cbuf);
        cbuf = NULL;
    }
}

/// @brief Circular queue reset
/// @param cbuf Allocated queue.
void std450_cqueue_reset(std450_cqueue_t* cbuf)
{
    cbuf->head = 0;
    cbuf->tail = 0;
    cbuf->full = 0;
}

/// @brief Circular queue usinge size.
/// @param cbuf Allocated queue.
unsigned short std450_cqueue_size(std450_cqueue_t* cbuf)
{
    unsigned short size = cbuf->max;
    if(!cbuf->full)
    {
        if(cbuf->head >= cbuf->tail)
        {
            size = (cbuf->head - cbuf->tail);
        }
        else
        {
            size = (cbuf->max + cbuf->head - cbuf->tail);
        }
    }
    return size;
}   

/// @brief Circular queue capacity.
/// @param cbuf Allocated queue.
unsigned short std450_cqueue_capacity(std450_cqueue_t* cbuf)
{
    return cbuf->max-(std450_cqueue_size(cbuf));
}

/// @brief Circular queue full state check.
/// @param cbuf Allocated queue.
int std450_cqueue_full(std450_cqueue_t* cbuf)
{
    return cbuf->full;
}

/// @brief Circular queue empty state check.
/// @param cbuf Allocated queue.
int std450_cqueue_empty(std450_cqueue_t* cbuf)
{
    return (!cbuf->full && (cbuf->head == cbuf->tail));
}


/// @brief    Circular buffer point advance.
/// @param cbuf Allocated queue.
void advance_pointer(std450_cqueue_t* cbuf)
{
    if(cbuf->full)
    {
        cbuf->tail = (cbuf->tail + 1) % cbuf->max;
    }
    cbuf->head = (cbuf->head + 1) % cbuf->max;
    cbuf->full = (cbuf->head == cbuf->tail);
}

/// @brief    Circular buffer point retreat.
/// @param cbuf Allocated queue.
void retreat_pointer(std450_cqueue_t* cbuf)
{
    cbuf->full = 0;
    cbuf->tail = (cbuf->tail + 1) % cbuf->max;
}

/// @brief    Data put in circular buffer.
/// @param cbuf Allocated queue.
/// @param data Put data
/// @return Success (1) Fail (0)
int std450_cqueue_put(std450_cqueue_t* cbuf, char data)
{
    int rtn = 0;

    if(cbuf != NULL)
    {
        if(!std450_cqueue_full(cbuf))
        {
            cbuf->buf[cbuf->head] = data;
            advance_pointer(cbuf);
            rtn = 1;
        }    
    }    
    return rtn;
}

/// @brief    Data get in circular buffer.
/// @param cbuf Allocated queue.
/// @param data Get data
/// @return Success (1) Fail (0)
int std450_cqueue_get(std450_cqueue_t* cbuf, char *data)
{
    int rtn = 0;

    if(cbuf != NULL)
    {
        if(!std450_cqueue_empty(cbuf))
        {
            *data = cbuf->buf[cbuf->tail];
            cbuf->buf[cbuf->tail] = 0x00;
            retreat_pointer(cbuf);
            rtn = 1;
        }
    }
    return rtn;
}

