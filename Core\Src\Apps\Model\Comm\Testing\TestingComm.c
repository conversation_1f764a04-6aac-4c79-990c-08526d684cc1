/**
******************************************************************************
* @file      TestingComm.c
* <AUTHOR>
* @date      2024-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "model.h"
#include "Testing.h"
#include "TargetBoard.h"

void TestingComm_Task(void)
{
    Tcomm_Protocol_Rx_s *pRx = &gTcommData_Rx;

    static u16 Cnt = 0;
    u16 chk_crc16 = 0;
    u8 chk_crc16_L = 0;
    u8 chk_crc16_H = 0;
    u8 recv_crc16_L = 0;
    u8 recv_crc16_H = 0;
    u16 header_len = 0;
    u16 crc_len = 0;
    u16 start_len = 0;
    u16 data_len = 0;

    u8 SysMode = g_hSysStatus.m_pStat->mode.system;
    if(SysMode != SYS_BOOT_RUN)
    {
        return;
    }
    
    int data = TARGET_UART_NULL_DATA;
    do
    {
        data = TBD_uart_get_data(TARGET_UART_DEBUG);
        if(data == TARGET_UART_NULL_DATA)
        {
            return;
        }

        pRx->Buffer[Cnt ++] = (u8)data;
        if(pRx->Buffer[0] == '$')
        {
            if(Cnt == 1)
            {
                pRx->Buffer[3] = '$';
                Cnt = 4;
            }
            else if(Cnt >= 6)
            {
                if(Cnt >= (pRx->st.hd.Frm_Len + 6))
                {
                    start_len = sizeof(Icomm_StartFrame_s);
                    header_len = sizeof(Icomm_HeaderFrame_s);
                    data_len = pRx->st.hd.Frm_Len - header_len;
                    crc_len = 2;

                    chk_crc16 = crc16_calc(0xFFFF, &pRx->Buffer[4], pRx->st.hd.Frm_Len);
                    chk_crc16_L = (u8)((chk_crc16)       & 0x00FF);
                    chk_crc16_H = (u8)((chk_crc16 >> 8)  & 0x00FF);

                    recv_crc16_L = pRx->Buffer[start_len+header_len+data_len];
                    recv_crc16_H = pRx->Buffer[start_len+header_len+data_len+1];

                    if((chk_crc16_L == recv_crc16_L) && (chk_crc16_H == recv_crc16_H))
                    {
                        Testing_Rx_Parsing(pRx);
                    }
                    else
                    {
                    }
                    Cnt = 0;
                }
            }
        }
        else
        {
            Cnt = 0;
        }

    } while (data != TARGET_UART_NULL_DATA);
}
