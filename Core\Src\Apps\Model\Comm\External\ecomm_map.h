/**
******************************************************************************
* @file      ecomm_map.h
* <AUTHOR>
* @date      2024-07-07
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_EXTERNAL_ECOMM_MAP_H_
#define SRC_APPS_MODEL_COMM_EXTERNAL_ECOMM_MAP_H_

#include "Common.h"

#define EX_COM_MAX_LEN 256

typedef struct{
	u8 bReceiving;					// Flag for receiving check
	int nRCnt;								// Receive count
	u8 rx[EX_COM_MAX_LEN];
}eCom_data_s;

extern eCom_data_s gExtBamCom;
extern eCom_data_s gExtRemCom;

#endif /* SRC_APPS_MODEL_COMM_EXTERNAL_ECOMM_MAP_H_ */
