///@file     pthread_user.c
///@brief    Pthread user application function code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#include <stdio.h>
#include <fcntl.h>
#include <stdlib.h>
#include <string.h>
#include "user/platform.h"
#if (USE_RTOS_LWIP == 1)
#include "sys.h"
#endif
#include "user/pthread_user.h"


/// @brief       Event process function set.
/// @param       info pthread information struct pointer.
/// @param       func_proc event process function pointer.
 void pth_event_func_set(pthread_info_t *info, void (*func_proc)(int, void*, void*))
{
    if(info != NULL)
    {
        info->_event = func_proc;
    }
}

/// @brief       Thread process function set.
/// @param       info pthread information struct pointer.
/// @param       func_proc event process function pointer.
void pth_proc_func_set(pthread_info_t *info, void (*func_proc)(void*))
{
    if(info != NULL)
    {
        info->_proc = func_proc;
    }
}

/// @brief       Thread process stop function set.
/// @param       info pthread information struct pointer.
/// @param       func_proc event process function pointer.
void pth_stop_func_set(pthread_info_t *info, void (*func_proc)(void*))
{
    if(info != NULL)
    {
        info->_stop = func_proc;
    }
}

/// @brief       Thread processs start set.
/// @param       info pthread information struct pointer.
void pth_proc_start(pthread_info_t *info)
{
    if(info != NULL)
    {
        info->pause = 0;
        info->oper  = 1;
    }
}

/// @brief       Thread processs pause set.
/// @param       info pthread information struct pointer.
void pth_proc_pause(pthread_info_t *info)
{
    if(info != NULL)
    {
        info->pause = 1;
    }
}

/// @brief       Thread processs resume set.
/// @param       info pthread information struct pointer.
void pth_proc_resume(pthread_info_t *info)
{
    if(info != NULL)
    {
        info->pause = 0;
    }
}

/// @brief       Thread processs operation set.
/// @param       info pthread information struct pointer.
int  pth_proc_oper_get(pthread_info_t *info)
{
    if(info != NULL)
    {
        return info->oper;
    }
    else
    {
        return 0;
    }
}

/// @brief       Thread processs signal number.
/// @param       info pthread information struct pointer.
/// @param       signo signal number.
void  pth_event_signo_set(pthread_info_t *info, int signo)
{
    if(info != NULL)
    {
        info->signo = signo;
    }
}

/// @brief       Evnet process time set.
/// @param       info pthread information struct pointer.
/// @param       init_sec initialize second.
/// @param       init_nsec initialize nano second.
/// @param       interval_sec interval second.
/// @param       interval_nsec interval nano second.
int  pth_proc_tspec_set(pthread_info_t *info, int init_sec, int init_nsec, int interval_sec, int interval_nsec)
{
    if(info == NULL)
    {
        return 0;
    }

    info->itspec.it_value.tv_sec = init_sec;
    info->itspec.it_value.tv_nsec = init_nsec;
    info->itspec.it_interval.tv_sec = interval_sec;
    info->itspec.it_interval.tv_nsec = interval_nsec;

    return 1;
}

/// @brief    Event process register.
/// @param       info pthread information struct pointer.
void pth_event_set(pthread_info_t *info, void* arg)
{
#if (USE_LINUX == 1)
    struct sigevent sigev;
    struct sigaction sa_act;
	struct itimerspec  *ptspec =  &(info->itspec);
    int signo = info->signo;

    if((signo) && (info->_event != NULL))
    {
        memset(&sa_act, 0x00, sizeof(struct sigevent));

        sigemptyset(&sa_act.sa_mask);
        sa_act.sa_sigaction = (info->_event);
        sa_act.sa_flags = SA_SIGINFO;
        if((sigaction(signo, &sa_act, NULL)) == -1){
            return;
        }

        sigev.sigev_notify = SIGEV_SIGNAL;
        sigev.sigev_signo = signo;
        if(arg!=NULL)
        {
            sigev.sigev_value.sival_ptr = arg;
        }

        if (timer_create(CLOCK_MONOTONIC, &sigev, &(info->timer_id)) == -1)
        {
            return;
        }

        if (timer_settime(info->timer_id, 0, ptspec, NULL) == -1)
        {
            return;
        }
    }
#else
    TickType_t nTimeout = 0;
    UBaseType_t uxReloaded = 0;

    if(info->_event != NULL)
    {
        /* Freertos tick is 10ms by default */
        nTimeout = (info->itspec.it_value.tv_sec * 1000) + info->itspec.it_value.tv_nsec;
        uxReloaded = (info->itspec.it_interval.tv_sec != 0 && info->itspec.it_interval.tv_nsec != 0) ? pdTRUE : pdFALSE;
        info->timer_id = xTimerCreate("Timer", nTimeout, uxReloaded, ( void * ) 1, info->_event);

        if (info->timer_id == NULL) 
        {
            DEBUG_LOG_THREAD("In %s:Timer creation failed....\r\n", __func__);
        } 
        else 
        {
            if(xTimerStart(info->timer_id, 0) != pdPASS) 
            {
                DEBUG_LOG_THREAD("In %s:Timer start failed....\r\n", __func__);
            }
        }
    }
#endif
}

/// @brief      Event process re-register.
/// @param      info pthread information struct pointer.
void pth_event_reset(pthread_info_t *info, void* arg)
{
    int rtn = 0;

    if(info == NULL)
    {
        return;
    }

    if(info->timer_id != 0)
    {
#if (USE_LINUX == 1)
        rtn = timer_delete(info->timer_id);
#else
        rtn = xTimerDelete(info->timer_id, 0);
#endif
    }

    if(rtn == 0)
    {
        pth_event_set(info, arg);
    }
}

/// @brief      Event process clear
/// @param      info pthread information struct pointer.
void pth_event_clear(pthread_info_t *info)
{
    if(info == NULL)
    {
        return;
    }

    if(info->timer_id != 0)
    {
        info->signo = 0;
#if (USE_LINUX == 1)
        timer_delete(info->timer_id);
#else
        xTimerDelete(info->timer_id, 0);
#endif
    }
}

/// @brief      Thread proc stop.
/// @param      info pthread information struct pointer.
void pth_proc_thread_stop(pth_args_t *arg)
{
    pth_args_t *args = (pth_args_t *)arg;
    pthread_info_t *info = args->info;

    //DEBUG_LOG_THREAD("pth_proc_thread_stop[%d]\n", info->pth_pid);
    if(info->pth_pid <= 0)
    {
        return;
    }

    info->pause = 0;
    info->oper  = 0;
    info->_stop(arg);
    if(info->pth_pid != 0)
    {
#if (USE_LINUX == 1)
        while(pthread_join(info->pth_pid, (void **)&rtn)!=0);
#else
        vTaskDelete(info->pth_pid);
#endif
    }
}

/// @brief      Thread proc exit.
/// @param      info pthread information struct pointer.
void pth_proc_thread_exit(pth_args_t *arg)
{
    pth_args_t *args = (pth_args_t *)arg;
    pthread_info_t *info = args->info;

    if(info != NULL)
    {
        pth_proc_thread_stop(args);        
    }
}

/// @brief      Thread proc .
/// @param      arg External thread arument.
static void pth_proc(void* arg)
{
    pth_args_t *args = (pth_args_t *)arg;
    pthread_info_t *info = args->info;

    while(info->oper)
    {
        while(info->pause)
        {
            SLEEP(100);
        }

        info->_proc(arg);
        SLEEP(50);
    }
}

/// @brief      Thread initialize.
/// @param      info pthread information struct pointer.
/// @param      arg pthread argument. argument point need 
/// @param      func_stop Thread stop function.
/// @param      func_proc Thread process function.
/// @param      func_event Thread event function.
void pth_proc_thread_init(pthread_info_t *info, void *arg, void (*func_stop)(void*), 
                                void (*func_proc)(void*), void (*func_event)(int, void*, void*))
{
    static char str = '1';
    char pcName[8];

    memset(info, 0x00, sizeof(pthread_info_t));
    info->oper  = 1;
    info->pause = 1;
    info->timer_id = 0;

    pth_stop_func_set(info, func_stop);
    pth_proc_func_set(info, func_proc);
    pth_event_func_set(info, func_event);

#if (USE_LINUX == 1)
	pthread_attr_init(&(info->pth_attr));
	pthread_attr_setscope(&(info->pth_attr), PTHREAD_SCOPE_SYSTEM);
	pthread_create(&(info->pth_pid), &(info->pth_attr), (void*)pth_proc, (void*)arg);
#else
    strcpy(pcName, "thread");
    strcat(pcName, &str);
    str += 1;

    info->pth_pid = sys_thread_new(pcName, pth_proc, (void*)arg, configMINIMAL_STACK_SIZE*4, DEFAULT_THREAD_PRIO);
    if(info->pth_pid == NULL)
    {
        DEBUG_LOG_THREAD("info->pth_pid: %s: Create Fail\r\n", pcName);
    }
    else
    {
        DEBUG_LOG_THREAD("info->pth_pid: %s: Create OK %p\r\n", pcName, info->pth_pid);
    }
#endif
}
