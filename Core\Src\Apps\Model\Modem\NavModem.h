/**
******************************************************************************
* @file      NavModem.h
* <AUTHOR>
* @date      2024-01-05
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/
#ifndef SRC_APPS_MODEL_MODEM_NAVMODEM_H_
#define SRC_APPS_MODEL_MODEM_NAVMODEM_H_

#include "Common.h"
#include "FskModem.h"

#define NAVTEX_CH_DX_STATION 0
#define NAVTEX_CH_RX_STATION 1

#define NAVTEX_BYTE_BUFFER_SIZE 100
#define NAVTEX_BIT_BUFFER_SIZE 100
#define NAVTEX_MESSAGE_RECV_BUFFER_SIZE (4096*2)
#define NAVTEX_MESSAGE_FEC_BUFFER_SIZE (NAVTEX_MESSAGE_RECV_BUFFER_SIZE / 2)

// 1 character = 70ms
// 5sec = 5000/70 = 72 count (5.04sec)
#define NAVTEX_MESSAGE_33_PER_FOR_5_SEC_ERROR_BUFFER_SIZE 72

// 1 character = 70ms
// 1 character 140ms after FEC
// 2.5sec = 2500/140 = 18 count (2.520sec)
#define NAVTEX_MESSAGE_RE_START_PHASING_DETECT_BUFFER_SIZE 18

// 1 character = 70ms
// 1 character 140ms after FEC
// 1.5sec = 1500/140 = 11 count (1.540sec)
#define NAVTEX_MESSAGE_END_PHASING_BUFFER_SIZE 11

#define NAVTEX_MESSAGE_STORED_BUFFER_SIZE 400
#define NAVTEX_MESSAGE_STRING_BUFFER_SIZE 3796

#define LATTERS_MODE 'a'
#define FIGURES_MODE 'b'
#define IDLE_SIGNAL_B 'c'
#define IDLE_SIGNAL_A 'e'
#define PHASING_SIGNAL_1 'e'
#define PHASING_SIGNAL_2 'f'
#define RAW_PHASING_SIGNAL_1 0x70
#define RAW_PHASING_SIGNAL_2 0x19
#define CHARACTER_ERROR 0x2A
#define START_PHASING 'f'
#define END_PHASING 'e'

#define NAV_MODEM_TICK_5_SEC (5000/50)
#define NAV_MODEM_TICK_10_SEC (10000/50)
#define NAV_MODEM_TICK_20_SEC (20000/50)

typedef enum
{
    RECV_DIAG_TYPE_NONE = 0,
    RECV_DIAG_TYPE_RUN,
    RECV_DIAG_TYPE_OK,
    RECV_DIAG_TYPE_FAIL,
} RECV_DIAG_t;

typedef enum
{
    RECV_NONE = 0,

    RECV_MSG_SUCCESS = 1,
    RECV_ERROR_START_PHASING_TIMEOUT       ,
    RECV_ERROR_CHARACTER_ERROR_OVER        ,
    RECV_ERROR_ZCZC_TIMEOUT                ,
    RECV_ERROR_ZCZC_FEC_BUFFER_OVER        ,
    RECV_ERROR_MSG_TIMEOUT                 ,
    RECV_ERROR_MSG_FEC_BUFFER_OVER         ,
    RECV_ERROR_NNNN_NOT_MATCH              ,
    RECV_ERROR_ID_NOT_MATCH                ,
    RECV_ERROR_END_PHASING_BEFORE_NNNN     ,
    RECV_ERROR_RE_START_PHASING            ,
}MsgRecvError_t;

typedef enum
{
    RECV_SEQ_STOP = 0,
    RECV_SEQ_PHASING_START_CHECK,
    RECV_SEQ_RECEIVE,
}eReceivedSequence;

typedef enum
{
    SEARCH_SEQ_ZCZC,
    SEARCH_SEQ_ONE_SPACE,
    SEARCH_SEQ_ID,
    SEARCH_SEQ_CR_LF,
    SERACH_SEQ_MSG,
}eReceivedSerachSequence;

typedef struct
{  
    u16 Freq;
    u16 Total_Character_Len;
    u16 Error_Character_Len;
    u16 ErrorRate;   // 0.1% scale

    u8 ID[5];
}sMessageComponent;

typedef struct
{
    u16 Message_Len;    
    u8 Message[NAVTEX_MESSAGE_STRING_BUFFER_SIZE];
}sMessageString;

typedef struct
{
    sMessageComponent Info;
    sMessageString Msg;
}sNavtexMsg;

typedef struct
{
    u8 rcvData;
    u8 Seq;
    u8 SearchSeq;
    u8 CCIR_Mode;
    u32 StartPhasingTime_ms;
    u32 EndPhasingTime_ms;
    u32 Timeout_Cnt;
    u16 ErrorCharLen;
    u16 ErrorRate;

    u8 Bit_Cnt;

    u8 result;
    u8 result_2nd;

    u8 FecData;

    int tmp_data;

    u8 End_Detect_NNNN;
    u8 End_Detect_rnn;
    u8 ZCZC_Detect;

    u16 debug_i;
    u16 debug_i_add;
    u16 debug_len;
    u16 debug_crc16;

    u32 ByteTail_Cnt;
    u32 ByteHead_Cnt;
    u8 ByteBuffer[NAVTEX_BYTE_BUFFER_SIZE];

    u32 Recv_Cnt;
    u8 RecvBuffer[NAVTEX_MESSAGE_RECV_BUFFER_SIZE];

    u32 RawStation_Cnt;
    u8 Raw_DX[NAVTEX_MESSAGE_FEC_BUFFER_SIZE];
    u8 Raw_RX[NAVTEX_MESSAGE_FEC_BUFFER_SIZE];

    u32 Station_Cnt;
    u8 DX[NAVTEX_MESSAGE_FEC_BUFFER_SIZE];
    u8 RX[NAVTEX_MESSAGE_FEC_BUFFER_SIZE];

    u32 FCTail_Cnt;
    u32 FCHead_Cnt;
    u8 FecComplitedBuffer[NAVTEX_MESSAGE_FEC_BUFFER_SIZE];

    u8 ErrorOver_Detect;
    u32 ErrorOver_Cnt;
    u8 ErrorOverDetectBuffer[NAVTEX_MESSAGE_33_PER_FOR_5_SEC_ERROR_BUFFER_SIZE];

    u8 ReStartPhasing_Detect;
    u32 ReStartPhasing_Cnt;
    u8 ReStartPhasingDetectBuffer[NAVTEX_MESSAGE_RE_START_PHASING_DETECT_BUFFER_SIZE];

    u8 EndPhasing_Detect;
    u32 EndPhasing_Cnt;
    u8 EndPhasingDetectBuffer[NAVTEX_MESSAGE_END_PHASING_BUFFER_SIZE];

    u32 Temp_Cnt;
    u32 Msg_Character_Cnt;
    u8 TempBuffer[NAVTEX_MESSAGE_FEC_BUFFER_SIZE];

}sNavtexRx;

typedef struct
{
    u8 TxEnable_Mode;
    u8 CCIR_Mode;
    u8 DX[NAVTEX_MESSAGE_FEC_BUFFER_SIZE];
    u8 RX[NAVTEX_MESSAGE_FEC_BUFFER_SIZE];
}sNavtexTx;

typedef struct
{
    int Mode;
    int Seq;
    int Result;
} NavModem_s;

void NavModem_Init(void);

u8 CCIR476_Decode(u8 str, sNavtexRx *pNavRx);
u8 CCIR476_Encode(u8 str, sNavtexTx *pNavTx);
u8 CCIR476_Mode_Check(u8 str, sNavtexTx *pNavTx);
u8 NavMessage_Id_A_to_Z_Check(u8 character);
u8 NavMessage_Id_0_to_9_Check(u8 character);
u8 NavMessage_StartPhasingDetect(sNavtexRx *pNavRx, tFskRxMDM *pFSK);

void NavMessage_RxDataAdd(sNavtexRx *pNavRx, tFskRxMDM *pFSK);
u8 NavMessage_RxDataRead(sNavtexRx *pNavRx, tFskRxMDM *pFSK);

void NavMessage_RecvBufferAdd(sNavtexRx *pNavRx, u8 data);
void NavMessage_RecvBufferClear(sNavtexRx *pNavRx);

void NavMessage_FecComplitedBufferAdd(sNavtexRx *pNavRx, u8 data);
u8 NavMessage_FecComlitedBufferRead(sNavtexRx *pNavRx);
void NavMessage_FecSort(sNavtexRx *pNavRx);

u8 NavMessage_ZCZC_Detect(u8 *pArr);
u8 NavMessage_NNNN_Detect(u8 *pArr);
u8 NavMessage_End_rnn_Detect(u8 *pArr);
u8 NavMessage_End_Phasing_Detect(u8 *pBuffer, u32 *pCnt, u8 InData);
u8 NavMessage_ErrorOver_Detect(u8 *pBuffer, u32 *pCnt, u8 InData);
u8 NavMessage_ReStartPhasing_Detect(u8 *pBuffer, u32 *pCnt, u8 InData);
u8 NavMessage_SearchSequence(sNavtexRx *pNavRx, sNavtexMsg *pNavMsg, tFskRxMDM *pFSK);

void NavtexMessageReceive(sNavtexRx *pNavRx, tFskRxMDM *pFSK, sNavtexMsg *pNavMsg);

void System_Debug_Tx_Navtex_Message_Log_Data(int mode, sNavtexRx *pNavRx, sMessageComponent *pInfo, sMessageString *pMsg);

void NavtexMessageTransmit(sNavtexTx *pNavTx, tFskTxMDM *pFSK, u8 *pId, u8 *pStr);
void NavtexMessageTransmit_StartPhasing(sNavtexTx *pNavTx, tFskTxMDM *pFSK);

void Navtex_Fsk_Dot_Out(tFskTxMDM *pFSK);
void Navtex_Fsk_Space_Out(tFskTxMDM *pFSK);
void Navtex_Fsk_Mark_Out(tFskTxMDM *pFSK);

void Self_NavtexMessageTransmit(void);
void Self_NavtexMessageTransmit_StartPhasing(void);
void Self_Navtex_Fsk_Dot_Out(void);
void Self_Navtex_Fsk_Mark_Out(void);
void Self_Navtex_Fsk_Space_Out(void);
void Self_Navtex_Tx_Stop(void);

int Proc_Self_Diag_StartPhasing_Test(sNavtexRx *p_Rx, int flag);
int Proc_Self_Diag_Message_Tx(int flag);

void NavtexMessageReceive_Task(void);
void NavtexMessageDiagnosis_Task(void);
void NavtexModem_Task(void);

extern u8 gNavtexDebugMsgBuffer[10240];
extern sNavtexTx gNavtexTx;
extern sNavtexRx gNavtexRx_518KHz;
extern sNavtexRx gNavtexRx_490KHz;
extern sNavtexRx gNavtexRx_42095KHz;

extern char *pg_NavModem_Test_Id;
extern char *pg_NavModem_Test_Msg;

#endif /* SRC_APPS_MODEL_MODEM_NAVMODEM_H_ */
