///@file     std450.h
///@brief    450 library api header.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#ifndef STD450_H
#define STD450_H


#include <stdint.h>
#include "user/platform.h"
#include "user/net_user.h"

# define STD450_API

#ifdef  __cplusplus
# define STD450_BEGIN_DECLS  extern "C" {
# define STD450_END_DECLS    }
#else
# define STD450_BEGIN_DECLS
# define STD450_END_DECLS
#endif

STD450_BEGIN_DECLS

#define C_SRP_PORT          60056           ///SRP port[7.5.2 & Table 4]. 
#define C_SRP_IP            "************"  ///SRP multicast ip[7.5.2 Table 4]

#define C_SYSLOG_PORT       514             ///syslog default port [******* & Table 6] 
#define C_SYSLOG_IP         "*************" ///syslog default multicast ip [******* & Table 6] 

#define C_LEN_TCKEN         2
#define C_LEN_450_TOKEN     6

#define C_TOKEN_UDPBC       "UdPbC\0"
#define C_TOKEN_RAUDP       "RaUdP\0"
#define C_TOKEN_RRUDP       "RrUdP\0"
#define C_TOKEN_NKPGN       "NkPgN\0"
#define C_TOKEN_RRTCP       "RrTcP\0"

enum 
{
    CH_MISC = 0x00,
    CH_NAVD,
    CH_RCOM,
    CH_BAM_1,
    CH_BAM_2,
    CH_MAX,
};

#define C_MAX_450_BUF       1472
#define C_MAX_SFI_LEN       6
#define C_MAX_CH_NUM        CH_MAX

// Transmission group
#define TRANSGROUP_MISC     1
#define TRANSGROUP_TGTD     2
#define TRANSGROUP_SATD     3
#define TRANSGROUP_NAVD     4
#define TRANSGROUP_VDRD     5
#define TRANSGROUP_RCOM     6
#define TRANSGROUP_TIME     7
#define TRANSGROUP_PROP     8
#define TRANSGROUP_USR1     9
#define TRANSGROUP_USR2     10
#define TRANSGROUP_USR3     11
#define TRANSGROUP_USR4     12
#define TRANSGROUP_USR5     13
#define TRANSGROUP_USR6     14
#define TRANSGROUP_USR7     15
#define TRANSGROUP_USR8     16
#define TRANSGROUP_BAM1     17
#define TRANSGROUP_BAM2     18
#define TRANSGROUP_CAM1     19
#define TRANSGROUP_CAM2     20
#define TRANSGROUP_NRB1     21  // Non re-transmittable binary file transfer
#define TRANSGROUP_NRB2     22  // Non re-transmittable binary file transfer
#define TRANSGROUP_NRB3     23  // Non re-transmittable binary file transfer
#define TRANSGROUP_NRB4     24  // Non re-transmittable binary file transfer
#define TRANSGROUP_NRB5     25  // Non re-transmittable binary file transfer
#define TRANSGROUP_RBF1     26  // Re-transmittable binary file transfer
#define TRANSGROUP_RBF2     27  // Re-transmittable binary file transfer
#define TRANSGROUP_RBF3     28  // Re-transmittable binary file transfer
#define TRANSGROUP_RBF4     29  // Re-transmittable binary file transfer
#define TRANSGROUP_RBF5     30  // Re-transmittable binary file transfer

#define STD450_FAIL         0
#define STD450_SUCCESS      1

// Struct
//-----------------------------------------------------------------------------
typedef struct _CH_Info
{
    int     Transmission_Group;
    char    SourceID_token[C_LEN_TCKEN];
    int     SourceID_num;
    char    DestiID_token[C_LEN_TCKEN];
    int     DestiID_num;
    int     Device;
    int     Channel;
    int     FuncType;
} CH_Info;

// 
typedef struct _IEC450_Info
{
    char    NIC_IP[NI_MAXHOST];
    CH_Info CH_INFO[C_MAX_CH_NUM];
} IEC450_Info;

typedef struct _std450 std450_t;

STD450_API std450_t* std450_new(void);
STD450_API void std450_mem_free(std450_t *dev450);
STD450_API void std450_free(std450_t *dev450);
STD450_API int  std450_dev_name_set(std450_t *dev450, char *name);
STD450_API int  std450_dev_name_get(std450_t *dev450, char *name);
STD450_API void std450_dev_index_set(std450_t *dev450, int idx);
STD450_API int  std450_dev_index_get(std450_t *dev450);
STD450_API std450_t* std450_open(IEC450_Info *IEC450_info);
STD450_API void std450_close(std450_t *dev450);
STD450_API int  std450_send(std450_t *dev450, int nCh, void *msg, int len);
STD450_API int  std450_recv(std450_t *dev450, int nCh, void *msg, int len);

STD450_END_DECLS

#endif /* STD450_H */
