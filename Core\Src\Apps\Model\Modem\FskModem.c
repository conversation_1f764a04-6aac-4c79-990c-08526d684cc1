﻿/**
 * @file      FskModem.c
 * <AUTHOR>
 * @brief     FskModem 관련 함수
 * @version   0.1
 * @date      2022-08-26
 *
 * @copyright Copyright (c) 2022
 *
 */


//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#include "AllConst.h"
#include "model.h"
#include "TargetBoard.h"
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

tFskTxMDM     G_xMsgFskTxMDM                = {0x00, };
tFskRxMDM     G_xMsgFskRxMDM_518KHz         = {0x00, };
tFskRxMDM     G_xMsgFskRxMDM_490KHz         = {0x00, };
tFskRxMDM     G_xMsgFskRxMDM_42095KHz       = {0x00, };
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
/**
 * @brief sin-cos발생 Oscillator 초기화 작업
 * 
 * @param pOSC    tTwoOSC-pointer
 * @param dRunMode 동작 모드 ()
 * @param dSmpFrq  sampling    주파수
 * @param dOscFrq  oscillating 주파수
 */
void  MakeInitTwoOSC(tTwoOSC *pOSC, uint32_t dRunMode, uint32_t dSmpFrq, uint32_t dOscFrq)
{
      pOSC->dRunMode = TWO_OSC_RUN_MODE_FILTER;

      if (dRunMode == TWO_OSC_RUN_MODE_FILTER || dRunMode == TWO_OSC_RUN_MODE_FUNCTION)
          pOSC->dRunMode = dRunMode;

      if (pOSC->dRunMode == TWO_OSC_RUN_MODE_FILTER)
      {
          pOSC->fVcoPhs = 0.0f;
          pOSC->fOscPhs = 2.0f * sinf(MAT_VAL_PI_F * (float32_t)dOscFrq / (float32_t)dSmpFrq);
          pOSC->fCosVal = 1.0f;
          pOSC->fSinVal = 0.0f;
      }
      else
      {
          pOSC->fVcoPhs = 0.0f;
          pOSC->fOscPhs = (MAT_VAL_PI_MUL_2_F * dOscFrq) / (float32_t)dSmpFrq;
          pOSC->fCosVal = 0.0f;
          pOSC->fSinVal = 0.0f;
      }
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief sin-cos발생 oscillator 실행
 * 
 * @param pOSC tTwoOSC-pointer
 */
void  CalcNewTwoOsc(tTwoOSC *pOSC)
{
      if (pOSC->dRunMode == TWO_OSC_RUN_MODE_FILTER)
      {
          pOSC->fSinVal = pOSC->fSinVal + pOSC->fOscPhs * pOSC->fCosVal;
          pOSC->fCosVal = pOSC->fCosVal - pOSC->fOscPhs * pOSC->fSinVal;
      }
      else
      {
          pOSC->fVcoPhs += pOSC->fOscPhs;
          if (pOSC->fVcoPhs >= MAT_VAL_PI_MUL_2_F)
              pOSC->fVcoPhs -= MAT_VAL_PI_MUL_2_F;

          pOSC->fSinVal = sinf(pOSC->fVcoPhs);
          pOSC->fCosVal = cosf(pOSC->fVcoPhs);
      }
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  MakeInitFskRxModem(tFskRxMDM *pFSK, uint32_t dSmpFrq, uint32_t dBaudRate, uint32_t dSpcFreq, uint32_t dMrkFreq, uint32_t dBufSize, uint32_t dAdcSize)
{
      int i = 0;

      MakeInitTwoOSC(&(pFSK->xSpcOSC), TWO_OSC_RUN_MODE_FILTER, dSmpFrq, dSpcFreq);
      MakeInitTwoOSC(&(pFSK->xMrkOSC), TWO_OSC_RUN_MODE_FILTER, dSmpFrq, dMrkFreq);

      pFSK->dRunSize  = dSmpFrq / dBaudRate;
      pFSK->dRunPosX  = 0;

      pFSK->pRunSpcI  = (float32_t *)malloc(sizeof(float32_t) * pFSK->dRunSize); 
      memset(pFSK->pRunSpcI, 0x00, sizeof(float32_t) * pFSK->dRunSize);

      pFSK->pRunSpcQ  = (float32_t *)malloc(sizeof(float32_t) * pFSK->dRunSize); 
      memset(pFSK->pRunSpcQ, 0x00, sizeof(float32_t) * pFSK->dRunSize);

      pFSK->pRunMrkI  = (float32_t *)malloc(sizeof(float32_t) * pFSK->dRunSize); 
      memset(pFSK->pRunMrkI, 0x00, sizeof(float32_t) * pFSK->dRunSize);

      pFSK->pRunMrkQ  = (float32_t *)malloc(sizeof(float32_t) * pFSK->dRunSize); 
      memset(pFSK->pRunMrkQ, 0x00, sizeof(float32_t) * pFSK->dRunSize);

      pFSK->fSumSpcI  = 0.0f;
      pFSK->fSumSpcQ  = 0.0f;
      pFSK->fSumMrkI  = 0.0f;
      pFSK->fSumMrkQ  = 0.0f;

      memset(&(pFSK->vBitPack), 0x00, sizeof(pFSK->vBitPack));
      pFSK->dDetermineFreq = 0;

      pFSK->dBitDataP = 0;
      pFSK->dBitDataC = 0;

      pFSK->dRecvSize = dBufSize;
      pFSK->pRecvData = (uint8_t *)malloc(sizeof(uint8_t) * pFSK->dRecvSize);
      pFSK->dRecvHead = 0;
      pFSK->dRecvTail = 0;

      pFSK->dPllDiffValue = 0;

      pFSK->dPllStepV = 16;
      pFSK->dPllIncrV = pFSK->dPllStepV * 96;
      pFSK->dPllFullV = pFSK->dPllIncrV * pFSK->dRunSize;
      pFSK->dPllSmpPt_Div = pFSK->dPllFullV / FSK_SAMPLE_POINT;
      pFSK->dPllCalibrationV = 0;
      pFSK->dPllMaxCalibrationV = pFSK->dPllIncrV * 2;
      //pFSK->dPllMaxCalibrationV = pFSK->dPllStepV;

        for(i=0; i<FSK_SAMPLE_POINT; i++)
        {
            // full	15360	
            // div20	768	
            // sample[0]		768
            // sample[1]		1536
            // sample[2]		2304
            // sample[3]		3072
            // sample[4]		3840
            // sample[5]		4608
            // sample[6]		5376
            // sample[7]		6144
            // sample[8]		6912
            // sample[9]		7680
            // sample[10]	    8448
            // sample[11]		9216
            // sample[12]		9984
            // sample[13]		10752
            // sample[14]		11520
            // sample[15]		12288
            // sample[16]		13056
            // sample[17]		13824
            // sample[18]		14592
            // sample[19]		15360

            pFSK->dPllSmpPt[i] = (pFSK->dPllSmpPt_Div * (i+1));
        }

      pFSK->dPllHalfV = pFSK->dPllFullV / 2;

      pFSK->dPllSmpPtStepC = 0;
      pFSK->dPllSmpPtStepP = 0;
      pFSK->dPllSmpPtCnt = 0;
      pFSK->dPllSmpPtStepCnt = 0;

      pFSK->dPllValue = 0;

      pFSK->dAdcSize  = dAdcSize;
      pFSK->pAdcData  = (uint16_t *)malloc(sizeof(uint16_t) * pFSK->dAdcSize); memset(pFSK->pAdcData, 0x00, sizeof(uint16_t) * pFSK->dAdcSize);
      pFSK->dAdcPosX  = 0;
      pFSK->dAdcSumX  = 0;
      pFSK->wAdcAvrX  = 0;
}

void ConfigFskRxModemFreqCh(tFskRxMDM *pFSK, uint16_t Freq)
{
    pFSK->dFreqCh = Freq;
}

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  MakeInitFskTxModem(tFskTxMDM *pFSK, uint32_t dSmpFrq, uint32_t dBaudRate, uint32_t dSpcFreq, uint32_t dMrkFreq, uint32_t dBufSize)
{
      pFSK->fSpcPhiVal = (MAT_VAL_PI_MUL_2_F * dSpcFreq) / (float32_t)dSmpFrq;
      pFSK->fMrkPhiVal = (MAT_VAL_PI_MUL_2_F * dMrkFreq) / (float32_t)dSmpFrq;
      pFSK->fVcoPhiVal = 0.0f;
      pFSK->fVcoAmpVal = 0.0f;

      pFSK->dSendSize  = dBufSize;
      pFSK->pSendData  = (uint8_t *)malloc(sizeof(uint8_t) * pFSK->dSendSize);
      pFSK->nSendHead  = 0;
      pFSK->nSendTail  = 0;

      pFSK->dTickValX  = dSmpFrq / dBaudRate;
      pFSK->dTickCntr  = pFSK->dTickValX;
      pFSK->dSendData  = 0;
      pFSK->dSendMode  = 0;

      pFSK->v_pp = FSK_TX_VOLTAGE_PP;
      pFSK->v_offset = FSK_TX_VOLTAGE_OFFSET;
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
int   ReadFskRxBitData(tFskRxMDM *pFSK)
{
      int   nBitData;

      if (pFSK->dRecvTail == pFSK->dRecvHead)
          return(FSK_MDM_BIT_DATA_EMPTY);

      nBitData = pFSK->pRecvData[pFSK->dRecvTail];
      ++pFSK->dRecvTail;
      if (pFSK->dRecvTail >= pFSK->dRecvSize)
          pFSK->dRecvTail  = 0;

      return(nBitData);
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  ApndFskRxBitData(tFskRxMDM *pFSK, uint8_t bBitData)
{
      pFSK->pRecvData[pFSK->dRecvHead] = bBitData;
      ++pFSK->dRecvHead;
      if (pFSK->dRecvHead >= pFSK->dRecvSize)
      {
        pFSK->dRecvHead  = 0;
      }
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  ClearAllFskRxBitData(tFskRxMDM *pFSK)
{
      pFSK->dRecvTail = (volatile uint32_t)(pFSK->dRecvHead);
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  ClearAllFskTxBitData(tFskTxMDM *pFSK)
{
      pFSK->nSendTail = (volatile uint32_t)(pFSK->nSendHead);
      pFSK->dTickCntr  = 0;
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
int   ReadFskTxBitData(tFskTxMDM *pFSK)
{
      int   nBitData;

      if (pFSK->nSendTail == pFSK->nSendHead)
          return(FSK_MDM_BIT_DATA_EMPTY);

      nBitData = pFSK->pSendData[pFSK->nSendTail];
      ++pFSK->nSendTail;
      if (pFSK->nSendTail >= pFSK->dSendSize)
          pFSK->nSendTail  = 0;

      return(nBitData);
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  ApndFskTxBitData(tFskTxMDM *pFSK, uint8_t bBitData)
{
      pFSK->pSendData[pFSK->nSendHead] = bBitData;
      ++pFSK->nSendHead;
      if (pFSK->nSendHead >= pFSK->dSendSize)
          pFSK->nSendHead  = 0;
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  ApndFskTx7BitData(tFskTxMDM *pFSK, uint8_t bByteData)
{
    int i = 0;

    for(i=0; i<7; i++)
    {
        if((bByteData & 0x01) == 0x01)
        {
            pFSK->pSendData[pFSK->nSendHead] = 1;      
        }
        else
        {
            pFSK->pSendData[pFSK->nSendHead] = 0;
        }
        bByteData >>= 1;

        ++pFSK->nSendHead;
        if (pFSK->nSendHead >= pFSK->dSendSize)
            pFSK->nSendHead  = 0;
    }
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void    ApndFskTxDiagDotData(tFskTxMDM *pFSK, uint8_t len)
{
    uint8_t bit_data = 0;
    int i = 0;

    for(i=0; i<len; i++)
    {
        bit_data = (uint8_t)(i%2);
        ApndFskTxBitData(pFSK, bit_data);
    }
}

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  SetFskTxSendMode(tFskTxMDM *pFSK, FskTxState_t mode)
{
    pFSK->dSendMode  = mode;
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
uint32_t GetFskTxSendMode(tFskTxMDM *pFSK)
{
      return(pFSK->dSendMode);
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
float32_t GetFskTxAmpValue(tFskTxMDM *pFSK)
{
      return(pFSK->fVcoAmpVal);
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
int   CalcFskRxAdcAvrVal(tFskRxMDM *pFSK, uint16_t wAdcVal)
{
      pFSK->dAdcSumX = (pFSK->dAdcSumX - pFSK->pAdcData[pFSK->dAdcPosX]) + wAdcVal;
      pFSK->wAdcAvrX = (pFSK->dAdcSumX / pFSK->dAdcSize);

      pFSK->pAdcData[pFSK->dAdcPosX] = wAdcVal;

      ++pFSK->dAdcPosX;
      if (pFSK->dAdcPosX >= pFSK->dAdcSize)
          pFSK->dAdcPosX  = 0;

      return(pFSK->wAdcAvrX);
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
uint32_t  RxSamplePointStepCheck(tFskRxMDM *pFSK)
{
    uint32_t ret = 0;
    uint8_t chk = 0;

    if(pFSK->dPllSmpPtStepP != pFSK->dPllSmpPtStepC)
    {
        if(pFSK->dPllSmpPtStepP == (FSK_SAMPLE_POINT-1) && pFSK->dPllSmpPtStepC == 0)
        {
            chk = 1;
        }
        else if((pFSK->dPllSmpPtStepP+1) == pFSK->dPllSmpPtStepC)
        {
            chk = 1;
        }
        else
        {
            chk = 0;
        }

        if(chk == 1)
        {
            pFSK->dPllSmpPtStepP = pFSK->dPllSmpPtStepC;
            pFSK->dPllSmpPtStepCnt += 1;

            if(pFSK->dDetermineFreq > 0)
            {
                pFSK->dPllSmpPtCnt += 1;
            }
            else
            {
                pFSK->dPllSmpPtCnt -= 1;
            }
            pFSK->dDetermineFreq = 0;

            ret = 1;
            return ret;
        }
    }

    return ret;
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
uint32_t  RxSamplePointFinalResult(tFskRxMDM *pFSK)
{
    uint32_t ret = 0xFF;

    if(pFSK->dPllSmpPtStepCnt >= FSK_SAMPLE_POINT)
    {
        if(pFSK->dPllSmpPtCnt > 0)
        {
            ret = 1;
        }
        else
        {
            ret = 0;
        }

        pFSK->dPllSmpPtCnt = 0;
        pFSK->dPllSmpPtStepCnt = 0;

        return ret;
    }

    return ret;
}

int   Proc_Fsk_DeMod_Test(int ch, int final_val)
{
    FSK_DeMod_Test_s *p = NULL;
    int ret = 0;
    int ConvCh = 0;

    switch(ch)
    {
        case FSK_FREQ_INT_518KHZ:
            p = &g_hSysStatus.m_pStat->test_fsk[FSK_DEMOD_TEST_CH_518KHZ];
            ConvCh = FSK_DEMOD_TEST_CH_518KHZ;
        break;

#if NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BASE_1_785KHZ
        case FSK_FREQ_LOC_490KHZ:
            p = &g_hSysStatus.m_pStat->test_fsk[FSK_DEMOD_TEST_CH_490KHZ];
            ConvCh = FSK_DEMOD_TEST_CH_490KHZ;
        break;

        case FSK_FREQ_LOC_42095KHZ:
            p = &g_hSysStatus.m_pStat->test_fsk[FSK_DEMOD_TEST_CH_490KHZ];
            ConvCh = FSK_DEMOD_TEST_CH_4209_5KHZ;
        break;

#elif NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BAND_PASS_10KHZ
        case FSK_FREQ_LOC_490KHZ:
            p = &g_hSysStatus.m_pStat->test_fsk[FSK_DEMOD_TEST_CH_490KHZ];
            ConvCh = FSK_DEMOD_TEST_CH_490KHZ;
        break;

        case FSK_FREQ_LOC_42095KHZ:
            p = &g_hSysStatus.m_pStat->test_fsk[FSK_DEMOD_TEST_CH_4209_5KHZ];
            ConvCh = FSK_DEMOD_TEST_CH_4209_5KHZ;
        break;
#endif
    }

    if(p->end_flag == 0)
    {
        if(p->start_flag == 1)
        {
            p->cur_cnt += 1;
            if(final_val == 1)
            {
                p->mark_cnt += 1;
            }
            else
            {
                p->space_cnt += 1;
            }

            if(p->cur_cnt >= p->tot_cnt)
            {
                p->end_flag = 1;
                p->start_flag = 0;

                Internal_Tx_Fsk_Resp_DeMod_Test(p, ConvCh);
            }
        }
    }

    return ret;
}

void Set_Self_BER_Rf_Test(tFskTxMDM *pFSK_Tx)
{
    RF_Self_Test_s *p = &g_hSysStatus.m_pStat->test_rf_self;

    // 0 - start
    // 1 - stop
    if(p->cur_mode == 0)
    {
        // start self rf tx
        // 0 - mark
        // 1 - space
        // 2 - dot
        if(p->out_select == 0)
        {
            SetFskTxSendMode(pFSK_Tx, TX_STATE_MARK_OUT);
        }
        else if(p->out_select == 1)
        {
            SetFskTxSendMode(pFSK_Tx, TX_STATE_SPACE_OUT);
        }
        else if(p->out_select == 2)
        {
            SetFskTxSendMode(pFSK_Tx, TX_STATE_DOT_OUT);
        }
        pFSK_Tx->v_pp = p->vpp;
        pFSK_Tx->v_offset = p->offset;

        p->mark_cnt = 0;
        p->space_cnt = 0;
        p->tot_cnt = 0;

        p->cur_mode = 1;
    }
}

void Proc_Self_BER_Rf_Test(tFskRxMDM *pFSK_Rx, tFskTxMDM *pFSK_Tx, int final_val)
{
    RF_Self_Test_s *p = &g_hSysStatus.m_pStat->test_rf_self;

    if(p->cur_mode == 1)
    {
        // 0 - 518khz
        // 1 - 490khz
        // 2 - 4209.5khz
        if( (p->ch_select == 0 && pFSK_Rx->dFreqCh == FSK_FREQ_INT_518KHZ) ||
            (p->ch_select == 1 && pFSK_Rx->dFreqCh == FSK_FREQ_LOC_490KHZ) ||
            (p->ch_select == 2 && pFSK_Rx->dFreqCh == FSK_FREQ_LOC_42095KHZ))
        {
            p->tot_cnt += 1;
            if(p->tot_cnt >= 2)
            {
                if(final_val == 1)
                {
                    p->mark_cnt += 1;
                }
                else
                {
                    p->space_cnt += 1;
                }

                if((p->mark_cnt + p->space_cnt) == 100)
                {
                    p->cur_mode = 0;
                    pFSK_Tx->v_pp = FSK_TX_VOLTAGE_PP;
                    pFSK_Tx->v_offset = FSK_TX_VOLTAGE_OFFSET;
                    SetFskTxSendMode(pFSK_Tx, TX_STATE_STOP);
                    Internal_Tx_Rf_Self_BER_Test_Response(4, p->ch_select,
                                                         p->tot_cnt,
                                                         p->mark_cnt,
                                                         p->space_cnt );
                }
            }
        }
    }
}

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  DecideFskRxBitData(tFskRxMDM *pFSK, int nAdcVal)
{
    uint32_t ret = 0;
    uint32_t step_ret = 0;
    float32_t fRxData;
#if FSK_DEBUG_MODE == FSK_DEBUG_ON
    static int tog_int = 0;
    static int tog_loc = 0;
#endif
    int i = 0;

    CalcNewTwoOsc(&(pFSK->xSpcOSC));
    CalcNewTwoOsc(&(pFSK->xMrkOSC));

    fRxData = (float32_t)nAdcVal;

/*******************************************************************************************
  * @brief current vibrate frequency
*******************************************************************************************/
    pFSK->fSpcCos = pFSK->xSpcOSC.fCosVal * fRxData;
    pFSK->fSpcSin = pFSK->xSpcOSC.fSinVal * fRxData;

    pFSK->fMrkCos = pFSK->xMrkOSC.fCosVal * fRxData;
    pFSK->fMrkSin = pFSK->xMrkOSC.fSinVal * fRxData;

/*******************************************************************************************
  * @brief current vibrate frequency
*******************************************************************************************/
    pFSK->fSumSpcI -= pFSK->pRunSpcI[pFSK->dRunPosX]; 
    pFSK->pRunSpcI[pFSK->dRunPosX] = pFSK->fSpcCos;

    pFSK->fSumSpcQ -= pFSK->pRunSpcQ[pFSK->dRunPosX]; 
    pFSK->pRunSpcQ[pFSK->dRunPosX] = pFSK->fSpcSin;

    pFSK->fSumMrkI -= pFSK->pRunMrkI[pFSK->dRunPosX]; 
    pFSK->pRunMrkI[pFSK->dRunPosX] = pFSK->fMrkCos;

    pFSK->fSumMrkQ -= pFSK->pRunMrkQ[pFSK->dRunPosX]; 
    pFSK->pRunMrkQ[pFSK->dRunPosX] = pFSK->fMrkSin;

    ++pFSK->dRunPosX;
    if (pFSK->dRunPosX >= pFSK->dRunSize)
        pFSK->dRunPosX  = 0;

    pFSK->fSumSpcI += pFSK->fSpcCos;
    pFSK->fSumSpcQ += pFSK->fSpcSin;
    pFSK->fSumMrkI += pFSK->fMrkCos;
    pFSK->fSumMrkQ += pFSK->fMrkSin;

    pFSK->fMrkSum = (pFSK->fSumMrkI * pFSK->fSumMrkI) + (pFSK->fSumMrkQ * pFSK->fSumMrkQ);
    pFSK->fSpcSum = (pFSK->fSumSpcI * pFSK->fSumSpcI) + (pFSK->fSumSpcQ * pFSK->fSumSpcQ);

    pFSK->vBitPack[0] <<= 1;
    pFSK->vBitPack[0]  &= 0x000000FF;

    // pFSK->vBitPack[1] <<= 1;
    // pFSK->vBitPack[1] &= 0x000000FF;
    if (pFSK->fMrkSum > pFSK->fSpcSum)
    {
        pFSK->vBitPack[0] |= 0x01;
        // pFSK->vBitPack[1] |= 0x01;
        pFSK->dDetermineFreq += 1;
    }
    else
    {
        pFSK->dDetermineFreq -= 1;
    }

    // if (    pFSK->vBitPack[0] == 0x00 || 
    //         pFSK->vBitPack[0] == 0x01 || 
    //         pFSK->vBitPack[0] == 0x02 || 
    //         pFSK->vBitPack[0] == 0x04 ||
    //         pFSK->vBitPack[0] == 0x08 ||
    //         pFSK->vBitPack[0] == 0x10 ||
    //         pFSK->vBitPack[0] == 0x20 ||
    //         pFSK->vBitPack[0] == 0x40 ||
    //         pFSK->vBitPack[0] == 0x80 )
    // {
    //     pFSK->dBitDataC = 0;
    // }
    // else
    // {
    //     pFSK->dBitDataC = 1;
    // }

    if(pFSK->vBitPack[0] == 0x000000FF)
    {
        pFSK->dBitDataC = 1;
    }
    else if(pFSK->vBitPack[0] == 0x00000000)
    {
        pFSK->dBitDataC = 0;
    }

    // if(pFSK->vBitPack[1] == 0x000000FF)
    // {
    //     pFSK->dDetermineFreq += 1;
    // }
    // else if(pFSK->vBitPack[1] == 0x00000000)
    // {
    //     pFSK->dDetermineFreq -= 1;
    // }

    pFSK->dPllValue += pFSK->dPllIncrV;
    if (pFSK->dBitDataC != pFSK->dBitDataP)
    {
        if (pFSK->dPllValue < pFSK->dPllHalfV)
        {
            pFSK->dPllCalibrationV = pFSK->dPllHalfV - pFSK->dPllValue;
            if(pFSK->dFreqCh == FSK_FREQ_INT_518KHZ)
            {
                if(gNavtexRx_518KHz.SearchSeq >= SEARCH_SEQ_ONE_SPACE)
                {
                    pFSK->dPllValue += pFSK->dPllStepV; // 기준펄스를 두고 오른쪽 이동
                }
                else
                {
                    pFSK->dPllCalibrationV = (pFSK->dPllCalibrationV >= pFSK->dPllMaxCalibrationV) ? (pFSK->dPllMaxCalibrationV) : (pFSK->dPllCalibrationV);
                    pFSK->dPllValue += pFSK->dPllCalibrationV; // 기준펄스를 두고 오른쪽 이동
                }
            }
            else if(pFSK->dFreqCh == FSK_FREQ_LOC_490KHZ)
            {
                if(gNavtexRx_490KHz.SearchSeq >= SEARCH_SEQ_ONE_SPACE)
                {
                    pFSK->dPllValue += pFSK->dPllStepV; // 기준펄스를 두고 오른쪽 이동
                }
                else
                {
                    pFSK->dPllCalibrationV = (pFSK->dPllCalibrationV >= pFSK->dPllMaxCalibrationV) ? (pFSK->dPllMaxCalibrationV) : (pFSK->dPllCalibrationV);
                    pFSK->dPllValue += pFSK->dPllCalibrationV; // 기준펄스를 두고 오른쪽 이동
                }
            }
            else if(pFSK->dFreqCh == FSK_FREQ_LOC_42095KHZ)
            {
                if(gNavtexRx_490KHz.SearchSeq >= SEARCH_SEQ_ONE_SPACE)
                {
                    pFSK->dPllValue += pFSK->dPllStepV; // 기준펄스를 두고 오른쪽 이동
                }
                else
                {
                    pFSK->dPllCalibrationV = (pFSK->dPllCalibrationV >= pFSK->dPllMaxCalibrationV) ? (pFSK->dPllMaxCalibrationV) : (pFSK->dPllCalibrationV);
                    pFSK->dPllValue += pFSK->dPllCalibrationV; // 기준펄스를 두고 오른쪽 이동
                }
            }
        }
        else if(pFSK->dPllValue > pFSK->dPllHalfV)
        {
            pFSK->dPllCalibrationV = pFSK->dPllValue - pFSK->dPllHalfV;
            if(pFSK->dFreqCh == FSK_FREQ_INT_518KHZ)
            {
                if(gNavtexRx_518KHz.SearchSeq >= SEARCH_SEQ_ONE_SPACE)
                {
                    pFSK->dPllValue -= pFSK->dPllStepV; // 기준펄스를 두고 오른쪽 이동
                }
                else
                {
                    pFSK->dPllCalibrationV = (pFSK->dPllCalibrationV >= pFSK->dPllMaxCalibrationV) ? (pFSK->dPllMaxCalibrationV) : (pFSK->dPllCalibrationV);
                    pFSK->dPllValue -= pFSK->dPllCalibrationV; // 기준펄스를 두고 왼쪽 이동
                }
            }
            else if(pFSK->dFreqCh == FSK_FREQ_LOC_490KHZ)
            {
                if(gNavtexRx_490KHz.SearchSeq >= SEARCH_SEQ_ONE_SPACE)
                {
                    pFSK->dPllValue -= pFSK->dPllStepV; // 기준펄스를 두고 왼쪽 이동
                }
                else
                {
                    pFSK->dPllCalibrationV = (pFSK->dPllCalibrationV >= pFSK->dPllMaxCalibrationV) ? (pFSK->dPllMaxCalibrationV) : (pFSK->dPllCalibrationV);
                    pFSK->dPllValue -= pFSK->dPllCalibrationV; // 기준펄스를 두고 왼쪽 이동
                }
            }
            else if(pFSK->dFreqCh == FSK_FREQ_LOC_42095KHZ)
            {
                if(gNavtexRx_490KHz.SearchSeq >= SEARCH_SEQ_ONE_SPACE)
                {
                    pFSK->dPllValue -= pFSK->dPllStepV; // 기준펄스를 두고 왼쪽 이동
                }
                else
                {
                    pFSK->dPllCalibrationV = (pFSK->dPllCalibrationV >= pFSK->dPllMaxCalibrationV) ? (pFSK->dPllMaxCalibrationV) : (pFSK->dPllCalibrationV);
                    pFSK->dPllValue -= pFSK->dPllCalibrationV; // 기준펄스를 두고 왼쪽 이동
                }
            }
        }
    }
    pFSK->dBitDataP = pFSK->dBitDataC;

    for(i=0; i<FSK_SAMPLE_POINT; i++)
    {
        if(i == (FSK_SAMPLE_POINT-1))
        {
            if((pFSK->dPllValue >= pFSK->dPllSmpPt[FSK_SAMPLE_POINT-1]))
            {
                pFSK->dPllSmpPtStepC = i;
                step_ret = RxSamplePointStepCheck(pFSK);

                if(step_ret == 1)
                {
                    pFSK->dPllValue -= pFSK->dPllFullV;
                }
            }
        }
        else
        {
            if((pFSK->dPllValue >= pFSK->dPllSmpPt[i]) && (pFSK->dPllValue < pFSK->dPllSmpPt[i+1]))
            {
                pFSK->dPllSmpPtStepC = i;
                step_ret = RxSamplePointStepCheck(pFSK);

                if(i == (FSK_SAMPLE_POINT/2))
                {
                    if(step_ret == 1)
                    {
                        ret = RxSamplePointFinalResult(pFSK);
                        if(ret != 0xFF)
                        {
                            pFSK->dBitData = ret;
                            ApndFskRxBitData(pFSK, pFSK->dBitData);
                            Proc_Fsk_DeMod_Test((int)pFSK->dFreqCh, (int)pFSK->dBitData);
                            Proc_Self_BER_Rf_Test(pFSK, &G_xMsgFskTxMDM, pFSK->dBitData);

#if FSK_DEBUG_MODE == FSK_DEBUG_ON
                            if(pFSK->dFreqCh == FSK_FREQ_INT_518KHZ)
                            {
                                // tp50
                                HAL_GPIO_WritePin(TP_PG7_GPIO_Port, TP_PG7_Pin, pFSK->dBitData);

                                tog_int ^= 0x01;
                                // tp51
                                HAL_GPIO_WritePin(TP_PC8_GPIO_Port, TP_PC8_Pin, tog_int);
                            }
                            else if(pFSK->dFreqCh == FSK_FREQ_LOC_490KHZ || pFSK->dFreqCh == FSK_FREQ_LOC_42095KHZ)
                            {
                                // tp52
                                HAL_GPIO_WritePin(TP_PA15_GPIO_Port, TP_PA15_Pin, pFSK->dBitData); 

                                // tp53
                                tog_loc ^= 0x01;
                                HAL_GPIO_WritePin(TP_PD2_GPIO_Port, TP_PD2_Pin, tog_loc); 
                            }
#endif
                        }
                    }
                }
            }
        }
    }

//HAL_GPIO_WritePin(GPIOE, GPIO_PIN_3, pFSK->dBitDataC);
#if (0)
{
static float32_t fMax = -9999999999.0f;
static float32_t fMin = +9999999999.0f;
static float32_t fTmp = 0.0f;
fTmp = fMrkSum - fSpcSum;
if (fMax < fTmp)  fMax = fTmp;
if (fMin > fTmp)  fMin = fTmp;
//HAL_GPIO_WritePin(GPIOH, GPIO_PIN_1, pFSK->dBitDataC);
HAL_GPIO_WritePin(GPIOH, GPIO_PIN_1, pFSK->vBitPack[2]);
DacSetChannel2Data(G_pDac1DataP, (int)((fTmp / 5.0e8) * 2000.0f + 2000.0f));
static float32_t *pX = (float32_t *)0x30000010;
static uint32_t   dX = 0;
++dX;
if (dX < 36000)
 {
    *pX++ = fSpcSum;
    *pX++ = fMrkSum;
 }
*(float32_t *)0x30000000 = fSpcSum;
*(float32_t *)0x30000004 = fMrkSum;
*(float32_t *)0x30000008 = fMin;
*(float32_t *)0x3000000c = fMax;
}
#endif
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  RunFskRxModemISR(tFskRxMDM *pFSK, int nAdcVal)
{
      DecideFskRxBitData(pFSK, nAdcVal - CalcFskRxAdcAvrVal(pFSK, nAdcVal));
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  RunFskTxModemISR(tFskTxMDM *pFSK)
{
    static int dot_tog = 0;

    // control
    switch(pFSK->dSendMode)
    {
        case TX_STATE_STOP:
            HAL_GPIO_WritePin(DO_RF_TEST_ENABLE_GPIO_Port, DO_RF_TEST_ENABLE_Pin, (GPIO_PinState)1);
            return;
        break;

        case TX_STATE_MSG_OUT:
            HAL_GPIO_WritePin(DO_RF_TEST_ENABLE_GPIO_Port, DO_RF_TEST_ENABLE_Pin, (GPIO_PinState)0);
            if (pFSK->dTickCntr == 0)
            {
                if (pFSK->nSendHead == pFSK->nSendTail)
                {
                    pFSK->dSendMode = TX_STATE_STOP;

                    return;
                }
                else
                {
                    pFSK->dSendData = ReadFskTxBitData(pFSK);
                    pFSK->dTickCntr = pFSK->dTickValX - 1;
                    //pFSK->dTickCntr = pFSK->dTickValX;	// 20240205 Tick Counter 오류로 인하여 TX 송신 메시지 수신 하지 못하는 현상 발생 
                }
            }
            else
            {
                pFSK->dTickCntr--;
            }

            if (pFSK->dSendData)
            {
                pFSK->fVcoPhiVal += pFSK->fMrkPhiVal;
            }
            else
            {
                pFSK->fVcoPhiVal += pFSK->fSpcPhiVal;
            }
        break;

        case TX_STATE_DOT_OUT:
            HAL_GPIO_WritePin(DO_RF_TEST_ENABLE_GPIO_Port, DO_RF_TEST_ENABLE_Pin, (GPIO_PinState)0);
            if (pFSK->dTickCntr == 0)
            {
                if (pFSK->nSendHead == pFSK->nSendTail)
                {
                    dot_tog ^= 0x01;
                    ApndFskTxBitData(pFSK, dot_tog);
                    pFSK->dSendData = ReadFskTxBitData(pFSK);
                    pFSK->dTickCntr = pFSK->dTickValX - 1;
                    //pFSK->dTickCntr = pFSK->dTickValX;	// 20240205 Tick Counter 오류로 인하여 TX 송신 메시지 수신 하지 못하는 현상 발생 
                }
            }
            else
            {
                pFSK->dTickCntr--;
            }

            if (pFSK->dSendData)
            {
                pFSK->fVcoPhiVal += pFSK->fMrkPhiVal;
            }
            else
            {
                pFSK->fVcoPhiVal += pFSK->fSpcPhiVal;
            }
        break;

        case TX_STATE_MARK_OUT:
            HAL_GPIO_WritePin(DO_RF_TEST_ENABLE_GPIO_Port, DO_RF_TEST_ENABLE_Pin, (GPIO_PinState)0);
            pFSK->fVcoPhiVal += pFSK->fMrkPhiVal;
        break;

        case TX_STATE_SPACE_OUT:
            HAL_GPIO_WritePin(DO_RF_TEST_ENABLE_GPIO_Port, DO_RF_TEST_ENABLE_Pin, (GPIO_PinState)0);
            pFSK->fVcoPhiVal += pFSK->fSpcPhiVal;
        break;

        default:
        break;
    }    

    if (pFSK->fVcoPhiVal >= MAT_VAL_PI_MUL_2_F)
    {
        pFSK->fVcoPhiVal -= MAT_VAL_PI_MUL_2_F;
    }

    pFSK->fVcoAmpVal = cosf(pFSK->fVcoPhiVal);
}
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
