/**
******************************************************************************
* @file      mt25ql128aba.c
* <AUTHOR>
* @date      2023-4-19
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/
#include <stdio.h>
#include "mt25ql128aba.h"

/**
  * @brief  Return the configuration of the QSPI memory.
  * @param  pInfo pointer on the configuration structure
  * @retval QSPI memory status
  */
int32_t MT25QL128A_GetFlashInfo(MT25QL128A_Info_t *pInfo)
{
	if(pInfo == NULL)
	{
		return MT25QL128A_ERROR_INIT;
	}
	
	pInfo->FlashSize          = MT25QL128A_FLASH_SIZE;
	pInfo->EraseSectorSize    = MT25QL128A_SUBSECTOR_SIZE;
	pInfo->ProgPageSize       = MT25QL128A_PAGE_SIZE;
	pInfo->EraseSectorsNumber = (MT25QL128A_FLASH_SIZE/pInfo->EraseSectorSize);
	pInfo->ProgPagesNumber    = (MT25QL128A_FLASH_SIZE/pInfo->ProgPageSize);
	
	return MT25QL128A_OK;
}

/**
  * @brief  Polling WIP(Write In Progress) bit become to 0
  *         SPI/QPI;4-0-4
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */
int32_t MT25QL128A_AutoPollingMemReady(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode,uint32_t Timeout)
{
	QSPI_CommandTypeDef     s_command;
	QSPI_AutoPollingTypeDef s_config;
	
	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_4_LINES;
			data_mode = QSPI_DATA_4_LINES;
		}			
		else if(Mode == MT25QL128A_QUAD_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_2_LINES;
			data_mode = QSPI_DATA_2_LINES;
		}			
		else
		{
			instruction_mode = QSPI_INSTRUCTION_1_LINE;
			data_mode = QSPI_DATA_1_LINE;
		}			
		
		/* Configure automatic polling mode to wait for memory ready */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_READ_STATUS_REG_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		s_config.Match           = 0;
		s_config.MatchMode       = QSPI_MATCH_MODE_AND;
		s_config.Interval        = 0x10;
		s_config.AutomaticStop   = QSPI_AUTOMATIC_STOP_ENABLE;
		s_config.Mask            = MT25QL128A_SR_WIP;
		s_config.StatusBytesSize = 1;

		if (HAL_QSPI_AutoPolling(Ctx, &s_command, &s_config, Timeout) != HAL_OK)
		{
			return MT25QL128A_ERROR_AUTOPOLLING;
		}

		return MT25QL128A_OK;
	}
	return MT25QL128A_ERROR_UNKNOWN;
}

/**
  * @brief  This function send a Write Enable and wait it is effective.
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */

int32_t MT25QL128A_WriteEnable(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef     s_command;
	QSPI_AutoPollingTypeDef s_config;

	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if(Ctx != NULL)
	{
		// for write enable command
		if(Mode == MT25QL128A_QUAD_SPI_MODE)			instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_QUAD_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else														instruction_mode = QSPI_INSTRUCTION_1_LINE;
	
		/* Enable write operations */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_WRITE_ENABLE_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		/* Configure automatic polling mode to wait for write enabling */
		if(Mode == MT25QL128A_QUAD_SPI_MODE)		data_mode = QSPI_DATA_4_LINES;
		else if(Mode == MT25QL128A_QUAD_SPI_MODE)	data_mode = QSPI_DATA_2_LINES;
		else													data_mode = QSPI_DATA_1_LINE;
		
		s_config.Match           = MT25QL128A_SR_WREN;
		s_config.Mask            = MT25QL128A_SR_WREN;
		s_config.MatchMode       = QSPI_MATCH_MODE_AND;
		s_config.StatusBytesSize = 1;
		s_config.Interval        = 0x10;
		s_config.AutomaticStop   = QSPI_AUTOMATIC_STOP_ENABLE;

		s_command.Instruction    = MT25QL128A_READ_STATUS_REG_CMD;
		s_command.DataMode       = data_mode;

		if (HAL_QSPI_AutoPolling(Ctx, &s_command, &s_config, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_AUTOPOLLING;
		}

		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}

/**
  * @brief  This function reset the (WEL) Write Enable Latch bit.
  *         SPI/QPI; 1-0-0/4-0-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */
int32_t MT25QL128A_WriteDisable(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef     s_command;

	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_QUAD_SPI_MODE)	instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else													instruction_mode = QSPI_INSTRUCTION_1_LINE;

		/* Enable write operations */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_WRITE_DISABLE_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}
		
		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}

/**
  * @brief  
  *         
  * @param  
  * @param  
  * @retval 
  */
int32_t MT25QL128A_DummyCyclesCfg(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
  QSPI_CommandTypeDef s_command;
  uint8_t reg;

  /* Initialize the read volatile configuration register command */
  s_command.InstructionMode   = QSPI_INSTRUCTION_1_LINE;
  s_command.Instruction       = MT25QL128A_READ_VOL_CFG_REG_CMD;
  s_command.AddressMode       = QSPI_ADDRESS_NONE;
  s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
  s_command.DataMode          = QSPI_DATA_1_LINE;
  s_command.DummyCycles       = 0;
  s_command.NbData            = 1;
  s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
  s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
  s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

  /* Configure the command */
  if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
  {
    return MT25QL128A_ERROR_COMMAND;
  }

  /* Reception of the data */
  if (HAL_QSPI_Receive(Ctx, &reg, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
  {
    return MT25QL128A_ERROR_COMMAND;
  }

  /* Enable write operations */
  if (MT25QL128A_WriteEnable(Ctx,Mode) != MT25QL128A_OK)
  {
    return MT25QL128A_ERROR_COMMAND;
  }

  /* Update volatile configuration register (with new dummy cycles) */  
  s_command.Instruction = MT25QL128A_WRITE_VOL_CFG_REG_CMD;
  MODIFY_REG(reg, MT25QL128A_VCR_NB_DUMMY, (10 << POSITION_VAL(MT25QL128A_VCR_NB_DUMMY)));
      
  /* Configure the write volatile configuration register command */
  if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
  {
    return MT25QL128A_ERROR_COMMAND;
  }

  /* Transmission of the data */
  if (HAL_QSPI_Transmit(Ctx, &reg, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
  {
    return MT25QL128A_ERROR_COMMAND;
  }
  
  return MT25QL128A_OK;
}




/**
  * @brief  Writes an amount of data to the QSPI memory.
  *         SPI/QPI; 1-1-1/1-2-2/1-4-4/4-4-4
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @param  pData Pointer to data to be written
  * @param  WriteAddr Write start address
  * @param  Size Size of data to write. Range 1 ~ 256
  * @retval QSPI memory status
  */

int32_t MT25QL128A_PageProgram(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint8_t *pData, uint32_t WriteAddr, uint32_t Size)
{
	uint32_t end_addr, current_size, current_addr;
	
	if((Ctx != NULL) && (pData != NULL))
	{
		QSPI_CommandTypeDef s_command;

		/* Calculation of the size between the write address and the end of the page */
		current_size = MT25QL128A_PAGE_SIZE - (WriteAddr % MT25QL128A_PAGE_SIZE);

		/* Check if the size of the data is less than the remaining place in the page */
		if (current_size > Size)
		{
			current_size = Size;
		}

		/* Initialize the adress variables */
		current_addr = WriteAddr;
		end_addr = WriteAddr + Size;
  
		/* Initialize the program command */
		s_command.InstructionMode   = QSPI_INSTRUCTION_1_LINE;
		s_command.Instruction       = MT25QL128A_EXT_QUAD_IN_FAST_PROG_CMD;
		s_command.AddressMode       = QSPI_ADDRESS_4_LINES;
		s_command.AddressSize       = QSPI_ADDRESS_24_BITS;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = QSPI_DATA_4_LINES;
		s_command.DummyCycles       = 0;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		do
		{
			s_command.NbData = current_size;
			s_command.Address = current_addr;

			/* Enable write operations */
			if (MT25QL128A_WriteEnable(Ctx,Mode) != MT25QL128A_OK)
			{
				return MT25QL128A_ERROR_COMMAND;
			}

			/* Configure the command */
			if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
			{
				return MT25QL128A_ERROR_COMMAND;
			}

			/* Transmission of the data */
			if (HAL_QSPI_Transmit(Ctx, pData, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
			{
				return MT25QL128A_ERROR_TRANSMIT;
			}

			/* Configure automatic polling mode to wait for end of program */  
			if (MT25QL128A_AutoPollingMemReady(Ctx, Mode, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != MT25QL128A_OK)
			{
				return MT25QL128A_ERROR_AUTOPOLLING;
			}

			/* Update the address and size variables for next page programming */
    		current_addr += current_size;
    		pData += current_size;
    		current_size = ((current_addr + MT25QL128A_PAGE_SIZE) > end_addr) ? (end_addr - current_addr) : MT25QL128A_PAGE_SIZE;
		}while(current_addr < end_addr);

		return MT25QL128A_OK;
	}

	return MT25QL128A_ERROR_UNKNOWN;
 }

/**
  * @brief  Reads an amount of data from the QSPI memory on DTR mode.
  *         SPI/QPI; 1-1-1/1-1-2/1-4-4/4-4-4
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @param  pData Pointer to data to be read
  * @param  ReadAddr Read start address
  * @param  Size Size of data to read
  * @retval QSPI memory status
  */
int32_t MT25QL128A_ReadDTR(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint8_t *pData, uint32_t ReadAddr, uint32_t Size)
{
#if 0	// KPB TEST
	QSPI_CommandTypeDef s_command;
	switch(Mode)
	{
		case MT25QL128A_SPI_MODE:                /* 1-1-1 commands, Power on H/W default setting */
			s_command.InstructionMode   = QSPI_INSTRUCTION_1_LINE;
			s_command.Instruction       = MT25QL128A_FAST_READ_4_BYTE_DTR_CMD;
			s_command.AddressMode       = QSPI_ADDRESS_1_LINE;
			s_command.DataMode          = QSPI_DATA_1_LINE;
			break;
		
		case MT25QL128A_SPI_2IO_MODE:           /* 1-1-2 read commands */
			s_command.InstructionMode   = QSPI_INSTRUCTION_1_LINE;
			s_command.Instruction       = MT25QL128A_DTR_DUAL_OUT_FAST_READ_CMD;
			s_command.AddressMode       = QSPI_ADDRESS_1_LINE;
			s_command.DataMode          = QSPI_DATA_2_LINES;
			break;
		
		case MT25QL128A_SPI_4IO_MODE:             /* 1-4-4 read commands */
			s_command.InstructionMode   = QSPI_INSTRUCTION_1_LINE;
			s_command.Instruction       = MT25QL128A_QUAD_INOUT_FAST_READ_4_BYTE_DTR_CMD;
			s_command.AddressMode       = QSPI_ADDRESS_4_LINES;
			s_command.DataMode          = QSPI_DATA_4_LINES;
			break;
		
		case MT25QL128A_QPI_MODE:                 /* 4-4-4 commands */
			s_command.InstructionMode   = QSPI_INSTRUCTION_4_LINES;
			s_command.Instruction       = MT25QL128A_QUAD_INOUT_FAST_READ_DTR_CMD;
			s_command.AddressMode       = QSPI_ADDRESS_4_LINES;
			s_command.DataMode          = QSPI_DATA_4_LINES;
			break;
	}
	
	/* Initialize the read command */
	s_command.DummyCycles       = MT25QL128A_DUMMY_CYCLES_READ_QUAD_DTR;
	s_command.AddressSize       = QSPI_ADDRESS_32_BITS;
	s_command.Address           = ReadAddr;
	s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
	s_command.NbData            = Size;
	s_command.DdrMode           = QSPI_DDR_MODE_ENABLE;
	s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_HALF_CLK_DELAY;
	s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

	/* Configure the command */
	if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
	{
		return MT25QL128A_ERROR_COMMAND;
	}

	/* Reception of the data */
	if (HAL_QSPI_Receive(Ctx, pData, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
	{
		return MT25QL128A_ERROR_RECEIVE;
	}
#endif
	return MT25QL128A_OK;
}

/**
  * @brief  Reads an amount of data from the QSPI memory on STR mode.
  *         SPI/QPI; 1-1-1/1-2-2/1-4-4/4-4-4
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @param  pData Pointer to data to be read
  * @param  ReadAddr Read start address
  * @param  Size Size of data to read
  * @retval QSPI memory status
  */
int32_t MT25QL128A_ReadSTR(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint32_t ReadAddr, uint8_t *pData, uint32_t Size)
{
	if((Ctx != NULL) && (pData != NULL))
	{
		QSPI_CommandTypeDef s_command;

		/* Initialize the read command */
		s_command.InstructionMode   = QSPI_INSTRUCTION_1_LINE;
		s_command.Instruction       = MT25QL128A_QUAD_INOUT_FAST_READ_CMD;
		s_command.AddressMode       = QSPI_ADDRESS_4_LINES;
		s_command.AddressSize       = QSPI_ADDRESS_24_BITS;
		s_command.Address           = ReadAddr;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = QSPI_DATA_4_LINES;
		s_command.DummyCycles       = MT25QL_DUMMY_CYCLES_READ;
		s_command.NbData            = Size;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Configure the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			//printf("Read Command Error!!\r\n");
			return MT25QL128A_ERROR_COMMAND;
		}

		/* Reception of the data */
		if (HAL_QSPI_Receive(Ctx, pData, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			//printf("Receive Error!!\r\n");
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}

	return MT25QL128A_ERROR_UNKNOWN;
}


/**
  * @brief  Erases the specified 4KB Subsector of the QSPI memory.
  *         MT25QL128ABA support 4K, 32K, 64K size block erase commands.
  *         SPI/QPI; 1-1-0/4-4-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @param  BlockAddress Block address to erase
  * @retval QSPI memory status
  */

int32_t MT25QL128A_Sub4KSectorErase(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode ,uint32_t BlockAddress)
{
	QSPI_CommandTypeDef s_command;
	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0, address_size = 0;

	if(Ctx != NULL)
	{
		address_size = QSPI_ADDRESS_24_BITS;
		
		if(Mode == MT25QL128A_QUAD_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_4_LINES;
			data_mode = QSPI_DATA_4_LINES;
		}			
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_2_LINES;
			data_mode = QSPI_DATA_2_LINES;
		}			
		else
		{
			// KPB 20240126 - Correction of sector erase functionality inoperability
			instruction_mode = QSPI_INSTRUCTION_1_LINE;
			addres_mode = QSPI_ADDRESS_1_LINE;
			address_size = QSPI_ADDRESS_24_BITS;
			data_mode = QSPI_DATA_NONE;
		}
		
		/* Initialize the erase command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_4K_SUBSECTOR_ERASE_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AddressSize       = address_size;
		s_command.Address           = BlockAddress;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.NbData            = 0;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		if(MT25QL128A_WriteEnable(Ctx,Mode) != MT25QL128A_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}
		
		/* Send the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}
		
		if(MT25QL128A_AutoPollingMemReady(Ctx,Mode,MT25QL128A_SUBSECTOR_ERASE_MAX_TIME) != MT25QL128A_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}

	return MT25QL128A_ERROR_UNKNOWN;
}


/**
  * @brief  Erases the specified 32KB Subsector of the QSPI memory.
  *         MT25QL128ABA support 4K, 32K, 64K size block erase commands.
  *         SPI/QPI; 1-1-0/4-4-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @param  BlockAddress Block address to erase
  * @retval QSPI memory status
  */

int32_t MT25QL128A_Sub32KSectorErase(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode ,uint32_t BlockAddress)
{
	QSPI_CommandTypeDef s_command;
	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0, address_size = 0;

	if(Ctx != NULL)
	{
		address_size = QSPI_ADDRESS_24_BITS;
		
		if(Mode == MT25QL128A_QUAD_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_4_LINES;
			data_mode = QSPI_DATA_4_LINES;
		}			
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_2_LINES;
			data_mode = QSPI_DATA_2_LINES;
		}			
		else
		{
			// KPB 20240126 - Correction of sector erase functionality inoperability
			instruction_mode = QSPI_INSTRUCTION_1_LINE;
			addres_mode = QSPI_ADDRESS_1_LINE;
			address_size = QSPI_ADDRESS_24_BITS;
			data_mode = QSPI_DATA_NONE;
		}			
		
		/* Initialize the erase command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_32K_SUBSECTOR_ERASE_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AddressSize       = address_size;
		s_command.Address           = BlockAddress;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.NbData            = 0;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		if(MT25QL128A_WriteEnable(Ctx,Mode) != MT25QL128A_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}
		
		/* Send the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		if(MT25QL128A_AutoPollingMemReady(Ctx,Mode, MT25QL128A_SECTOR_ERASE_MAX_TIME) != MT25QL128A_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}

	return MT25QL128A_ERROR_UNKNOWN;
}


/**
  * @brief  Erases the specified 64KB sector of the QSPI memory.
  *         MT25QL128ABA support 4K, 32K, 64K size block erase commands.
  *         SPI/QPI; 1-1-0/4-4-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @param  BlockAddress Block address to erase
  * @retval QSPI memory status
  */

int32_t MT25QL128A_SectorErase(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode ,uint32_t BlockAddress)
{
	QSPI_CommandTypeDef s_command;
	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0, address_size = 0;

	if(Ctx != NULL)
	{
		address_size = QSPI_ADDRESS_24_BITS;
		
		if(Mode == MT25QL128A_QUAD_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_4_LINES;
			data_mode = QSPI_DATA_4_LINES;
		}			
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_2_LINES;
			data_mode = QSPI_DATA_2_LINES;
		}			
		else
		{
			// KPB 20240126 - Correction of sector erase functionality inoperability
			instruction_mode = QSPI_INSTRUCTION_1_LINE;
			addres_mode = QSPI_ADDRESS_1_LINE;
			address_size = QSPI_ADDRESS_24_BITS;
			data_mode = QSPI_DATA_NONE;
		}			
		
		/* Initialize the erase command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_SECTOR_ERASE_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AddressSize       = address_size;
		s_command.Address           = BlockAddress;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.NbData            = 0;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		if(MT25QL128A_WriteEnable(Ctx,Mode) != MT25QL128A_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}
		
		/* Send the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		if(MT25QL128A_AutoPollingMemReady(Ctx,Mode, MT25QL128A_SECTOR_ERASE_MAX_TIME) != MT25QL128A_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}

	return MT25QL128A_ERROR_UNKNOWN;
}

/**
  * @brief  Whole chip erase.
  *         SPI/QPI; 1-0-0/4-0-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */

int32_t MT25QL128A_ChipErase(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef s_command;
	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;
	
	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)			instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else														instruction_mode = QSPI_INSTRUCTION_1_LINE;
		
		/* Initialize the erase command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_BULK_ERASE_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}
	return MT25QL128A_ERROR_UNKNOWN;
}
/**
  * @brief  Read Flash Status register value
  *         SPI/QPI; 1-0-1/4-0-4
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @param  Value pointer to status register value
  * @retval QSPI memory status
  */
int32_t MT25QL128A_ReadStatusRegister(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint8_t *Value)
{
	QSPI_CommandTypeDef s_command;
	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if((Ctx != NULL) && (Value != NULL))
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_4_LINES;
			data_mode = QSPI_DATA_4_LINES;
		}			
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_2_LINES;
			data_mode = QSPI_DATA_2_LINES;
		}			
		else
		{
			instruction_mode = QSPI_INSTRUCTION_1_LINE;
			data_mode = QSPI_DATA_1_LINE;
		}			
		
		/* Initialize the read flag status register command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_READ_STATUS_REG_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.DummyCycles       = dummy_cycles;
		s_command.NbData            = 1;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Configure the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		/* Reception of the data */
		if (HAL_QSPI_Receive(Ctx,Value, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_RECEIVE;
		}

		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}

/**
  * @brief  This function put QSPI memory in QPI mode (Quad I/O) from SPI mode.
  *         SPI -> QPI; 1-x-x -> 4-4-4
  *         SPI; 1-0-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */
int32_t MT25QL128A_EnterQuadMode(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef s_command;

	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)	instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else													instruction_mode = QSPI_INSTRUCTION_1_LINE;
		
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_ENTER_QUAD_INOUT_MODE_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}
/**
  * @brief  This function put QSPI memory in SPI mode (Single I/O) from QPI mode.
  *         QPI -> SPI; 4-4-4 -> 1-x-x
  *         QPI; 4-0-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */
int32_t MT25QL128A_ExitQuadMode(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef s_command;

	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;
	
	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)	instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else													instruction_mode = QSPI_INSTRUCTION_1_LINE;
		
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_RESET_QUAD_INOUT_MODE_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}

	return MT25QL128A_ERROR_UNKNOWN;
}

int32_t MT25QL128A_EnableMemoryMappedMode(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_MemoryMappedTypeDef s_mem_mapped_cfg;
	QSPI_CommandTypeDef s_command;

	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;
	
	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else													instruction_mode = QSPI_INSTRUCTION_1_LINE;

		addres_mode = QSPI_ADDRESS_4_LINES;
		data_mode = QSPI_DATA_4_LINES;
		dummy_cycles = 10;
		
		/* Configure the command for the read instruction */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_QUAD_INOUT_FAST_READ_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AddressSize       = QSPI_ADDRESS_24_BITS;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Configure the memory mapped mode */
		s_mem_mapped_cfg.TimeOutActivation = QSPI_TIMEOUT_COUNTER_DISABLE;
		s_mem_mapped_cfg.TimeOutPeriod     = 0;

		if (HAL_QSPI_MemoryMapped(Ctx, &s_command, &s_mem_mapped_cfg) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}


/**
  * @brief  Flash reset enable command
  *         SPI/QPI; 1-0-0, 4-0-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */
int32_t MT25QL128A_ResetEnable(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef s_command;
	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)	instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else													instruction_mode = QSPI_INSTRUCTION_1_LINE;
		
		/* Initialize the reset enable command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_RESET_ENABLE_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Send the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}


/**
  * @brief  Flash reset memory command
  *         SPI/QPI; 1-0-0, 4-0-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */
int32_t MT25QL128A_ResetMemory(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef s_command;

	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;
	
	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)	instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else													instruction_mode = QSPI_INSTRUCTION_1_LINE;
		
		/* Initialize the reset enable command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_RESET_MEMORY_CMD ;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DataMode          = data_mode;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Send the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}
		
		return MT25QL128A_OK;
	}

	return MT25QL128A_ERROR_UNKNOWN;
}


/**
  * @brief  Read Flash 3 Byte IDs.
  *         Manufacturer ID, Memory type, Memory density
  *         SPI/QPI; 1-0-1/4-0-4
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @param  ID pointer to flash id value
  * @retval QSPI memory status
  */
int32_t MT25QL128A_ReadID(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint8_t *ID)
{
	QSPI_CommandTypeDef s_command;
	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t instruction = 0;
	uint32_t dummy_cycles = 0;

	if((Ctx != NULL) && (ID != NULL))
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_4_LINES;
			instruction = MT25QL128A_MULTIPLE_IO_READ_ID_CMD;
			data_mode = QSPI_DATA_4_LINES;
		}			
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_2_LINES;
			instruction = MT25QL128A_MULTIPLE_IO_READ_ID_CMD;
			data_mode = QSPI_DATA_2_LINES;
		}			
		else
		{
			instruction_mode = QSPI_INSTRUCTION_1_LINE;
			instruction = MT25QL128A_READ_ID_CMD;
			data_mode = QSPI_DATA_1_LINE;
		}			
		
		/* Initialize the read ID command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = instruction;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DataMode          = data_mode;
		s_command.NbData            = 3;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Configure the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		/* Reception of the data */
		if (HAL_QSPI_Receive(Ctx, ID, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_RECEIVE;
		}

		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}

/**
  * @brief  Program/Erases suspend. Interruption Program/Erase operations.
  *         After the device has entered Erase-Suspended mode,
  *         system can read any address except the block/sector being Program/Erased.
  *         SPI/QPI; 1-0-0/4-0-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */

int32_t MT25QL128A_ProgEraseSuspend(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef s_command;

	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)	instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else													instruction_mode = QSPI_INSTRUCTION_1_LINE;
		
		/* Initialize the read ID command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_PROG_ERASE_SUSPEND_CMD ;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DataMode          = data_mode;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Configure the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}

	return MT25QL128A_ERROR_UNKNOWN;
}

/**
  * @brief  Program/Erases resume.
  *         SPI/QPI; 1-0-0/4-0-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */
int32_t MT25QL128A_ProgEraseResume(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef s_command;

	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)	instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else													instruction_mode = QSPI_INSTRUCTION_1_LINE;
		
		/* Initialize the read ID command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_PROG_ERASE_RESUME_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DataMode          = data_mode;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Configure the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}

/**
  * @brief  Deep power down.
  *         The device is not active and all Write/Program/Erase instruction are ignored.
  *         SPI/QPI; 1-0-0/4-0-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */
int32_t MT25QL128A_EnterDeepPowerDown(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef s_command;
	
	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)	instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else													instruction_mode = QSPI_INSTRUCTION_1_LINE;
		
		/* Initialize the read ID command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_ENTER_DEEP_POWER_DOWN_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DataMode          = data_mode;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Configure the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}

/**
  * @brief  Release from deep power down.
  *         After CS# go high, system need wait tRES1 time for device ready.
  *         SPI/QPI; 1-0-0/4-0-0
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @retval QSPI memory status
  */
int32_t MT25QL128A_ReleaseFromDeepPowerDown(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode)
{
	QSPI_CommandTypeDef s_command;

	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)			instruction_mode = QSPI_INSTRUCTION_4_LINES;
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)		instruction_mode = QSPI_INSTRUCTION_2_LINES;
		else														instruction_mode = QSPI_INSTRUCTION_1_LINE;
		
		/* Initialize the read ID command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_RELEASE_FROM_DEEP_POWER_DOWN_CMD ;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DataMode          = data_mode;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Configure the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}

/**
  * @brief  Read SECTOR PROTECTION Block register value.
  *         SPI; 1-0-1
  * @param  Ctx Component object pointer
  * @param  Mode Interface mode
  * @param  SPBRegister pointer to SPBRegister value
  * @retval QSPI memory status
  */
int32_t MT25QL128A_ReadSPBLockRegister(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint8_t *SPBRegister)
{
	QSPI_CommandTypeDef s_command;
	
	uint32_t instruction_mode = QSPI_INSTRUCTION_NONE, addres_mode = QSPI_ADDRESS_NONE, data_mode = QSPI_DATA_NONE;
	uint32_t dummy_cycles = 0;

	if(Ctx != NULL)
	{
		if(Mode == MT25QL128A_QUAD_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_4_LINES;
			data_mode = QSPI_DATA_4_LINES;
		}
		else if(Mode == MT25QL128A_DUAL_SPI_MODE)
		{
			instruction_mode = QSPI_INSTRUCTION_2_LINES;
			data_mode = QSPI_DATA_2_LINES;
		}
		else
		{
			instruction_mode = QSPI_INSTRUCTION_1_LINE;
			data_mode = QSPI_DATA_1_LINE;
		}

		/* Initialize the reading of SPB lock register command */
		s_command.InstructionMode   = instruction_mode;
		s_command.Instruction       = MT25QL128A_READ_SECTOR_PROTECTION_CMD;
		s_command.AddressMode       = addres_mode;
		s_command.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
		s_command.DummyCycles       = dummy_cycles;
		s_command.DataMode          = data_mode;
		s_command.NbData            = 1;
		s_command.DdrMode           = QSPI_DDR_MODE_DISABLE;
		s_command.DdrHoldHalfCycle  = QSPI_DDR_HHC_ANALOG_DELAY;
		s_command.SIOOMode          = QSPI_SIOO_INST_EVERY_CMD;

		/* Configure the command */
		if (HAL_QSPI_Command(Ctx, &s_command, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_COMMAND;
		}

		/* Reception of the data */
		if (HAL_QSPI_Receive(Ctx, SPBRegister, HAL_QPSI_TIMEOUT_DEFAULT_VALUE) != HAL_OK)
		{
			return MT25QL128A_ERROR_RECEIVE;
		}

		return MT25QL128A_OK;
	}
	
	return MT25QL128A_ERROR_UNKNOWN;
}

