/**
******************************************************************************
* @file      ext_comm_ctrl.h
* <AUTHOR>
* @date      2023-7-18
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/


#ifndef _EXTERNAL_COMM_CONTROL_H_
#define _EXTERNAL_COMM_CONTROL_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32h7xx_hal.h"	/* for data type(uint8_t, uint32_t .....) */
#include "ext_comm_frame_type.h"
//#include "sentences.h"

#define EX_COM_MAX_LEN 256

typedef struct{
	uint8_t bReceiving;					// Flag for receiving check
	int nRCnt;								// Receive count
	uint8_t rx[EX_COM_MAX_LEN];
	uint8_t tx[EX_COM_MAX_LEN];
}eCom_data_s;

#ifdef __cplusplus
}
#endif

#endif	/* _EXTERNAL_COMM_CONTROL_H_ */

