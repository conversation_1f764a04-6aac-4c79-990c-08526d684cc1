/**
******************************************************************************
* @file      Common.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "Common.h"

/**
  * @brief  
  * @param  
  * @retval 
  */
u32 getMovingAvgFiltered(u32 *pArrVal, u8 *pFlagHold, u32 val, u32 cnt)
{
	static u8 curCnt = 0;	
	u32 maxVal = 0;
	u8 i = 0;
	u8 numAvg = 0;

	pArrVal[curCnt++] = val;
	if(curCnt >= cnt)
	{
		*pFlagHold = 1;
		curCnt = 0;
	}

	if(*pFlagHold == 1)
	{
		numAvg = cnt;
	}
	else
	{
		numAvg = curCnt;
	}

	for(i=0; i<numAvg; i++)
	{
		maxVal += pArrVal[i];
	}

	return (maxVal / (u32)numAvg);
}

uint16_t crc16_calc(const uint16_t init_crc, const void *p, uint32_t len)
{
    const uint8_t *p_data = p;
    uint16_t crc = init_crc;
    uint32_t i;

    for (i = 0; i < len; i++) {
        crc  = (uint8_t)(crc >> 8) | (crc << 8);
        crc ^= p_data[i];
        crc ^= (uint8_t)(crc & 0xFF) >> 4;
        crc ^= (crc << 8) << 4;
        crc ^= ((crc & 0xFF) << 4) << 1;
    }

    return crc;
}

int Common_Tick_Period_Timer(int *p_tick, int stndard_tick)
{
	int ret = 0;

	*p_tick += 1;
	if(*p_tick == 1)
	{
		ret = 1;
	} 
	else if(*p_tick >= stndard_tick)
	{
		*p_tick = 0;
	}

	return ret;
}

int Common_Tick_End_Timer(int *p_tick, int stndard_tick)
{
	int ret = 0;

	*p_tick += 1;
	if(*p_tick >= stndard_tick)
	{
		ret = 1;
		*p_tick = 0;
	} 

	return ret;
}



