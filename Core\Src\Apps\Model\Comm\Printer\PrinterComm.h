/**
******************************************************************************
* @file      PrinterComm.h
* <AUTHOR>
* @date      2024-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_PRINTERCOMM_H_
#define SRC_APPS_MODEL_COMM_PRINTERCOMM_H_

#include "Common.h"
#include "NavModem.h"

// typedef struct
// {
//     u8 cmd[10];
// }sPrinterCmdVar;

void Printer_Eth_Send_Data(u8 *pBuff, u16 len);

#endif /* SRC_APPS_MODEL_COMM_PRINTERCOMM_H_ */
