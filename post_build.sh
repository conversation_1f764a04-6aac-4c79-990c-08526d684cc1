#!/bin/bash
ProjDirPath="C:/Code/NAVTEX/Firmware/receiver"
HEADER_FILE="${ProjDirPath}/Core/Src/Common/Common_Config.h"

# Main flash
MAIN_MAJOR=$(grep SYSTEM_SW_VER_MAJOR $HEADER_FILE | awk '{print $3}')
MAIN_MINOR=$(grep SYSTEM_SW_VER_MINOR $HEADER_FILE | awk '{print $3}')
MAIN_REV=$(grep SYSTEM_SW_VER_REV $HEADER_FILE | awk '{print $3}')
MAIN_RC=$(grep SYSTEM_SW_VER_RELEASE_CANDIDATE $HEADER_FILE | awk '{print $3}')
MAIN_SYSTEM_MODE=$(grep '#define SYSTEM_MODE' $HEADER_FILE | awk '{print $3}')

MAIN_VERSION="v${MAIN_MAJOR}.${MAIN_MINOR}.${MAIN_REV}-rc${MAIN_RC}"

arm-none-eabi-objcopy -O binary --only-section=.meta_info_main $1.elf meta_info_main_flash.bin
arm-none-eabi-objcopy -O binary $1.elf navtex_receiver_main_flash.bin

mkdir -p Created_Bin_File
#cat meta_info_main_flash.bin navtex_receiver_main_flash.bin > Created_Bin_File/navtex_receiver_main_firmware_${MAIN_VERSION}.bin
cp navtex_receiver_main_flash.bin Created_Bin_File/navtex_receiver_main_flash_${MAIN_SYSTEM_MODE}_${MAIN_VERSION}.bin