/**
******************************************************************************
* @file      TargetFSK.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_DRIVERS_TARGETFSK_H_
#define SRC_DRIVERS_TARGETFSK_H_

#if NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BASE_1_785KHZ
#define FSK_SAMPLE_FREQ 24000
#define FSK_BPS         100

#define FSK_SPACE_FREQ  1785
#define FSK_MARK_FREQ   1615

#define FSK_BUFFER_SIZE 1024
#define FSK_ADC_SIZE    (FSK_SAMPLE_FREQ/200)
#elif NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BAND_PASS_10KHZ
#define FSK_SAMPLE_FREQ 40000
#define FSK_BPS         100

#define FSK_SPACE_FREQ  10085
#define FSK_MARK_FREQ   9915

#define FSK_BUFFER_SIZE 256
#define FSK_ADC_SIZE    512
#endif

void TargetFSK_Init(void);
void FSK_Msg_Rx_Run(void);
void FSK_Msg_Tx_Run(void);

#endif /* SRC_DRIVERS_TARGETFSK_H_ */
