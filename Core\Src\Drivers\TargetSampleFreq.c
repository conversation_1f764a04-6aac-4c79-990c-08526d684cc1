/**
******************************************************************************
* @file      TargetSampleFreq.c
* <AUTHOR>
* @date      2024-05-10
* @brief
******************************************************************************
* @attention
*
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetBoard.h"

extern TIM_HandleTypeDef htim3;

/**
  * @brief  
  * @param  
  * @retval 
  */
void TargetSampleFreq_Set_SamplePWM(u8 EN)
{
	// Timer3 Clock = APB1 Timer Clock
	// PWM Frequency = ((Timer3 Clock/Counter Period))/(Prescaler+1)
	// Sample Frequency PWM 24KHz = (240MHz/(99+1))/(99+1)
	// Duty Ratio = Pulse value/(Counter Period + 1)
	
	TIM_OC_InitTypeDef sConfigOC = {0};

    if(EN == 0)
    {
        HAL_TIM_PWM_Stop(&htim3, TIM_CHANNEL_1);
    }
    else if(EN == 1)
    {
        sConfigOC.OCMode = TIM_OCMODE_PWM1;
        sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
        sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;

        sConfigOC.Pulse = ((50 * (htim3.Init.Period+1))/100);

        if (HAL_TIM_PWM_ConfigChannel(&htim3, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
        {
            Error_Handler();
        }
        HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1);
    }
}

void TargetSampleFreq_Init(void)
{
    //TargetSampleFreq_Set_SamplePWM(1);

    HAL_TIM_Base_Start_IT(&htim3);
}


