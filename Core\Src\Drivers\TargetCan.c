/**
******************************************************************************
* @file      TargetCan.c
* <AUTHOR>
* @date      2024-05-10
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
* hal lib callback list
    hfdcan->ClockCalibrationCallback    = HAL_FDCAN_ClockCalibrationCallback;
    hfdcan->TxEventFifoCallback         = HAL_FDCAN_TxEventFifoCallback;        
    hfdcan->RxFifo0Callback             = HAL_FDCAN_RxFifo0Callback;            
    hfdcan->RxFifo1Callback             = HAL_FDCAN_RxFifo1Callback;            
    hfdcan->TxFifoEmptyCallback         = HAL_FDCAN_TxFifoEmptyCallback;        
    hfdcan->TxBufferCompleteCallback    = HAL_FDCAN_TxBufferCompleteCallback;   
    hfdcan->TxBufferAbortCallback       = HAL_FDCAN_TxBufferAbortCallback;      
    hfdcan->RxBufferNewMessageCallback  = HAL_FDCAN_RxBufferNewMessageCallback; 
    hfdcan->HighPriorityMessageCallback = HAL_FDCAN_HighPriorityMessageCallback;
    hfdcan->TimestampWraparoundCallback = HAL_FDCAN_TimestampWraparoundCallback;
    hfdcan->TimeoutOccurredCallback     = HAL_FDCAN_TimeoutOccurredCallback;
    hfdcan->ErrorCallback               = HAL_FDCAN_ErrorCallback;          
    hfdcan->ErrorStatusCallback         = HAL_FDCAN_ErrorStatusCallback;    
    hfdcan->TT_ScheduleSyncCallback     = HAL_FDCAN_TT_ScheduleSyncCallback;
    hfdcan->TT_TimeMarkCallback         = HAL_FDCAN_TT_TimeMarkCallback;   
    hfdcan->TT_StopWatchCallback        = HAL_FDCAN_TT_StopWatchCallback;  
    hfdcan->TT_GlobalTimeCallback       = HAL_FDCAN_TT_GlobalTimeCallback; 
******************************************************************************
*/

#include "TargetBoard.h"
#include "model.h"

extern FDCAN_HandleTypeDef hfdcan1;

static FDCAN_TxHeaderTypeDef TxHeader;
static FDCAN_RxHeaderTypeDef RxHeader;

sDrvCanComponent gDrvCan;

void TargetCan_Init(void)
{
    HAL_FDCAN_Stop(&hfdcan1);
    HAL_FDCAN_DeInit(&hfdcan1);

    hfdcan1.Instance = FDCAN1;
    hfdcan1.Init.FrameFormat = FDCAN_FRAME_CLASSIC;
    hfdcan1.Init.Mode = FDCAN_MODE_NORMAL;
    hfdcan1.Init.AutoRetransmission = DISABLE;
    hfdcan1.Init.TransmitPause = DISABLE;
    hfdcan1.Init.ProtocolException = DISABLE;
    hfdcan1.Init.NominalPrescaler = 20;
    hfdcan1.Init.NominalSyncJumpWidth = 1;
    hfdcan1.Init.NominalTimeSeg1 = 15;
    hfdcan1.Init.NominalTimeSeg2 = 2;
    hfdcan1.Init.DataPrescaler = 1;
    hfdcan1.Init.DataSyncJumpWidth = 1;
    hfdcan1.Init.DataTimeSeg1 = 1;
    hfdcan1.Init.DataTimeSeg2 = 1;
    hfdcan1.Init.MessageRAMOffset = 0;
    hfdcan1.Init.StdFiltersNbr = 0;
    hfdcan1.Init.ExtFiltersNbr = 0;
    hfdcan1.Init.RxFifo0ElmtsNbr = 40;
    hfdcan1.Init.RxFifo0ElmtSize = FDCAN_DATA_BYTES_8;
    hfdcan1.Init.RxFifo1ElmtsNbr = 0;
    hfdcan1.Init.RxFifo1ElmtSize = FDCAN_DATA_BYTES_8;
    hfdcan1.Init.RxBuffersNbr = 0;
    hfdcan1.Init.RxBufferSize = FDCAN_DATA_BYTES_8;
    hfdcan1.Init.TxEventsNbr = 0;
    hfdcan1.Init.TxBuffersNbr = 0;
    hfdcan1.Init.TxFifoQueueElmtsNbr = 2;
    hfdcan1.Init.TxFifoQueueMode = FDCAN_TX_FIFO_OPERATION;
    hfdcan1.Init.TxElmtSize = FDCAN_DATA_BYTES_8;
    if (HAL_FDCAN_Init(&hfdcan1) != HAL_OK)
    {
        Error_Handler();
    }

    /* Component Variable Initialize */
    memset(&gDrvCan, 0, sizeof(sDrvCanComponent));

    FDCAN_FilterTypeDef sFilterConfig;
    /* Configure Rx filter */
    sFilterConfig.IdType = FDCAN_EXTENDED_ID;
    sFilterConfig.FilterIndex = 0;
    sFilterConfig.FilterType = FDCAN_FILTER_MASK;
    sFilterConfig.FilterConfig = FDCAN_FILTER_TO_RXFIFO0;
    // sFilterConfig.FilterID1 = 0x7FF;
    // sFilterConfig.FilterID2 = 0x1FFFFFFF;
    sFilterConfig.FilterID1 = 0x1FFFFFFF;
    sFilterConfig.FilterID2 = 0x1FFFFFFF;

    if (HAL_FDCAN_ConfigFilter(&hfdcan1, &sFilterConfig) != HAL_OK)
    {
        /* Filter configuration Error */
        DEBUG_MSG("[%s] ConfigFilter Error\r\n", __FUNCTION__);
    }

    // /* Configure global filter to reject all non-matching frames */
    // HAL_FDCAN_ConfigGlobalFilter(&hfdcan1, FDCAN_REJECT, FDCAN_REJECT, FDCAN_REJECT_REMOTE, FDCAN_REJECT_REMOTE);

    if (HAL_FDCAN_ActivateNotification(&hfdcan1, 
                                        FDCAN_IT_RX_FIFO0_MESSAGE_LOST|
                                        FDCAN_IT_RX_FIFO0_FULL|
                                        FDCAN_IT_RX_FIFO0_WATERMARK|
                                        FDCAN_IT_RX_FIFO0_NEW_MESSAGE|
                                        FDCAN_IT_TX_COMPLETE|
                                        FDCAN_IT_TX_FIFO_EMPTY|
                                        FDCAN_IT_TX_EVT_FIFO_FULL|
                                        FDCAN_IT_BUS_OFF, 0) != HAL_OK)
    {
        /* Notification Error */
        DEBUG_MSG("[%s] Activate Notification Fail\r\n", __FUNCTION__);
    }

    /* Start the FDCAN module */
    if (HAL_FDCAN_Start(&hfdcan1) != HAL_OK)
    {
        /* Start Error */
        DEBUG_MSG("[%s] FDCAN Start Fail\r\n", __FUNCTION__);
    }
}

void TargetCan_Test_Tx(void)
{
    u8 can_data[8] = {1,2,3,4,5,6,7,8};

    TargetCan_Direct_Send_Message(&hfdcan1, CAN_TX_ID_ALARM_UNIT_PERIODIC, can_data);
}

void Can_Direct_Send(u32 id, u8 *pData)
{
    TargetCan_Direct_Send_Message(&hfdcan1, id, pData);
}

void TargetCan_Audio_Tx_Task(void)
{
    static u8 cnt = 0;
    static u8 can_data[8] = {0, };
    int data = -1;
    
    do
    {
        data = G729_Encode_Read_Out_Data_Buffer();
        if(data == -1)
        {
            if(cnt == 2)
            {
                TargetCan_Audio_Data_Tx(can_data, 2);
                cnt = 0;
            }
            return;
        }

        can_data[cnt ++] = (u8)data;
        if(cnt >= 8)
        {
            TargetCan_Audio_Data_Tx(can_data, 8);
            cnt = 0;

            return;
        }
    } while (data != -1);
}

void TargetCan_Audio_Data_Tx(u8 *pData, u8 len)
{
    // packet 0 - first packet
    // packet 1 - second packet

    if(len == 8)
    {
        TargetCan_Direct_Send_Message(&hfdcan1, 0x321, pData);
    }
    else if(len == 2)
    {
        TargetCan_Direct_Send_Message(&hfdcan1, 0x322, pData);
    }
}

void TargetCan_Direct_Send_Message(FDCAN_HandleTypeDef *pHandle, uint32_t can_id, uint8_t *p_data)
{
    /* Prepare Tx Header */
    TxHeader.Identifier = can_id;
    TxHeader.IdType = FDCAN_EXTENDED_ID;
    TxHeader.TxFrameType = FDCAN_DATA_FRAME;
    TxHeader.DataLength = FDCAN_DLC_BYTES_8;
    TxHeader.ErrorStateIndicator = FDCAN_ESI_ACTIVE;
    TxHeader.BitRateSwitch = FDCAN_BRS_OFF;
    TxHeader.FDFormat = FDCAN_CLASSIC_CAN;
    //TxHeader.TxEventFifoControl = FDCAN_NO_TX_EVENTS;
    TxHeader.TxEventFifoControl = FDCAN_STORE_TX_EVENTS;
    TxHeader.MessageMarker = 0;

    /* Start the Transmission process */
    if (HAL_FDCAN_AddMessageToTxFifoQ(&hfdcan1, &TxHeader, p_data) != HAL_OK)
    {
        /* Transmission request Error */
        DEBUG_MSG("[%s] Can Tx Error\r\n", __FUNCTION__);
    }
}


/**
  * @brief  Rx FIFO 0 callback.
  * @param  hfdcan: pointer to an FDCAN_HandleTypeDef structure that contains
  *         the configuration information for the specified FDCAN.
  * @param  RxFifo0ITs: indicates which Rx FIFO 0 interrupts are signalled.
  *                     This parameter can be any combination of @arg FDCAN_Rx_Fifo0_Interrupts.
  * @retval None
  */
void HAL_FDCAN_RxFifo0Callback(FDCAN_HandleTypeDef *hfdcan, uint32_t RxFifo0ITs)
{
    if(RxFifo0ITs == FDCAN_IE_RF0NE)
    {
        /* Retrieve Rx messages from RX FIFO0 */
        if (HAL_FDCAN_GetRxMessage(hfdcan, FDCAN_RX_FIFO0, &RxHeader, gDrvCan.RxData) != HAL_OK)
        {
            /* Reception Error */
            DEBUG_MSG("[%s] Can ISR_0 Rx Error\r\n", __FUNCTION__);
            return;
        }
           
        if(RxHeader.Identifier == CAN_RX_ID_ALARM_UNIT_PERIODIC || RxHeader.Identifier == CAN_RX_ID_ALARM_UNIT_EVENT)
        {
            // DEBUG_MSG("[%s] Can Rx ISR_0 ID:%08xh Data:%02xh, %02xh, %02xh, %02xh, %02xh, %02xh, %02xh, %02xh\r\n", __FUNCTION__,
            //                                                                                                     RxHeader.Identifier,
            //                                                                                                     gDrvCan.RxData[0],
            //                                                                                                     gDrvCan.RxData[1],
            //                                                                                                     gDrvCan.RxData[2],
            //                                                                                                     gDrvCan.RxData[3],
            //                                                                                                     gDrvCan.RxData[4],
            //                                                                                                     gDrvCan.RxData[5],
            //                                                                                                     gDrvCan.RxData[6],
            //                                                                                                     gDrvCan.RxData[7] );

            Internal_Tx_Can_Received_Data(RxHeader.Identifier, gDrvCan.RxData, 8);
        }
    }
    else if(RxFifo0ITs == FDCAN_IT_RX_FIFO0_MESSAGE_LOST)
    {
        DEBUG_MSG("[%s] Can ISR_0 Message Lost Error\r\n", __FUNCTION__);
        CLEAR_BIT(hfdcan->Instance->CCCR, FDCAN_CCCR_INIT);
        TargetCan_Init();

        return;
    }
    else if(RxFifo0ITs == FDCAN_IT_RX_FIFO0_FULL)
    {
        DEBUG_MSG("[%s] Can ISR_0 Rx Full Error\r\n", __FUNCTION__);
        CLEAR_BIT(hfdcan->Instance->CCCR, FDCAN_CCCR_INIT);
        TargetCan_Init();

        return;
    }
    else if(RxFifo0ITs == FDCAN_IT_RX_FIFO0_WATERMARK)
    {
        DEBUG_MSG("[%s] Can ISR_0 Watermark Error\r\n", __FUNCTION__);
        CLEAR_BIT(hfdcan->Instance->CCCR, FDCAN_CCCR_INIT);
        TargetCan_Init();

        return;
    }

    if (HAL_FDCAN_ActivateNotification(hfdcan, FDCAN_IT_RX_FIFO0_NEW_MESSAGE, 0) != HAL_OK)
	{
	  /* Notification Error */
    	DEBUG_MSG("[%s] Can ISR_0 Rx New Message Error Error\r\n", __FUNCTION__);
	}
}

/**
  * @brief  Tx FIFO Empty callback.
  * @param  hfdcan pointer to an FDCAN_HandleTypeDef structure that contains
  *         the configuration information for the specified FDCAN.
  * @retval None
  */
void HAL_FDCAN_TxFifoEmptyCallback(FDCAN_HandleTypeDef *hfdcan)
{
    gDrvCan.TxEmptyCnt += 1;
}

/**
  * @brief  Tx Event callback.
  * @param  hfdcan pointer to an FDCAN_HandleTypeDef structure that contains
  *         the configuration information for the specified FDCAN.
  * @param  TxEventFifoITs indicates which Tx Event FIFO interrupts are signaled.
  *         This parameter can be any combination of @arg FDCAN_Tx_Event_Fifo_Interrupts.
  * @retval None
  */
void HAL_FDCAN_TxEventFifoCallback(FDCAN_HandleTypeDef *hfdcan, uint32_t TxEventFifoITs)
{
    gDrvCan.TxEventFifoCnt += 1;
}

/**
  * @brief  Transmission Complete callback.
  * @param  hfdcan pointer to an FDCAN_HandleTypeDef structure that contains
  *         the configuration information for the specified FDCAN.
  * @param  BufferIndexes Indexes of the transmitted buffers.
  *         This parameter can be any combination of @arg FDCAN_Tx_location.
  * @retval None
  */
void HAL_FDCAN_TxBufferCompleteCallback(FDCAN_HandleTypeDef *hfdcan, uint32_t BufferIndexes)
{
    gDrvCan.TxCompleteCnt += 1;
}

/**
  * @brief  Error status callback.
  * @param  hfdcan pointer to an FDCAN_HandleTypeDef structure that contains
  *         the configuration information for the specified FDCAN.
  * @param  ErrorStatusITs indicates which Error Status interrupts are signaled.
  *         This parameter can be any combination of @arg FDCAN_Error_Status_Interrupts.
  * @retval None
  */
void HAL_FDCAN_ErrorStatusCallback(FDCAN_HandleTypeDef *hfdcan, uint32_t ErrorStatusITs)
{
    FDCAN_ProtocolStatusTypeDef protocol_status;
    if(hfdcan == &hfdcan1)
    {
        if((ErrorStatusITs & FDCAN_IT_BUS_OFF) != RESET)
        {
            gDrvCan.busoff_cnt += 1;

            HAL_FDCAN_GetProtocolStatus(hfdcan, &protocol_status);
            if(protocol_status.BusOff)
            {
                CLEAR_BIT(hfdcan->Instance->CCCR, FDCAN_CCCR_INIT);
                TargetCan_Init();
            }
        }
    }
}
