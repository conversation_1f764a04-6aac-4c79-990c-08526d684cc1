/**
******************************************************************************
* @file      TestingComm_Map.h
* <AUTHOR>
* @date      2024-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_TESTING_TESTINGCOMM_MAP_H_
#define SRC_APPS_MODEL_COMM_TESTING_TESTINGCOMM_MAP_H_

#include "Common.h"
#include "TargetUart.h"
/******************************************************************************
 * Tx 
******************************************************************************/

/******************************************************************************
 * Rx 
******************************************************************************/
typedef struct
{
    u8 Disc;
    u8 OnOff;
    u8 dummy_2;
    u8 dummy_3;
} Tcomm_Gpio_Control_Rx_t;

typedef struct
{
    u8 Disc;
    u8 OnOff;
    u8 dummy_2;
    u8 dummy_3;
} Tcomm_RF_Control_Rx_t;


/*-----------------------------------------------------------------------------*/
/*-----------------------------------------------------------------------------*/
/******************************************************************************
 * Icomm Start frame struct
******************************************************************************/
typedef struct
{
    u8 dummy_0;
    u8 dummy_1;
    u8 dummy_2;

    u8 Start;
}Tcomm_StartFrame_s;

/******************************************************************************
 * Icomm header frame struct
******************************************************************************/
typedef struct
{
    u16 Frm_Len;
    u16 Seq_Num;
    u16 Cmd;
    u16 Cmd_Param;
}Tcomm_HeaderFrame_s;

/******************************************************************************
 * Icomm data frame Tx struct
******************************************************************************/
typedef union
{
    
}Tcomm_DataFrame_Tx_s;

/******************************************************************************
 * Icomm data frame Rx struct
******************************************************************************/
typedef union
{
    Tcomm_Gpio_Control_Rx_t Gpio;
    Tcomm_RF_Control_Rx_t RF_Control;
}Tcomm_DataFrame_Rx_s;

/******************************************************************************
 * Common
******************************************************************************/
typedef union
{
    u8 Buffer[MAX_TARGET_UART_BUF_SIZE];
    struct
    {
        Tcomm_StartFrame_s st;
        Tcomm_HeaderFrame_s hd;
        Tcomm_DataFrame_Tx_s dt;
    } st;
} Tcomm_Protocol_Tx_s;

typedef union
{
    u8 Buffer[MAX_TARGET_UART_BUF_SIZE];
    struct
    {
        Tcomm_StartFrame_s st;
        Tcomm_HeaderFrame_s hd;
        Tcomm_DataFrame_Rx_s dt;
    } st;
} Tcomm_Protocol_Rx_s;

extern Tcomm_Protocol_Tx_s gTcommData_Tx;
extern Tcomm_Protocol_Rx_s gTcommData_Rx;

#endif /* SRC_APPS_MODEL_COMM_TESTING_TESTINGCOMM_MAP_H_ */
