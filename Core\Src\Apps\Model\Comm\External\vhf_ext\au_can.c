/**
******************************************************************************
* @file      nmea2000.c
* <AUTHOR>
* @date      2024-7-13
* @brief     NMEA2000과 내부 Can 통신을 위한 컨트롤러 
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
//#include "apps_data_type.h"
//#include "icom_ctrl.h"
#include "au_can.h"
#include "system_status.h"

extern FDCAN_HandleTypeDef hfdcan1;

/**
 * @brief  
 * @param  
 * @return 
*/
void eCan_au_send_conn_req_ack(can_msg_s *pMsg)
{
	can_msg_s tmp_msg;

	if(pMsg != NULL)
	{
		TBD_can_init_msg(&tmp_msg);
		tmp_msg.id = VHF_EVENT_CAN_ID;
		tmp_msg.data[ABU_CAN_MSG_IDX_CMD] = ABU_CMD_CONN_REQ_ACK;
		tmp_msg.data[ABU_CAN_MSG_IDX_ADDR_L] = pMsg->id & 0x00FF;
		tmp_msg.data[ABU_CAN_MSG_IDX_ADDR_H] = (pMsg->id & 0xFF00)>>8;
		eCan_send_msg(&tmp_msg);
	}
}

/**
 * @brief  알람 유닛  주기 메시지 처리
 * @param  
 * @return 
*/
void eCan_au_proc_rx_periodic_msg(can_msg_s *pMsg)
{
	uint8_t mode = 0, mute_stat = 0, maj = 0, min = 0, patch;
	if(pMsg != NULL)
	{
		mode = (pMsg->data[AUP_CB_IDX_MODE] & AUP_MODE_MASK);
		mute_stat = (pMsg->data[AUP_CB_IDX_MUTE_STAT] & AUP_MUTE_STATUS_MASK);
		maj = pMsg->data[AUP_CB_IDX_VER_MAJ];
		min = pMsg->data[AUP_CB_IDX_VER_MIN];
		patch = pMsg->data[AUP_CB_IDX_VER_PATCH];


		switch(mode)
		{
			case ALARM_STATUS_NONE_ALARM:
				g_hSysStatus.m_pStat->dev_stat.au.ver_maj = maj;
				g_hSysStatus.m_pStat->dev_stat.au.ver_min = min;
				g_hSysStatus.m_pStat->dev_stat.au.ver_patch = patch;

				if(g_hSysStatus.m_pStat->dev_stat.au.conn == 0)
				{
					g_hSysStatus.m_pStat->dev_stat.au.conn = 1;
				}
//				icom_send_alarm_unit_version(ICOM_TGT_CONTROLLER, g_hSysStatus.m_pStat->dev_stat.au.conn, maj, min, patch);
				//DEBUG_MSG("[AUP NONE ALARM]Maj=%d, Min=%d, Patch=%d\r\n",maj,min,patch);
				break;

			default:
				DM("Undefined alarm unit periodic message!\r\n");
				break;
		}
	}
}

/**
 * @brief  알람 유닛 이벤트 메시지 처리
 * @param  
 * @return 
*/
void eCan_au_proc_rx_event_msg(can_msg_s *pMsg)
{
	uint8_t mode = 0, dist_btn_stat = 0, dist_btn_time = 0, conn_req = 0;
	if(pMsg != NULL)
	{
		mode = (pMsg->data[AUE_CB_IDX_MODE] & AUE_MODE_MASK);
		dist_btn_time = ((pMsg->data[AUE_CB_IDX_DIST_BTN_STAT] & AUE_DIST_BTN_STAT_TICK_MASK) >> 2);
		dist_btn_stat = (pMsg->data[AUE_CB_IDX_DIST_BTN_STAT] & AUE_DIST_BTN_STAT_BTN_MASK);
		conn_req = pMsg->data[AUE_CB_IDX_CON_REQ];

		if (conn_req == AUE_CONN_REQ_REQUEST)
		{
			eCan_au_send_conn_req_ack(pMsg);
			if(g_hSysStatus.m_pStat->dev_stat.au.conn == 0)
			{
				g_hSysStatus.m_pStat->dev_stat.au.conn = 1;
			}
			DM("Received alarm unit Connection Request Message!!\r\n");
		}
		else
		{
			if(dist_btn_stat == AUE_DIST_BTN_STAT_PRESSED)
			{
			}
			else if(dist_btn_stat == AUE_DIST_BTN_STAT_RELEASED)
			{
			}
		}
	}
}



