/**
******************************************************************************
* @file      system_status.h
* <AUTHOR>
* @date      2023-09-05
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_SYSTEM_STATUS_H_
#define SRC_APPS_MODEL_SYSTEM_STATUS_H_

#include "NavModem.h"
#include "Common.h"
#include "TargetBoard.h"

/*******************************************************************************************
  * @brief Definitions
*******************************************************************************************/

/*******************************************************************************************
  * @brief Color Thema
*******************************************************************************************/
#define COLOR_THEMA_DAY     0
#define COLOR_THEMA_NIGHT   1
#define MAX_COLOR_THEMA     2
#define DEFT_COLOR_THEMA    COLOR_THEMA_DAY

/*******************************************************************************************
  * @brief Data & Time
*******************************************************************************************/
#define MIN_YEAR        2000
#define MAX_YEAR        2999
#define INVALID_YEAR    99
#define DEFT_YEAR       INVALID_YEAR

#define MIN_MONTH       1
#define MAX_MONTH       12
#define INVALID_MONTH   99
#define DEFT_MONTH      INVALID_MONTH

#define MIN_DAY         1
#define MAX_DAY         31
#define INVALID_DAY     99
#define DEFT_DAY        INVALID_DAY

#define MIN_HOUR        0
#define MAX_HOUR        23
#define INVALID_HOUR    99
#define DEFT_HOUR       INVALID_HOUR

#define MIN_MIN         0
#define MAX_MIN         59
#define INVALID_MIN     99
#define DEFT_MIN        INVALID_MIN

#define MIN_SEC         0
#define MAX_SEC         59
#define INVALID_SEC     99
#define DEFT_SEC        INVALID_SEC

#define MIN_MSI_COUNT     0
#define MAX_MSI_COUNT     200
#define INVALID_MSI_COUNT 999
#define DEFT_MSI_COUNT    INVALID_MSI_COUNT

#define MIN_BAM_COUNT     0
#define MAX_BAM_COUNT     200
#define INVALID_BAM_COUNT 999
#define DEFT_BAM_COUNT    INVALID_MSI_COUNT

/*******************************************************************************************
  * @brief System Mode
*******************************************************************************************/
typedef enum
{
    POWER_ON = 0,
    BOOTING,
    RUN,
    SLEEP,
} MODE_MAIN_TYPE;

typedef struct
{
    int Ctrl_SystemState;
    int system;
    int display_mode;
} sMode;

/*******************************************************************************************
  * @brief BAM Message
*******************************************************************************************/
typedef struct 
{
  int count;
}sBAM_Component;

/*******************************************************************************************
  * @brief GPS
*******************************************************************************************/
typedef struct
{
  u16 deg;
  u16 min;
  u16 sec;
}GPS_Coordinate_s;

typedef struct
{
    int status;
    GPS_Coordinate_s Lat;
    GPS_Coordinate_s Lon;
}sGPS_Component;

/*******************************************************************************************
  * @brief Ethernet Mac Address
*******************************************************************************************/
typedef struct
{
    u8 addr[6];
}sMac;


/*******************************************************************************************
  * @brief System Self Diagnosis
*******************************************************************************************/
typedef struct
{
    int Recv_Result_RF_518KHz;
    int Recv_Result_RF_490KHz;
    int Recv_Result_RF_4209_5KHz;

    int Msg_Recv_Mon_State;
}sDiagnosis;

/*******************************************************************************************
  * @brief FSK Demodulation Test
*******************************************************************************************/
typedef enum
{
    FSK_DEMOD_TEST_CH_NONE = 0,

    FSK_DEMOD_TEST_CH_518KHZ = FSK_DEMOD_TEST_CH_NONE,
    FSK_DEMOD_TEST_CH_490KHZ,
    FSK_DEMOD_TEST_CH_4209_5KHZ,

    FSK_DEMOD_TEST_CH_MAX,
} FSK_DeMod_Test_Ch_t;

typedef struct
{
    int ch;
    int tot_cnt;
    int cur_cnt;
    int mark_cnt;
    int space_cnt;
    int end_flag;
    int start_flag;
} FSK_DeMod_Test_s;

/*******************************************************************************************
  * @brief Version
*******************************************************************************************/
typedef struct
{
    int Receive_Hw[4];
    int Receive_Sw[4];
} Product_Version_s;

/*******************************************************************************************
  * @brief RF Self test
*******************************************************************************************/
typedef struct
{
    int out_select;
    int ch_select;
    int mode;
    int vpp;
    int offset;
    int mark_cnt;
    int space_cnt;
    int tot_cnt;

    int cur_mode;
} RF_Self_Test_s;

/*******************************************************************************************
  * @brief DAC OUT CHANNEL
*******************************************************************************************/
typedef enum
{
  DAC_OUT_CH_NONE = 0,
  DAC_OUT_CH_INT = DAC_OUT_CH_NONE,
  DAC_OUT_CH_LOC,
  DAC_OUT_CH_MAX,
} Dac_Out_Channel_t;

typedef enum {
	SELF_TEST_ITEM_NONE = 0,
	SELF_TEST_DSC_LOOP = 1,
}self_test_item_t;

typedef enum {
	SELF_TEST_STAT_NONE = 0,
	SELF_TEST_STAT_TESTING = 1,
	SELF_TEST_STAT_PASS = 2,
	SELF_TEST_STAT_FAIL = 3,
}self_test_stat_t;

typedef enum {
	SELF_TEST_RUN_MODE_NONE = 0,
	SELF_TEST_RUN_MODE_START = 1,
	SELF_TEST_RUN_MODE_STOP = 2,
}self_test_run_mode_t;

typedef struct {
	self_test_item_t test_item;
	self_test_run_mode_t test_run_mode;	
	self_test_stat_t test_status;
	uint32_t prev_ms;
}self_test_status_s;

typedef struct{
	uint8_t conn;
	uint8_t ver_maj;
	uint8_t ver_min;
	uint8_t ver_patch;
}alarm_box_status_s;

typedef struct{
	uint8_t conn;
	uint8_t ver_maj;
	uint8_t ver_min;
	uint8_t ver_patch;
}alarm_unit_status_s;

typedef struct
{
	alarm_box_status_s ab[MAX_ALARM_BOX_CNT];
	alarm_unit_status_s au;
	alarm_box_status_s lab;
}ext_dev_status_s;

/*******************************************************************************************
  * @brief System Status Component and Handler
*******************************************************************************************/
typedef struct
{
    sMode mode;
    sBAM_Component bam;
    sGPS_Component gps;
    sDiagnosis diag;
    sMac mac;
    FSK_DeMod_Test_s test_fsk[FSK_DEMOD_TEST_CH_MAX];
    TimeDateFor4byte_s timedate;
    Product_Version_s ver;
    RF_Self_Test_s test_rf_self;
    ext_dev_status_s dev_stat;		// external device status
    int system_all_para_received_flag;
    int dac_out_ch;
} SystemStatus;

typedef struct
{
  sDrvMacComponent *pMac;
  sDrvRTCComponent *pRtc;
  sDrvAdcComponent *pAdc;
}sDrvComponent;

typedef struct
{
    SystemStatus *m_pStat;
    sDrvComponent m_Drv;

    SystemStatus *(*GetSysStat)(void);

    // color thema func pointer
    void (*SetColorThema)(int nColorThema);
    int (*GetColorThema)(void);
    
    // GPS
    sGPS_Component *(*getGpsData)(void);

    // Ethernet Mac Address
    void (*setMacAddress)(sMac Mac);
    sMac (*getMacAddress)(void);

    // version
    char *(*getSwVersionString)(int *pVer);
    char *(*getHwVersionString)(int *pVer);

    // init components
    void (*InitSysStatus)(SystemStatus *pStat);
} SysStatus_Handler;


/*******************************************************************************************
  * @brief Definition Functions List
*******************************************************************************************/
void SystemStatusHandler_Init(void);

/*******************************************************************************************
  * @brief External Components
*******************************************************************************************/
extern SystemStatus        g_curSysStatus __attribute__((aligned(4)));
extern SysStatus_Handler   g_hSysStatus __attribute__((aligned(4)));

#endif /* SRC_APPS_MODEL_SYSTEM_STATUS_H_ */
