/**
******************************************************************************
* @file      nju72341.c
* <AUTHOR>
* @date      2023-4-7
* @brief     volume control driver for JRC NJU72341
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/
#include <stdio.h>
#include "nju72341.h"

static NJU72341_Context nju72341_context;

static void NJU72341_init_reg();
static void NJU72341_init_context();
static HAL_StatusTypeDef NJU72431_i2c_write(uint8_t dev_addr, uint8_t reg_addr, uint8_t data);
/**
  * @brief  
  * @param  
  * @retval 
  */
static void NJU72341_init_reg()
{
	nju72341_context.reg.volA_reg = 0x00;
	nju72341_context.reg.vol1B_reg = 0x00;
	nju72341_context.reg.vol2B_reg = 0x00;
}

/**
  * @brief  
  * @param  
  * @retval 
  */
static void NJU72341_init_context()
{
	nju72341_context.phI2c = NULL;
	nju72341_context.pMutePort = NULL;
	nju72341_context.nMutePin = -1;
	NJU72341_init_reg();
}

/**
  * @brief  
  * @param  
  * @retval 
  */
void NJU72341_init(I2C_HandleTypeDef *pHI2C, GPIO_TypeDef *pMutePort, int nMutePin)
{
	NJU72341_init_context();

	nju72341_context.phI2c = pHI2C;
	nju72341_context.pMutePort = pMutePort;
	nju72341_context.nMutePin = nMutePin;

	NJU72431_i2c_write(NJU72341_DEV_ADDR,NJU72341_REG_VOL_A,nju72341_context.reg.volA_reg);
	NJU72431_i2c_write(NJU72341_DEV_ADDR,NJU72341_REG_VOL1B,nju72341_context.reg.vol1B_reg);
	NJU72431_i2c_write(NJU72341_DEV_ADDR,NJU72341_REG_VOL2B,nju72341_context.reg.vol2B_reg);
}

/**
  * @brief  
  * @param  
  * @retval 
  */
static HAL_StatusTypeDef NJU72431_i2c_write(uint8_t dev_addr, uint8_t reg_addr, uint8_t data) 
{
	HAL_StatusTypeDef ret = HAL_ERROR;
	uint8_t addr = (dev_addr);
	uint8_t com_buf[2] = {0, 0};
	
	if(nju72341_context.phI2c != NULL)
	{
		com_buf[0] = reg_addr;
		com_buf[1] = data;

		ret = HAL_I2C_Master_Transmit(nju72341_context.phI2c, addr, com_buf, 2, NJU72341_I2C_WAIT);
	}		
	return ret;
}

/**
  * @brief  
  * @param  
  * @retval 
  */
uint8_t NJU72341_get_vol1A()
{
	return ((nju72341_context.reg.volA_reg & NJU72341_VOL1A_MASK) >> NJU72341_VOL1A_SHIFT);
}

/**
  * @brief  
  * @param  
  * @retval 
  */
uint8_t NJU72341_get_vol2A()
{
	return ((nju72341_context.reg.volA_reg & NJU72341_VOL2A_MASK) >> NJU72341_VOL2A_SHIFT);
}

/**
  * @brief  get zero cross detection value of Volume 1B register
  * @param  void
  * @retval zero cross detection value of Volume 1B register
  */
uint8_t NJU72341_get_zero1()
{
	return ((nju72341_context.reg.vol1B_reg & NJU72341_ZERO1_MASK) >> NJU72341_ZERO1_SHIFT);
}

/**
  * @brief  get zero cross detection value of Volume 2B register
  * @param  void
  * @retval zero cross detection value of Volume 2B register
  */
uint8_t NJU72341_get_zero2()
{
	return ((nju72341_context.reg.vol2B_reg & NJU72341_ZERO2_MASK) >> NJU72341_ZERO2_SHIFT);
}

/**
  * @brief  
  * @param  
  * @retval 
  */
uint8_t NJU72341_get_vol1B()
{
	return ((nju72341_context.reg.vol1B_reg & NJU72341_VOL1B_MASK) >> NJU72341_VOL1B_SHIFT);
}

/**
  * @brief  
  * @param  
  * @retval 
  */
uint8_t NJU72341_get_vol2B()
{
	return ((nju72341_context.reg.vol2B_reg & NJU72341_VOL2B_MASK) >> NJU72341_VOL2B_SHIFT);
}

/**
  * @brief  set volume 1a gain 
  * @param  vol(0x00 ~ 0x03`)
  * @retval 
  */
int NJU72341_set_vol1A(uint8_t vol)
{
	int nRet = NJU72341_ERROR;
	
	uint8_t tmpReg = (nju72341_context.reg.volA_reg & ~NJU72341_VOL1A_MASK);

	if((vol >= NJU72341_MIN_VOL1A) && (vol <= NJU72341_MAX_VOL1A))
	{
		tmpReg |= (vol << NJU72341_VOL1A_SHIFT);

		if(NJU72431_i2c_write(NJU72341_DEV_ADDR,NJU72341_REG_VOL_A,tmpReg) == HAL_OK)
		{
			nju72341_context.reg.volA_reg = tmpReg;
			nRet = NJU72341_OK;
		}
	}
	return nRet;
}


/**
  * @brief  
  * @param  
  * @retval 
  */
int NJU72341_set_vol2A(uint8_t vol)
{
	int nRet = NJU72341_ERROR;
	
	uint8_t tmpReg = (nju72341_context.reg.volA_reg & ~NJU72341_VOL2A_MASK);

	if((vol >= NJU72341_MIN_VOL2A) && (vol <= NJU72341_MAX_VOL2A))
	{
		tmpReg |= (vol << NJU72341_VOL2A_SHIFT);

		if(NJU72431_i2c_write(NJU72341_DEV_ADDR,NJU72341_REG_VOL_A,tmpReg) == HAL_OK)
		{
			nju72341_context.reg.volA_reg = tmpReg;
			nRet = NJU72341_OK;
		}
	}
	return nRet;
}


/**
  * @brief  
  * @param  
  * @retval 
  */
int NJU72341_set_zero1(uint8_t bOnOff)
{
	int nRet = NJU72341_ERROR;
	uint8_t tmpReg = (nju72341_context.reg.vol1B_reg & NJU72341_VOL1B_MASK);
	
	if((bOnOff == NJU72341_ON) || (bOnOff == NJU72341_OFF))
	{
		tmpReg |= (bOnOff << NJU72341_ZERO1_SHIFT);

		if(NJU72431_i2c_write(NJU72341_DEV_ADDR,NJU72341_REG_VOL1B,tmpReg) == HAL_OK)
		{
			nju72341_context.reg.vol1B_reg = tmpReg;
			nRet = NJU72341_OK;
		}
	}

	return nRet;
}


/**
  * @brief  
  * @param  
  * @retval 
  */
int NJU72341_set_zero2(uint8_t bOnOff)
{
	int nRet = NJU72341_ERROR;
	uint8_t tmpReg = (nju72341_context.reg.vol2B_reg & NJU72341_VOL2B_MASK);
	
	if((bOnOff == NJU72341_ON) || (bOnOff == NJU72341_OFF))
	{
		tmpReg |= (bOnOff << NJU72341_ZERO2_SHIFT);

		if(NJU72431_i2c_write(NJU72341_DEV_ADDR,NJU72341_REG_VOL2B,tmpReg) == HAL_OK)
		{
			nju72341_context.reg.vol2B_reg = tmpReg;
			nRet = NJU72341_OK;
		}
	}

	return nRet;
}

/**
  * @brief  
  * @param  
  * @retval 
  */
int NJU72341_set_vol1B(uint8_t nVol)
{
	int nRet = NJU72341_ERROR;
	
	uint8_t tmpReg = (nju72341_context.reg.vol1B_reg & NJU72341_ZERO1_MASK);

	if((nVol >= NJU72341_MIN_VOL1B) && (nVol <= NJU72341_MAX_VOL1B))
	{
		tmpReg |= (nVol & NJU72341_VOL1B_MASK);
	}
			
	if(NJU72431_i2c_write(NJU72341_DEV_ADDR,NJU72341_REG_VOL1B,tmpReg) == HAL_OK)
	{
		nju72341_context.reg.vol1B_reg = tmpReg;
		nRet = NJU72341_OK;
	}
	
	return nRet;
}



/**
  * @brief  
  * @param  
  * @retval 
  */
int NJU72341_set_vol2B(uint8_t nVol)
{
	int nRet = NJU72341_ERROR;
	
	uint8_t tmpReg = (nju72341_context.reg.vol2B_reg & NJU72341_ZERO2_MASK);

	if((nVol >= NJU72341_MIN_VOL2B) && (nVol <= NJU72341_MAX_VOL2B))
	{
		tmpReg |= (nVol & NJU72341_VOL2B_MASK);
	}
			
	if(NJU72431_i2c_write(NJU72341_DEV_ADDR,NJU72341_REG_VOL2B,tmpReg) == HAL_OK)
	{
		nju72341_context.reg.vol2B_reg = tmpReg;
		nRet = NJU72341_OK;
	}
	
	return nRet;
}

/**
  * @brief  
  * @param  
  * @retval 
  */
void NJU72341_set_vol_mute_control(uint8_t bOnOff)
{
	if((nju72341_context.pMutePort != NULL) && (nju72341_context.nMutePin >= 0))
	{
		if(bOnOff == NJU72341_ON)	HAL_GPIO_WritePin(nju72341_context.pMutePort, nju72341_context.nMutePin, GPIO_PIN_SET);
		else								HAL_GPIO_WritePin(nju72341_context.pMutePort, nju72341_context.nMutePin, GPIO_PIN_RESET);
	}
}

