/**
******************************************************************************
* @file      TargetG729.h
* <AUTHOR>
* @date      2024-05-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_DRIVERS_TARGETG729_H_
#define SRC_DRIVERS_TARGETG729_H_

#include "Common.h"
#include "g729a.h"

#define G729_BUFFER_SIZE 240    // realiry 10ms frame, added 20ms frame

#define G729_ENCODE_IN_SIZE 80
#define G729_ENCODE_OUT_SIZE 10

#define G729_DECODE_IN_SIZE 10
#define G729_DECODE_OUT_SIZE 80

typedef struct
{
    struct
    {
        encoder_state state;
        i32 frame_size;

        i16 in_data_buffer[G729_BUFFER_SIZE];
        u16 in_data_front;
        u16 in_data_rear;

        u16 in_enc_cnt;
        i16 in_enc_buffer[G729_ENCODE_IN_SIZE];
        u8 out_enc_buffer[G729_ENCODE_OUT_SIZE];

        u8 out_data_buffer[G729_BUFFER_SIZE];
        u16 out_data_front;
        u16 out_data_rear;

        int dtx_enable;
    } encode;

    struct
    {
        decoder_state state;
        i32 frame_size;

        u8 in_data_buffer[G729_BUFFER_SIZE];
        u16 in_data_front;
        u16 in_data_rear;

        u16 in_dec_cnt;
        u8 in_dec_buffer[G729_DECODE_IN_SIZE];
        i16 out_dec_buffer[G729_DECODE_OUT_SIZE];

        i16 out_data_buffer[G729_BUFFER_SIZE];
        u16 out_data_front;
        u16 out_data_rear;
    } decode;   
} sDrvG729Component;

extern sDrvG729Component gDrvG729;

int G729_Encode_Apnd_Data_Buffer(i16 i_data);
i32 G729_Encode_Read_Data_Buffer(void);

int G729_Encode_Apnd_Out_Data_Buffer(u8 *pData, u8 len);
int G729_Encode_Read_Out_Data_Buffer(void);

int G729_Decode_Apnd_Data_Buffer(u8 *pData, u8 len);
i32 G729_Decode_Read_Data_Buffer(void);

int G729_Decode_Apnd_Out_Data_Buffer(i16 *pData, u8 len);
i32 G729_Decode_Read_Out_Data_Buffer(void);

void G729_Encode_Run(void);
void G729_Decode_Run(void);

void TargetG729_Encode_Init(void);
void TargetG729_Decode_Init(void);
void TargetG729_Init(void);


#endif /* SRC_DRIVERS_TARGETG729_H_ */
