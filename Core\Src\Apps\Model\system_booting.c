/**
******************************************************************************
* @file      system_booting.c
* <AUTHOR>
* @date      2023-11-07
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "model.h"
#include "TargetBoard.h"

//extern RTC_HandleTypeDef hrtc;
//extern osThreadId EthernetTaskHandle;

extern sNavtexTx gNavtexTx;

void System_Parameter_Setting_Send_to_Control(void)
{
    static int seq = 0;
    int status = 0;

    if(Get_Rx_Icomm_Handshake_Status(I_COMM_RX_TYPE_SYSTEM_PARAMETER_GETTING_REQUEST) == COMM_STATE_SET)
    {
        if(seq == 0)
        {
            Reset_Tx_Icomm_Handshake_Status(I_COMM_TX_SYSTEM_ALL_PARAMETER_SEND);
            Internal_Tx_System_All_Parameter_Send();
            seq += 1;
        }
        else if(seq == 1)
        {
            status = Get_Tx_Icomm_Handshake_Status(I_COMM_TX_SYSTEM_ALL_PARAMETER_SEND);
            if(status == COMM_STATE_SET || status == COMM_STATE_TIMEOUT)
            {
                if(status == COMM_STATE_SET)
                {
                    Reset_Tx_Icomm_Handshake_Status(I_COMM_TX_TYPE_SYSTEM_PARAMETER_SENDING_SUCCESS);
                    Internal_Tx_System_Parameter_Sending_Success();

                    DEBUG_MSG("[OK] System All Parameter send\r\n");
                    seq += 1;
                }
                else
                {
                    DEBUG_MSG("[ERROR] System All Parameter send Timeout\r\n");
                    seq = 0;
                }
            }
        }
        else if(seq == 2)
        {
            status = Get_Tx_Icomm_Handshake_Status(I_COMM_TX_TYPE_SYSTEM_PARAMETER_SENDING_SUCCESS);
            if(status == COMM_STATE_SET || status == COMM_STATE_TIMEOUT)
            {
                if(status == COMM_STATE_SET)
                {
                    DEBUG_MSG("[OK] System Parameter Sending Success\r\n");
                }
                else
                {
                    DEBUG_MSG("[ERROR] System Parameter Sending Success Timeout\r\n");
                }
                Reset_Rx_Icomm_Handshake_Status(I_COMM_RX_TYPE_SYSTEM_PARAMETER_GETTING_REQUEST, 0);
                seq = 0;
            }
        }
    }
}

void System_Booting_Run(void)
{
    static int tickCnt = 0;
    static int flag = 0;
    int result = 0;

    u8 *pSeq = (u8 *)&g_hSysStatus.m_pStat->mode.system;

    switch(*pSeq)
    {
        case SYS_BOOT_INIT:
            //osThreadResume(EhernetTaskHandle);
            //DEBUG_MSG("[%s] Ethernet Thread Resume...\r\n", __FUNCTION__);

            tickCnt += 1;
            if(tickCnt >= BOOT_TICK_2_SEC)
            {
                *pSeq = SYS_BOOT_PARAMETER_GET;
                DEBUG_MSG("[%s] SYS_BOOT_PARAMETER_GET\r\n", __FUNCTION__);

                tickCnt = 0;
            }
            
        break;

        case SYS_BOOT_PARAMETER_GET:
            if(flag == 0)
            {
                Reset_Tx_Icomm_Handshake_Status(I_COMM_TX_TYPE_SYSTEM_PARAMETER_GETTING_REQUEST);
                Reset_Rx_Icomm_Handshake_Status(I_COMM_RX_TYPE_SYSTEM_PARAMETER_SENDING_SUCCESS, 2000);
                DEBUG_MSG("[%s] System Parameter Getting Request\r\n", __FUNCTION__);
                Internal_Tx_System_Parameter_Getting_Request();

                flag = 1;
            }
            else
            {
                result = Get_Rx_Icomm_Handshake_Status(I_COMM_RX_TYPE_SYSTEM_PARAMETER_SENDING_SUCCESS);
                if(result == COMM_STATE_SET || result == COMM_STATE_TIMEOUT)
                {
                    if(result == COMM_STATE_SET)
                    {
                        *pSeq = SYS_BOOT_PRE_RUN;
                        DEBUG_MSG("[%s] SYS_BOOT_PRE_RUN\r\n", __FUNCTION__);
                    }
                    else
                    {
                        DEBUG_MSG("[%s] System Parameter Getting Request Timeout Repeat\r\n", __FUNCTION__);
                        flag = 0;
                    }
                }
            }
        break;

        case SYS_BOOT_PRE_RUN:
            *pSeq = SYS_BOOT_RUN;

            //gNavtexTx.TxEnable_Mode = FSK_TX_TYPE_DOT;

            DEBUG_MSG("[%s] SYS_BOOT_RUN\r\n", __FUNCTION__);
        break;

        case SYS_BOOT_RUN:
        break;

        case SYS_BOOT_SLEEP:
        break;

        default:
        break;
    }

    if(*pSeq >= SYS_BOOT_PARAMETER_GET)
    {
        System_Parameter_Setting_Send_to_Control();
    }
}


