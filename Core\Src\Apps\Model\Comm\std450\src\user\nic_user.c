///@file     nic_user.c
///@brief    Network Interface Card user application function code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "user/platform.h"
#include "user/nic_user.h"

#if (USE_LINUX == 1)
#include <wchar.h>
#include <winsock2.h>
#include <iphlpapi.h>
#include <Winioctl.h>
#include <ntddndis.h>

#pragma comment(lib,"iphlpapi.lib")

#else
#include "lwip/inet.h"
#endif


/// @brief	Check network interface card(NIC).
/// @brief	Get network interface card(NIC) count.
/// @return	Device total NIC count.
int get_nic_count(void)
{
    int count = 0;

#if (USE_LINUX == 1)
    FILE *fp;
    char str_result[256]="";
    char str_exec[32] = "ifconfig | grep HWaddr";
    fp = popen(str_exec, "r");

    if(fp != NULL)
    {
        while(fgets(str_result, 256, fp) != NULL)
        {
            count += 1;
        }
        pclose(fp);
    }
    else
    {
        count = -1;
    }

    if(count <= 0)
    {
        sprintf(str_exec, "%s", "ifconfig | grep ether");
        fp = popen(str_exec,"r");
        if(fp != NULL)
        {
            while(fgets(str_result, 256, fp) != NULL)
            {
                count += 1;
            }
            pclose(fp);
        }
        else
        {
            count = -1;
        }
    }
#endif

    return count;
}

/// @brief	Check network interface card(NIC).
/// @param	nic  Exist target nic.
/// @return	Enable(0) or error(-1).
int check_nic(const char *nic)
{
    int rtn = -1;

#if (USE_LINUX == 1)
    FILE *fp;
    char str_result[256]="", str_exec[256]="";

    sprintf(str_exec,"ifconfig | grep %s",nic);
    fp = popen(str_exec,"r");

    if(fp != NULL)
    {
        while(fgets(str_result, 256, fp) != NULL)
        {
            rtn = 0;
        }
        pclose(fp);
    }
#endif

    return rtn;
}

/// @brief	Check network interface card connect(NIC).
/// @param	nic  Line connect check target nic.
/// @return	Connect(0) or Disconnect(-1).
int  check_nic_connect(const char *nic)
{
    int rtn = -1;

#if (USE_LINUX == 1)
    FILE *fp;
    char str_result[256]="", str_exec[256]="";
    sprintf(str_exec,"more /sys/class/net/%s/carrier",nic);
    fp=popen(str_exec,"r");

    if(fp != NULL)
    {
        while(fgets(str_result, 256, fp) != NULL)
        {
            rtn = atoi(str_result);
            if(rtn == 1)
            {
                rtn = 0;
            }
            else
            {
                rtn = -1;
            }
        }
        pclose(fp);
    }
#endif

    return rtn;
}

/// @brief	Check network interface card connect(NIC).
/// @param	nic  Line connect check target nic.
/// @return	Connect(0) or Disconnect(-1).
int check_nic_link_status(void)
{
    //if (netif_is_up(gnetif))
    {
        return 0;
    }

    return -1;
}

/// @brief	Check network ip stat.
/// @param	ip Ping target ip.
/// @return	PING OK(0) or PING Error(-1).
int  check_ping(const char *ip)
{
    int rtn = -1;

#if (USE_LINUX == 1)
    FILE *fp;
    char str_result[256]="", str_exec[256]="";

    sprintf(str_exec, "ping -c 1 -W 1 %s | grep \"1 packets received, 0%% packet\"", ip);
    fp = popen(str_exec, "r");

    if(fp != NULL)
    {
        while(fgets(str_result, 256, fp) != NULL)
        {
            rtn = 0;
        }
        pclose(fp);
    }
#endif

    return rtn;
}

/// @brief	Check network multicast on.
/// @param	nic Checking multicast nic.
/// @return	Multicast OK(0) or Multicast OFF(-1).
int check_multicast(const char *nic)
{
    int rtn = -1;

#if (USE_LINUX == 1)
    FILE *fp;
    char str_result[256]="", str_exec[256]="";

    sprintf(str_exec, "ifconfig %s |  grep \"RUNNING MULTICAST\" ",nic);
    fp = popen(str_exec, "r");

    if(fp != NULL)
    {
        while(fgets(str_result, 256, fp) != NULL)
        {
            rtn = 0;
        }
        pclose(fp);
    }

    return rtn;
#endif
    return 0;
}

/// @brief  Set multicast
/// @param  nic Network Interface Card for 450 networking.
/// @param  set 0: OFF, 1:ON;
void set_multicast(const char* nic, int set)
{
#if (USE_LINUX == 1)
    char cmd[256] = "";
    sprintf(cmd, "ifconfig %s %s", nic, (set == 1) ? "multicast" : "-multicast");
    system(cmd);
#endif
}

/// @brief  Set multicast
/// @param  nic Network Interface Card for 450 networking.
/// @param  set 0: OFF, 1:ON;
/// @param  igmp_ver IGMP Version setting parameter
void set_igmp_ver(const char* nic, int igmp_ver)
{
#if (USE_LINUX == 1)
    char cmd[256] = "";
    sprintf(cmd, "echo %d > /proc/sys/net/ipv4/conf/%s/force_igmp_version", igmp_ver, nic);
    system(cmd);
#endif
}

