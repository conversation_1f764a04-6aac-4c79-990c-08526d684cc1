/**
******************************************************************************
* @file      system_config.c
* <AUTHOR>
* @date      2023-09-05
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "Common.h"
#include "model.h"

/*******************************************************************************************
 * @brief Variables
*******************************************************************************************/
SystemConfig        g_SysConfig __attribute__((aligned(4))) = {0};
SysConfig_Handler   g_hSysCfg __attribute__((aligned(4))) = {0};

/*******************************************************************************************
  * @brief Functions List
*******************************************************************************************/
static SystemConfig *GetSystemConfig(void);
static int InitSystemConfig(SystemConfig *pCfg);

/*******************************************************************************************
 * @brief 
 * @param
 * @return 0 : Success / -1 : Failure
*******************************************************************************************/
static int InitSystemConfig(SystemConfig *pCfg)
{
    int i = 0;
    int nRet = -1;

    if(pCfg == NULL)
    {
        return nRet;
    }

    memset(pCfg, 0x00, sizeof(SystemConfig));

    // Navtex settings
    pCfg->nav.ch_set_local = RF_RELAY_CH_490KHZ;
    pCfg->nav.ch_set_518khz_station_manual_to_auto = NAV_STATION_MODE_MANUAL;
    pCfg->nav.ch_set_490khz_station_manual_to_auto = NAV_STATION_MODE_MANUAL;
    pCfg->nav.ch_set_42095khz_station_manual_to_auto = NAV_STATION_MODE_MANUAL;
    for(i=0; i<A_TO_Z_NUM; i++) 
    {
        pCfg->nav.ch_set_518khz_station[i] = 1;
        pCfg->nav.ch_set_518khz_station_print[i] = 1;
        pCfg->nav.ch_set_518khz_station_ins[i] = 1;
        pCfg->nav.ch_set_518khz_message[i] = 1;
        pCfg->nav.ch_set_518khz_message_print[i] = 1;
        pCfg->nav.ch_set_518khz_message_ins[i] = 1;
        pCfg->nav.ch_set_518khz_automatic_station[i] = 1;

        pCfg->nav.ch_set_490khz_station[i] = 1;
        pCfg->nav.ch_set_490khz_station_print[i] = 1;
        pCfg->nav.ch_set_490khz_station_ins[i] = 1;
        pCfg->nav.ch_set_490khz_message[i] = 1;
        pCfg->nav.ch_set_490khz_message_print[i] = 1;
        pCfg->nav.ch_set_490khz_message_ins[i] = 1;
        pCfg->nav.ch_set_490khz_automatic_station[i] = 1;

        pCfg->nav.ch_set_42095khz_station[i] = 1;
        pCfg->nav.ch_set_42095khz_station_print[i] = 1;
        pCfg->nav.ch_set_42095khz_station_ins[i] = 1;
        pCfg->nav.ch_set_42095khz_message[i] = 1;
        pCfg->nav.ch_set_42095khz_message_print[i] = 1;
        pCfg->nav.ch_set_42095khz_message_ins[i] = 1;
        pCfg->nav.ch_set_42095khz_automatic_station[i] = 1;
    }

    // Dimm settings
    pCfg->dimm.mmu_Mode = DIMM_MODE_TYPE_MANUAL;
    pCfg->dimm.nPreMode = DIMM_MODE_TYPE_MANUAL;
    pCfg->dimm.cur_Mode = DIMM_MODE_TYPE_MANUAL;
    pCfg->dimm.nValue = 100;

    // Recv GPIO settings
    pCfg->gpio.recv_rf_relay = RF_RELAY_CH_490KHZ;
    pCfg->gpio.recv_ant_power = 1;
    pCfg->gpio.recv_alarm_relay = 0;
    pCfg->gpio.recv_4m_freq_gen = 1;

    // INS settings
    pCfg->ins.serial_port = MV_VAL_ON;
    pCfg->ins.mode = MV_VAL_MANUAL;
    pCfg->ins.bps = MV_VAL_BPS_4800;
    pCfg->ins.lan_port = MV_VAL_ON;

    // BAM settings
    pCfg->bam.serial_port = MV_VAL_ON;
    pCfg->bam.mode = MV_VAL_MANUAL;
    pCfg->bam.bps = MV_VAL_BPS_4800;
    pCfg->bam.lan_port = MV_VAL_ON;

    // Print settings
    pCfg->print.model = MV_VAL_PRINT_MODEL_SRP_350PLUSIII;
    pCfg->print.mode = MV_VAL_COMM_RS232;
    pCfg->print.bps_sel = MV_VAL_PRINT_BPS_9600;
    pCfg->print.ip[0] = 192;
    pCfg->print.ip[1] = 168;
    pCfg->print.ip[2] = 0;
    pCfg->print.ip[3] = 1;

    // Network settings
    pCfg->network.mode = MV_VAL_LAN_COMM_DHCP;
    pCfg->network.ip[0] = 192;
    pCfg->network.ip[1] = 168;
    pCfg->network.ip[2] = 0;
    pCfg->network.ip[3] = 10;
    pCfg->network.netmask[0] = 255;
    pCfg->network.netmask[1] = 255;
    pCfg->network.netmask[2] = 255;
    pCfg->network.netmask[3] = 0;
    pCfg->network.gateway[0] = 192;
    pCfg->network.gateway[1] = 168;
    pCfg->network.gateway[2] = 0;
    pCfg->network.gateway[3] = 1;
    pCfg->network.static_ip[0] = 192;
    pCfg->network.static_ip[1] = 168;
    pCfg->network.static_ip[2] = 0;
    pCfg->network.static_ip[3] = 10;
    pCfg->network.static_netmask[0] = 255;
    pCfg->network.static_netmask[1] = 255;
    pCfg->network.static_netmask[2] = 255;
    pCfg->network.static_netmask[3] = 0;
    pCfg->network.static_gateway[0] = 192;
    pCfg->network.static_gateway[1] = 168;
    pCfg->network.static_gateway[2] = 0;
    pCfg->network.static_gateway[3] = 1;
    pCfg->network.sfi_id[0] = 'N';
    pCfg->network.sfi_id[1] = 'R';
    pCfg->network.sfi_id[2] = '0';
    pCfg->network.sfi_id[3] = '0';
    pCfg->network.sfi_id[4] = '0';
    pCfg->network.sfi_id[5] = '1';
    pCfg->network.sfi_id[6] = '\0';
    for(i=0; i<20; i++)
    {
        pCfg->network.tx_group[i] = 0;
        pCfg->network.rx_group[i] = 0;
    }
    pCfg->network.tx_group[MV_VAL_STD450_TG_RCOM] = 1;
    pCfg->network.tx_group[MV_VAL_STD450_TG_VDRD] = 1;
    pCfg->network.tx_group[MV_VAL_STD450_TG_PROP] = 1;
    pCfg->network.tx_group[MV_VAL_STD450_TG_BAM_1] = 1;
    pCfg->network.rx_group[MV_VAL_STD450_TG_RCOM] = 1;
    pCfg->network.rx_group[MV_VAL_STD450_TG_VDRD] = 1;
    pCfg->network.rx_group[MV_VAL_STD450_TG_PROP] = 1;
    pCfg->network.rx_group[MV_VAL_STD450_TG_BAM_1] = 1;

    // Audio settings
    pCfg->audio.key_beep_mode = MV_VAL_ON;
    pCfg->audio.key_beep_volume = 10;
    pCfg->audio.noti_mode = MV_VAL_ON;
    pCfg->audio.noti_volume = 20;
    pCfg->audio.warning_mode = MV_VAL_ON;
    pCfg->audio.warning_volume = 20;

    // Datetime settings
    pCfg->datetime.mode = MV_VAL_TIME_MODE_GPS;
    pCfg->datetime.date_type = MV_VAL_DATE_TYPE_YYYY_MM_DD;
    pCfg->datetime.time_type = MV_VAL_TIME_TYPE_24H;
    pCfg->datetime.loc_date.year = 2025;
    pCfg->datetime.loc_date.month = 1;
    pCfg->datetime.loc_date.day = 1;
    pCfg->datetime.loc_time.hour = 0;
    pCfg->datetime.loc_time.min = 0;
    pCfg->datetime.loc_time.sec = 0;
    pCfg->datetime.utc_offset.hour = 9;
    pCfg->datetime.utc_adjust_mode = MV_VAL_UTC_ADJUST_INC;

    // Antenna settings
    pCfg->antenna.type = MV_CH_ANT_TYPE_ACTIVE;

    // Alarm Relay
    pCfg->alarm_relay.status = MV_VAL_OFF;

    nRet = 0;
    return nRet;
}

/*******************************************************************************************
 * @brief Get current system config
 * @param
 * @return 
*******************************************************************************************/
static SystemConfig *GetSystemConfig(void)
{
    return g_hSysCfg.m_pCfg;
}

/*******************************************************************************************
 * @brief 
 * @param
 * @return 
*******************************************************************************************/
void SystemConfigHandler_Init(void)
{
    g_hSysCfg.m_pCfg            = &g_SysConfig;
    g_hSysCfg.InitSysCfg        = InitSystemConfig;

    g_hSysCfg.GetSysCfg         = GetSystemConfig;
    
    g_hSysCfg.InitSysCfg(&g_SysConfig);

    DEBUG_MSG("***************************************\r\n");
	DEBUG_MSG("* System Config Variable Size Check * \r\n");
	DEBUG_MSG("* Size : %d\r\n", sizeof(SystemConfig));
	DEBUG_MSG("***************************************\r\n");
}
