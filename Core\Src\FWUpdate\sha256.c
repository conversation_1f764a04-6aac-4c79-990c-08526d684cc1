/*
 * sha256.c
 *
 *  Created on: Apr 15, 2025
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#include <string.h> // memcpy, memset 사용
#include "sha256.h"

// --- 내부 상수 및 매크로 ---

// SHA-256 상수 K (미리 계산된 값)
static const uint32_t k[64] =
{
		0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1,
		0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3,
		0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786,
		0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
		0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147,
		0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13,
		0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b,
		0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
		0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a,
		0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208,
		0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
};

// 32비트 오른쪽 회전 매크로
#define ROTR(a,b) (((a) >> (b)) | ((a) << (32-(b))))

// SHA-256 논리 함수 매크로
#define Ch(x,y,z) (((x) & (y)) ^ (~(x) & (z)))
#define Maj(x,y,z) (((x) & (y)) ^ ((x) & (z)) ^ ((y) & (z)))
#define Sigma0(x) (ROTR(x, 2) ^ ROTR(x,13) ^ ROTR(x,22))
#define Sigma1(x) (ROTR(x, 6) ^ ROTR(x,11) ^ ROTR(x,25))
#define sigma0(x) (ROTR(x, 7) ^ ROTR(x,18) ^ ((x) >> 3))
#define sigma1(x) (ROTR(x,17) ^ ROTR(x,19) ^ ((x) >> 10))

// 바이트 배열 -> 32비트 워드 변환 (Big Endian)
// STM32H7은 Little Endian 이므로 변환 필요
#define BYTES_TO_U32_BE(bytes, index) (\
    ((uint32_t)((bytes)[(index)]) << 24) | \
    ((uint32_t)((bytes)[(index) + 1]) << 16) | \
    ((uint32_t)((bytes)[(index) + 2]) << 8) | \
    ((uint32_t)((bytes)[(index) + 3])))

// 32비트 워드 -> 바이트 배열 변환 (Big Endian)
#define U32_TO_BYTES_BE(u32, bytes, index) do { \
    (bytes)[(index)    ] = (uint8_t)(((u32) >> 24) & 0xFF); \
    (bytes)[(index) + 1] = (uint8_t)(((u32) >> 16) & 0xFF); \
    (bytes)[(index) + 2] = (uint8_t)(((u32) >> 8)  & 0xFF); \
    (bytes)[(index) + 3] = (uint8_t)(((u32)      ) & 0xFF); \
} while(0)

/**
 * @brief SHA-256 핵심 변환 함수 (64바이트 블록 처리)
 */
static void sha256_transform(SHA256_CTX *ctx, const uint8_t *data)
{
	uint32_t a, b, c, d, e, f, g, h, i, j, t1, t2, m[64];

	// 1. 메시지 스케줄 (W[0...63]) 생성
	for (i = 0, j = 0; i < 16; ++i, j += 4)
	{
		m[i] = BYTES_TO_U32_BE(data, j); // Big Endian으로 변환
	}
	for (; i < 64; ++i)
	{
		m[i] = sigma1(m[i - 2]) + m[i - 7] + sigma0(m[i - 15]) + m[i - 16];
	}

	// 2. 작업 변수 초기화 (현재 해시 상태 값)
	a = ctx->state[0];
	b = ctx->state[1];
	c = ctx->state[2];
	d = ctx->state[3];
	e = ctx->state[4];
	f = ctx->state[5];
	g = ctx->state[6];
	h = ctx->state[7];

	// 3. 메인 루프 (64 라운드)
	for (i = 0; i < 64; ++i)
	{
		t1 = h + Sigma1(e) + Ch(e, f, g) + k[i] + m[i];
		t2 = Sigma0(a) + Maj(a, b, c);
		h = g;
		g = f;
		f = e;
		e = d + t1;
		d = c;
		c = b;
		b = a;
		a = t1 + t2;
	}

	// 4. 해시 상태 값 업데이트
	ctx->state[0] += a;
	ctx->state[1] += b;
	ctx->state[2] += c;
	ctx->state[3] += d;
	ctx->state[4] += e;
	ctx->state[5] += f;
	ctx->state[6] += g;
	ctx->state[7] += h;
}

/**
 * @brief SHA-256 컨텍스트 초기화
 */
void sha256_init(SHA256_CTX *ctx)
{
	if (ctx == NULL)
		return; // NULL 포인터 방지

	ctx->datalen = 0;  // 현재 버퍼 길이 0
	ctx->bitlen = 0;   // 전체 비트 길이 0
	// 초기 해시 값 설정 (H0 ~ H7)
	ctx->state[0] = 0x6a09e667;
	ctx->state[1] = 0xbb67ae85;
	ctx->state[2] = 0x3c6ef372;
	ctx->state[3] = 0xa54ff53a;
	ctx->state[4] = 0x510e527f;
	ctx->state[5] = 0x9b05688c;
	ctx->state[6] = 0x1f83d9ab;
	ctx->state[7] = 0x5be0cd19;
}

/**
 * @brief SHA-256 해시 계산 데이터 업데이트
 */
void sha256_update(SHA256_CTX *ctx, const uint8_t *data, size_t len)
{
	size_t i = 0;

	if (ctx == NULL || data == NULL)
		return;

	// 입력된 데이터 길이만큼 반복
	for (i = 0; i < len; ++i)
	{
		// 데이터를 내부 버퍼에 채움
		ctx->data[ctx->datalen] = data[i];
		ctx->datalen++;

		// 버퍼가 꽉 차면 (64바이트) 변환 함수 호출
		if (ctx->datalen == SHA256_BLOCK_SIZE)
		{
			sha256_transform(ctx, ctx->data); // 핵심 변환
			ctx->bitlen += SHA256_BLOCK_SIZE * 8; // 전체 비트 길이 업데이트
			ctx->datalen = 0; // 버퍼 비움
		}
	}
}

/**
 * @brief SHA-256 최종 해시 값 계산
 */
void sha256_final(SHA256_CTX *ctx, uint8_t *hash)
{
	uint32_t i = 0;

	if (ctx == NULL || hash == NULL)
		return;

	// --- 패딩 처리 ---
	i = ctx->datalen; // 현재 버퍼에 남은 데이터 길이

	// 마지막 비트에 1 추가 (0x80)
	ctx->data[i++] = 0x80;

	// 버퍼의 남은 공간이 패딩(길이 정보 포함)에 충분하지 않으면
	if (ctx->datalen >= 56) // 64 - 8 = 56
	{
		// 현재 블록을 0으로 채우고 변환
		memset(ctx->data + i, 0, SHA256_BLOCK_SIZE - i);
		sha256_transform(ctx, ctx->data);
		// 다음 블록을 0으로 채움
		memset(ctx->data, 0, SHA256_BLOCK_SIZE);
	}
	else // 충분하면 현재 블록에 0 채우기
	{
		memset(ctx->data + i, 0, SHA256_BLOCK_SIZE - i);
	}

	// 전체 비트 길이 추가 (Big Endian)
	ctx->bitlen += ctx->datalen * 8; // 마지막 블록의 비트 길이 추가
	// 64비트 길이를 Big Endian으로 마지막 8바이트에 저장
	ctx->data[63] = (uint8_t) (ctx->bitlen);
	ctx->data[62] = (uint8_t) (ctx->bitlen >> 8);
	ctx->data[61] = (uint8_t) (ctx->bitlen >> 16);
	ctx->data[60] = (uint8_t) (ctx->bitlen >> 24);
	ctx->data[59] = (uint8_t) (ctx->bitlen >> 32);
	ctx->data[58] = (uint8_t) (ctx->bitlen >> 40);
	ctx->data[57] = (uint8_t) (ctx->bitlen >> 48);
	ctx->data[56] = (uint8_t) (ctx->bitlen >> 56);

	// 마지막 블록 변환
	sha256_transform(ctx, ctx->data);

	// 최종 해시 값(state)을 Big Endian 바이트 배열로 변환하여 저장
	for (i = 0; i < 8; ++i)
	{
		U32_TO_BYTES_BE(ctx->state[i], hash, i * 4);
	}
}

/////

