/**
******************************************************************************
* @file      TargetRTC.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetBoard.h"

extern RTC_HandleTypeDef hrtc;

sDrvRTCComponent gDrvRtc = {0};

void TBD_Rtc_Init(void)
{
    memset(&gDrvRtc, 0, sizeof(sDrvRTCComponent));
}

void TBD_Rtc_Task(void)
{
    RTC_TimeTypeDef sTime = {0};
    RTC_DateTypeDef sDate = {0};
    
    if(gDrvRtc.Flag == 1)
    {
        sTime.Hours         = gDrvRtc.Hours;
        sTime.Minutes       = gDrvRtc.Minutes;
        sTime.Seconds       = gDrvRtc.Seconds;
        HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BIN);

        sDate.Year          = gDrvRtc.Year;
        sDate.Month         = gDrvRtc.Month;
        sDate.Date          = gDrvRtc.Date;
        HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

        gDrvRtc.Flag = 0;
    }
}
