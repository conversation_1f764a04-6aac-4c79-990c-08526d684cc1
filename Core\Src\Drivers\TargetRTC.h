/**
******************************************************************************
* @file      TargetRTC.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_DRIVERS_TARGETRTC_H_
#define SRC_DRIVERS_TARGETRTC_H_

#include "Common.h"

typedef struct 
{
    u8 Flag;
    u8 Hours;
    u8 Minutes;
    u8 Seconds;
    u8 Year;
    u8 Month;
    u8 WeekDay;
    u8 Date;
}sDrvRTCComponent;
extern sDrvRTCComponent gDrvRtc;

void TBD_Rtc_Init(void);

void TBD_Rtc_Task(void);

#endif /* SRC_DRIVERS_TARGETRTC_H_ */
