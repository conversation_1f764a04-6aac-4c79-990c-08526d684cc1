/**
******************************************************************************
* @file      mt48lc16m16a2.h
* <AUTHOR>
* @date      2023-3-31
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef __MT48LC16M16A2_H__
#define __MT48LC16M16A2_H__

#ifdef __cplusplus
 extern "C" {
#endif

#include "stm32h7xx_hal.h"

//#define MT48LC16M16A2_REFRESH_COUNT			((uint32_t)0x0396)   /* SDRAM refresh counter (120Mhz SDRAM clock) */
#define MT48LC16M16A2_REFRESH_COUNT			((uint32_t)0x0493)     /* SDRAM refresh counter (150Mhz SDRAM clock) */

#define MT48LC16M16A2_TIMEOUT             ((uint32_t)0xFFFF)

typedef struct
 {
   uint32_t TargetBank;           /*!< Target Bank                             */
   uint32_t RefreshMode;          /*!< Refresh Mode                            */
   uint32_t RefreshRate;          /*!< Refresh Rate                            */
   uint32_t BurstLength;          /*!< Burst Length                            */
   uint32_t BurstType;            /*!< Burst Type                              */
   uint32_t CASLatency;           /*!< CAS Latency                             */
   uint32_t OperationMode;        /*!< Operation Mode                          */
   uint32_t WriteBurstMode;       /*!< Write Burst Mode                        */
 } MT48LC16M16A2_Context_t;	

#define MT48LC16M16A2_OK                         (0)
 #define MT48LC16M16A2_ERROR                      (-1)
	
	 /* Register Mode */
 #define MT48LC16M16A2_BURST_LENGTH_1              0x00000000U
 #define MT48LC16M16A2_BURST_LENGTH_2              0x00000001U
 #define MT48LC16M16A2_BURST_LENGTH_4              0x00000002U
 #define MT48LC16M16A2_BURST_LENGTH_8              0x00000004U
 #define MT48LC16M16A2_BURST_TYPE_SEQUENTIAL       0x00000000U
 #define MT48LC16M16A2_BURST_TYPE_INTERLEAVED      0x00000008U
 #define MT48LC16M16A2_CAS_LATENCY_2               0x00000020U
 #define MT48LC16M16A2_CAS_LATENCY_3               0x00000030U
 #define MT48LC16M16A2_OPERATING_MODE_STANDARD     0x00000000U
 #define MT48LC16M16A2_WRITEBURST_MODE_PROGRAMMED  0x00000000U
 #define MT48LC16M16A2_WRITEBURST_MODE_SINGLE      0x00000200U
	
	 /* Command Mode */
 #define MT48LC16M16A2_NORMAL_MODE_CMD             0x00000000U
 #define MT48LC16M16A2_CLK_ENABLE_CMD              0x00000001U
 #define MT48LC16M16A2_PALL_CMD                    0x00000002U
 #define MT48LC16M16A2_AUTOREFRESH_MODE_CMD        0x00000003U
 #define MT48LC16M16A2_LOAD_MODE_CMD               0x00000004U
 #define MT48LC16M16A2_SELFREFRESH_MODE_CMD        0x00000005U
 #define MT48LC16M16A2_POWERDOWN_MODE_CMD          0x00000006U
	
/** @addtogroup MT48LC16M16A2_Private_Functions
* @{
*/
int32_t MT48LC16M16A2_Init(SDRAM_HandleTypeDef *Ctx, MT48LC16M16A2_Context_t *pRegMode);
int32_t MT48LC16M16A2_ClockEnable(SDRAM_HandleTypeDef *Ctx, uint32_t Interface);
int32_t MT48LC16M16A2_Precharge(SDRAM_HandleTypeDef *Ctx, uint32_t Interface);
int32_t MT48LC16M16A2_ModeRegConfig(SDRAM_HandleTypeDef *Ctx, MT48LC16M16A2_Context_t *pRegMode);
int32_t MT48LC16M16A2_TimingConfig(SDRAM_HandleTypeDef *Ctx, FMC_SDRAM_TimingTypeDef *pTiming);
int32_t MT48LC16M16A2_RefreshMode(SDRAM_HandleTypeDef *Ctx, uint32_t Interface, uint32_t RefreshMode);
int32_t MT48LC16M16A2_RefreshRate(SDRAM_HandleTypeDef *Ctx, uint32_t RefreshCount);
int32_t MT48LC16M16A2_EnterPowerMode(SDRAM_HandleTypeDef *Ctx, uint32_t Interface);
int32_t MT48LC16M16A2_ExitPowerMode(SDRAM_HandleTypeDef *Ctx, uint32_t Interface);
int32_t MT48LC16M16A2_Sendcmd(SDRAM_HandleTypeDef *Ctx, FMC_SDRAM_CommandTypeDef *SdramCmd);

#ifdef __cplusplus
 	}
#endif

#endif /* __MT48LC16M16A2_H__ */

