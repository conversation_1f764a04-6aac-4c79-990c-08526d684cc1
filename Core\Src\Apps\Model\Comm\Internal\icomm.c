/**
******************************************************************************
* @file      icomm.c
* <AUTHOR>
* @date      2024-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "Internal.h"
#include "TargetBoard.h"
#include "Comm.h"

void Internal_Comm_Send(Icomm_Protocol_Tx_s *pComm)
{
    u16 data_len = 0;
    u16 header_len = 0;
    u16 crc_len = 0;
    u16 start_len = 0;
    u16 crc16 = 0;

    start_len = sizeof(Icomm_StartFrame_s);
    header_len = sizeof(Icomm_HeaderFrame_s);

    crc_len = 2;
    data_len = pComm->st.hd.Frm_Len;

    // start byte
    pComm->st.st.Start_0 = '!';
	pComm->st.st.Start_1 = '@';
	pComm->st.st.Start_2 = '#';
	pComm->st.st.Start_3 = '$';

    // data frame offset
    pComm->st.hd.Frm_Len += header_len; // + frame(8byte))

    // make crc
    crc16 = crc16_calc(0xFFFF, &pComm->Buffer[4], (header_len+data_len));
    pComm->Buffer[start_len+header_len+data_len]   = (crc16 & 0xFF);
    pComm->Buffer[start_len+header_len+data_len+1] = ((crc16 >> 8) & 0xFF);

    // transmit
    TBD_uart_send_data(TARGET_UART_NAVTEX_CONTROL, (uint8_t *)pComm->Buffer, (start_len+header_len+data_len+crc_len));

#ifdef EN_DBG_COMM_PARA_MSG
    Comm_Debug_Tx_Msg((uint8_t *)pComm->Buffer, (start_len+header_len+data_len+crc_len));
#endif
}

void Internal_Comm_Handshake_EnQueue(Icomm_Protocol_Tx_s *pComm)
{
    u16 data_len = 0;
    u16 header_len = 0;
    u16 crc_len = 0;
    u16 start_len = 0;
    u16 crc16 = 0;
    u16 cmd = 0;

    start_len = sizeof(Icomm_StartFrame_s);
    header_len = sizeof(Icomm_HeaderFrame_s);

    crc_len = 2;
    data_len = pComm->st.hd.Frm_Len;

    // start byte
    pComm->st.st.Start_0 = '!';
    pComm->st.st.Start_1 = '@';
    pComm->st.st.Start_2 = '#';
    pComm->st.st.Start_3 = '$';

    // data frame offset
    pComm->st.hd.Frm_Len += header_len; // + frame(8byte))

    // make crc
    crc16 = crc16_calc(0xFFFF, &pComm->Buffer[4], (header_len+data_len));
    pComm->Buffer[start_len+header_len+data_len]   = (crc16 & 0xFF);
    pComm->Buffer[start_len+header_len+data_len+1] = ((crc16 >> 8) & 0xFF);

    // copy cmd
    cmd = pComm->st.hd.Cmd;

    // transmit
    Handshake_Send_EnQueue(TARGET_UART_NAVTEX_CONTROL, (uint8_t *)pComm->Buffer, (start_len+header_len+data_len+crc_len), crc16, cmd);
}

void Internal_Buffer_Init(u8 *pData, u16 len)
{
    int i = 0;

    for(i=0; i<len; i++)
    {
        pData[i] = 0;
    }
}

void Internal_Comm_Task(void)
{
    Icomm_Protocol_Rx_s *pRx = &gIcommData_Rx;

    static u16 Cnt = 0;
    static int curIndex = -1;
    u16 chk_crc16 = 0;
    u8 chk_crc16_L = 0;
    u8 chk_crc16_H = 0;
    u8 recv_crc16_L = 0;
    u8 recv_crc16_H = 0;
    u16 header_len = 0;
    u16 crc_len = 0;
    u16 start_len = 0;
    u16 data_len = 0;
    uint16_t cmd = 0;
    int i = 0;

    int data = TARGET_UART_NULL_DATA;
    do
    {
        data = TBD_uart_get_data(TARGET_UART_NAVTEX_CONTROL);
        if(data == TARGET_UART_NULL_DATA)
        {
            return;
        }

        pRx->Buffer[Cnt ++] = (u8)data;
        if(pRx->Buffer[0] == '!' && pRx->Buffer[1] == '@' && pRx->Buffer[2] == '#' && pRx->Buffer[3] == '$')
        {
            if(Cnt == 10)
            {
                cmd = pRx->st.hd.Cmd & 0xFF;
                for(i=0; i<I_COMM_RX_TYPE_MAX; i++)
                {
                    if(cmd == gIcommConfig_Rx_Tb[i][0])
                    {
                        curIndex = i;
                        break;
                    }
                }

                if(curIndex == -1)
                {
                    Cnt = 0;
                    curIndex = -1;
                    Internal_Buffer_Init(&pRx->Buffer[0], 4);
                }
                else
                {
                    if(pRx->st.hd.Frm_Len > gIcommConfig_Rx_Tb[curIndex][1])
                    {
                        Cnt = 0;
                        curIndex = -1;
                        Internal_Buffer_Init(&pRx->Buffer[0], 4);
                    }
                }
            }
            else if(Cnt > 12)
            {
                if(Cnt >= (pRx->st.hd.Frm_Len + 6)) // start frame 1byte + start frame dummy 3byte + crc 2byte
                {
                    start_len = sizeof(Icomm_StartFrame_s);
                    header_len = sizeof(Icomm_HeaderFrame_s);
                    data_len = pRx->st.hd.Frm_Len - header_len;
                    crc_len = 2;

                    chk_crc16 = crc16_calc(0xFFFF, &pRx->Buffer[4], pRx->st.hd.Frm_Len);
                    chk_crc16_L = (u8)((chk_crc16)       & 0x00FF);
                    chk_crc16_H = (u8)((chk_crc16 >> 8)  & 0x00FF);

                    recv_crc16_L = pRx->Buffer[start_len+header_len+data_len];
                    recv_crc16_H = pRx->Buffer[start_len+header_len+data_len+1];

                    if((chk_crc16_L == recv_crc16_L) && (chk_crc16_H == recv_crc16_H))
                    {
                        Internal_Rx_Parsing(pRx, chk_crc16);

#ifdef EN_DBG_COMM_PARA_MSG
                        // debug
                        Comm_Debug_Rx_Msg(pRx->Buffer, ((start_len+header_len+data_len+crc_len)));
#endif
                    }
                    else
                    {
                    }
                    Cnt = 0;
                    curIndex = -1;
                    Internal_Buffer_Init(&pRx->Buffer[0], 4);
                }
            }
        }
        else
        {
            if(Cnt >= 4)
            {
                pRx->Buffer[0] = pRx->Buffer[1];
                pRx->Buffer[1] = pRx->Buffer[2];
                pRx->Buffer[2] = pRx->Buffer[3];
                Cnt = 3;
            }
        }
    } while (data != TARGET_UART_NULL_DATA);
}

void Comm_Debug_Tx_Msg(u8 *pData, u16 len)
{
    // debug
    u8 strbuf[1024] = {0, };
    u8 strtmp[4] = {0, };
    u16 i = 0;
    u16 n = 0;
    u16 j = 0;

    for(i=0; i<len; i++)
    {
    	snprintf((char *)strtmp, sizeof(strtmp), "%02x ", (unsigned char)pData[i]);

        for(n=0; n<3; n++)
        {
            strbuf[j ++] = strtmp[n];
        }
    }
    DEBUG_MSG("[%s] Tx = %s \r\n", __FUNCTION__, strbuf);
}

void Comm_Debug_Rx_Msg(u8 *pData, u16 len)
{
    // debug
    u8 strbuf[1024] = {0, };
    u8 strtmp[4] = {0, };
    u16 i = 0;
    u16 n = 0;
    u16 j = 0;

    for(i=0; i<len; i++)
    {
        snprintf((char *)strtmp, sizeof(strtmp), "%02x ", (unsigned char)pData[i]);

        for(n=0; n<3; n++)
        {
            strbuf[j ++] = strtmp[n];
        }
    }
    DEBUG_MSG("[%s] Rx = %s \r\n", __FUNCTION__, strbuf);
}
