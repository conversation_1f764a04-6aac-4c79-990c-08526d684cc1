/**
******************************************************************************
* @file      Handshake_Mgr.c
* <AUTHOR>
* @date      2024-11-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_HANDSHAKE_MGR_HANDSHAKE_MGR_H_
#define SRC_APPS_MODEL_COMM_HANDSHAKE_MGR_HANDSHAKE_MGR_H_

#include "Common.h"

#ifdef EN_DBG_MSG
#define DEBUG_HANDSHAKE_LOG(fmt, args...) printf("[Handshake Comm]" fmt, ##args);
#else
#define DEBUG_HANDSHAKE_LOG(fmt, args...)
#endif

#define HANDSHAKE_MAX_NUM 100
#define HANDSHAKE_PACKET_SIZE_BASE_ON_1_BYTE_MAX 1100
#define HANDSHAKE_TICK_SET_NUM 30
#define HANDSHAKE_TICK_SET_MS 200

typedef struct
{
    u16 len;                                                                                                     
    u16 cmd;
    u8 packet_data[HANDSHAKE_PACKET_SIZE_BASE_ON_1_BYTE_MAX];
}Handshake_Tx_t;

typedef struct
{
    u16 crc;
    u16 crc_flag;
    u16 cmd;
}Handshake_Rx_t;

typedef struct
{
    int front;
    int rear;

    int tx_tick_set[HANDSHAKE_TICK_SET_NUM];
    int tx_tick_cnt;
    int tx_send_cnt;
    int tx_timeout_cnt;
    
    Handshake_Tx_t tx[HANDSHAKE_MAX_NUM];
    Handshake_Rx_t rx[HANDSHAKE_MAX_NUM];
}Handshake_t;

void Handshake_Mgr_Init(void);
void Handshake_Mgr_Reset(int n);
void Handshake_Backend_Task(u8 uart_no);

int Handshake_Send_EnQueue(u8 uart_no, u8 *packet_data, u16 len, u16 crc16, u16 cmd);
int Handshake_Send_DeQueue(u8 uart_no, u8 *packet_data);
void Handshake_Backend_Response_Recv(u16 cmd, u16 crc);

void Handshake_Backend_Response_Recv_Timeout(void);

#endif /* SRC_APPS_MODEL_COMM_HANDSHAKE_MGR_HANDSHAKE_MGR_H_ */
 