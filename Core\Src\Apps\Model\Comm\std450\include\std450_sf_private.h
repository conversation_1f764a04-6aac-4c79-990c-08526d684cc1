///@file     std450_sf_private.h
///@brief    450 internal SF private code header.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#ifndef STD450_SF_PRIVATE_H
#define STD450_SF_PRIVATE_H

#include <std450_common.h>

STD450_BEGIN_DECLS

#define UDPBC_MAX_OPTION 5 //UdPbC option maximum number.

typedef struct sf_s sf_t;       ///Typedef declaration sf

///@brief SF Comm. UDP confgiruation struct.
typedef struct _std450_sf_udp_s {
    char nic_ip[NI_MAXHOST];            /// Using SF Multicast IP.
    net_targ_t targ;                    /// Using SF UDP address.
}std450_sf_udp_t;

///@brief SF Comm. Uart confgiruation struct.
typedef struct _std450_sf_uart_s {
        //uart_targ_t targ;
}std450_sf_uart_t;

///@brief SF Comm. CAN confgiruation struct.
typedef struct _std450_sf_can_s {
        //can_targ_t targ;
}std450_sf_can_t;

///@brief SF Comm. TCP confgiruation struct.
///@param targ SF TCP address.
typedef struct _std450_sf_tcp_s {
        net_targ_t targ;
}std450_sf_tcp_t;

///@brief Backend define and behavior for each communication type and IEC 611612-450 function type. 
///@param _open Communicate file descriptor open function.
///@param _close Communicate file descriptor close function.
///@param _read Commuicate read from file descriptor.
///@param _write Commuicate write to file descriptor.
typedef struct std450_backend_s {
    int     (*_open)(sf_t*, void*);
    int     (*_close)(sf_t*);
    ssize_t (*_read)(sf_t*, char*, int);
    ssize_t (*_write)(sf_t*, char*, int);
} std450_backend_t;

///@brief SF UdPbc information struct.
///@param flag_group UdPbC group flag.
///@param flag_text  UdPbC text flag.
///@param flag_auth  UdPbC authentication flag.
///@param _group UdPbC group string [SFI] info.
///@param _text  UdPbC text string [proprietary] info.
///@param _auth  UdPbC authentication string info.
typedef struct std450_sf_udpbc_s{
    int flag_group;
    int flag_text;
    int flag_auth;
    char *_group;
    char *_text;
    char *_auth;
} std450_sf_udpbc_t;

///@brief SF UdPbc information struct.
///@param _block_id     RaUdp block id.
///@param _max_seq      RaUdp send packet Max page number.
///@param _curr_seq     RaUdp send packet Current page number.
///@param _curr_pos     RaUdp send packet send binary file position.
///@param flag_sts      Using RaUdP status number flag. [For Error number]
///@param _dev_id       RaUdP device id.
///@param _ch_id        RaUdP channel id.
///@param _sts_num      RaUdP status number. [ flag_sts_num ==1 ]. Normal is 0.
///@param _sts_text     RaUdP status text about status nuber. [ flag_sts_num ==1 ]
typedef struct std450_sf_raudp_s{
    unsigned int _block_id;
    unsigned int _max_seq;
    unsigned int _curr_seq;
    unsigned int _curr_pos;
    unsigned short _dtype;
    char _dev_id;
    char _ch_id;        
    int flag_sts_text;
    int _sts_num;
    char *_sts_text;
    unsigned int _bin_len;
    unsigned int _extension_len;
    char *_extension;
    char *_bin;
} std450_sf_raudp_t;

///@brief SF pthread argument.
///@param sf  allocated 450 device for SF.
///@param err_list  When error occoured, insert data target linked list. (just address get)
///@param sfi_list  allocate sfi address. When create sf, sfi_list allocate sfi space.
typedef struct sf_pth_args_s{
    sf_t *sf;
    std450_list_t *err_list; 
    sfi_info_t *sfi_info;
} sf_pth_args_t;

///@brief Backend define and behavior for each communication type and IEC 611612-450 function type. 
///@param _set_opt Each token header, option. 
///@param _write_msg Each token header, write message behavior.
///@param _read_msg  Each token header, read message behavior.
typedef struct std450_behavior_s {
    void (*_init)(sf_t*, std450_list_t*, sfi_info_t*);
    void (*_exit)(sf_t*);
    int (*_set_opt)(sf_t*, void*);
    ssize_t (*_write_msg)(sf_t*, void*, int);
    ssize_t (*_read_msg) (sf_t*, void*, int);
} std450_behavior_t;

/// @brief System function struct. IEC 61162-450 4.4 SF requirements
/// @todo  Adding queue and fifo. Data process function. 
struct sf_s {
    int   id;                           /// Internal sf id in std450 dev.
    int   fd;                           /// SF file descriptor.
    int   comm_type;                    /// Communication type. [UDP, UART, CA  N, TCP]
    int   func_type;                    /// Function Type. [None, UdPbc, RaUdp, RrUdp, NkPgN, ...]
    int   line_cnt;                     /// SF Tx Total Comm. line count.
    int   dst_num;                      /// SF destination number.
    sfi_t src;                          /// SF soruce SFI
    sfi_t *dst;                         /// SF destination SFI
    const std450_backend_t *backend;    /// SF operation comm function. and config (comm)
    const std450_behavior_t *behavior;  /// SF opertion function.
    void *backend_data;                 /// sf comm. config information.
    void *behavior_data;                /// sf behavior information.
    std450_list_t  tx_list;            /// Tx thread working list. 
    std450_list_t  rx_list;            /// Rx thread working list. 
    pthread_info_t tx_tinfo;           /// Tx trhead inforamtion.
    pthread_info_t rx_tinfo;           /// Rx thread informaiton.
    pth_args_t     tx_pth_args;
    pth_args_t     rx_pth_args;
};

STD450_END_DECLS

#endif  /* STD450_SF_PRIVATE_H */