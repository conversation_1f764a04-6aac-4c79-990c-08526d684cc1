/**
******************************************************************************
* @file      ext_can_ctrl.h
* <AUTHOR>
* @date      2024-7-13
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/


#ifndef _EXT_CAN_CTRL_H_
#define _EXT_CAN_CTRL_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"
#include "TargetCan.h"
#include "firmware_update.h"

#define VHF_PERIODIC_CAN_ID				(0x800)	// VHF Periodic Can ID
#define VHF_EVENT_CAN_ID				(0x880)	// VHF Event Can ID
#define ABU_CAN_ID_MASK					(0xFF0)
#define ABU_DEV_ID_MASK					(0xF)

// Bootloader
#define ABU_ENTRY_BL_CMD_ID             (0xBFF)
#define ABU_SYSTEM_RESET_CMD_ID         (0xBFE)
#define ABU_BL_CMD_ID                   (0xB00)

#define ABU_ENTRY_BL_CMD_ACK_ID         (0xBFA)
#define ABU_BL_ERROR_CODE_ID            (0xBEA)
#define ABU_BL_CMD_ACK_ID               (0xB0A)

#define ABU_BL_BID_AU                   (0xA0)
#define ABU_BL_BID_VHF_AB_BASE          (0x20)

#define ABU_BL_METAINFO_DEV_AB          (0xA0)
#define ABU_BL_METAINFO_DEV_AU          (0xAB)
#define ABU_BL_METAINFO_DEV_SB          (0xC0)

#define ABU_BL_CMD_FW_METAINFO          (0x00)
#define ABU_BL_CMD_WRITE_BUFFER         (0x01)
#define ABU_BL_CMD_FLASH_PAGE           (0x02)
#define ABU_BL_CMD_FLASH_CRC            (0x03)
#define ABU_BL_CMD_SYSTEM_RESET         (0xFE)
#define ABU_BL_CMD_ENTRY_BL             (0xFF)

#define ABU_BL_MSG_BID_IDX              (0)	// BYTE 1
#define ABU_BL_MSG_CMD_IDX              (1)	// BYTE 2
#define ABU_BL_MSG_PARAM1_IDX           (3)	// BYTE 4
#define ABU_BL_MSG_PARAM2_IDX           (4)	// BYTE 5~8



// define vhf can period message index
#define VHF_CAN_MSG_IDX_STATUS				(0)	// BYTE 1

// define alarm box & unit can message index
#define ABU_CAN_MSG_IDX_CMD					(0)	// BYTE 1
#define ABU_CAN_MSG_IDX_ADDR_L				(2)	// BYTE 3
#define ABU_CAN_MSG_IDX_ADDR_H				(3)	// BYTE 4
#define ABU_CAN_MSG_IDX_VAL					(4)	// BYTE 5~8

// BYTE1(Alarm status)
#define ABU_ALARM_STATUS_MASK				(0x0F)

typedef enum {
	ALARM_STATUS_NONE			= 0x00,
	ALARM_STATUS_NONE_ALARM		= 0x01,
	ALARM_STATUS_DIST_RX		= 0x02,
	ALARM_STATUS_DIST_TX		= 0x03,
	ALARM_STATUS_DIST_BTN_PRESS	= 0x04,
	ALARM_STATUS_DIST_ACK_RX	= 0x05,
	ALARM_STATUS_URG_RX			= 0x06,
	ALARM_STATUS_URG_ACK_RX		= 0x07
}alarm_status_t;

// BYTE5(command)
#define ABU_CMD_MASK						(0x0F)
#define ABU_CMD_CONN_REQ_ACK				(0x01)
#define ABU_CMD_ALARM_SATAUS_CHANGE			(0x02)

void eCan_abu_send_status_change(alarm_status_t status);
uint8_t eCan_abu_get_bl_board_id(fwu_device_param_t dev);
int eCan_abu_send_msg(fwu_device_param_t dev, uint32_t id, uint8_t cmd, uint8_t param1, uint8_t* p_param2);
int eCan_abu_send_entry_bl(fwu_device_param_t dev);
int eCan_abu_send_system_reset(fwu_device_param_t dev);
int eCan_abu_send_bl_cmd(fwu_device_param_t dev, uint8_t cmd, uint8_t param1, uint8_t* p_param2);
int eCan_send_msg(can_msg_s *pMsg);
void eCan_process();
void eCan_abu_process();
void eCan_rmt_process();

#ifdef __cplusplus
}
#endif

#endif

