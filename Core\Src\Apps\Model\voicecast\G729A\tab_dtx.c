/*
  ITU-T G.729A Speech Coder with Anne<PERSON> B    ANSI-C Source Code
*/

/*
----------------------------------------------------------------------
                    COPYRIGHT NOTICE
----------------------------------------------------------------------
   ITU-T G.729 Annex C ANSI C source code
   Copyright (C) 1998, AT&T, France Telecom, NTT, University of
   Sherbrooke.  All rights reserved.

----------------------------------------------------------------------
*/

/*********************************************************************/
/******             Tables used for VAD/DTX/CNG                 ******/
/*********************************************************************/
#include <stdio.h>

#include "typedef.h"
#include "ld8a.h"
#include "vad.h"
#include "dtx.h"
#include "tab_dtx.h"

/* VAD constants */
FLOAT lbf_corr[NP+1]={
    (F)0.24017939691329, (F)0.21398822343783, (F)0.14767692339633,
    (F)0.07018811903116, (F)0.00980856433051, (F)-0.02015934721195,
    (F)-0.02388269958005, (F)-0.01480076155002, (F)-0.00503292155509,
    (F)0.00012141366508, (F)0.00119354245231, (F)0.00065908718613,
    (F)0.00015015782285};

/* Quantization of SID gain */
FLOAT fact[NB_GAIN+1] =
        {(F)0.003125, (F)0.00078125, (F)0.000390625};
 FLOAT tab_Sidgain[32] = {
   (F)0.502,    (F)1.262,    (F)2.000,    (F)3.170,    (F)5.024,    (F)7.962,
  (F)12.619,   (F)15.887,   (F)20.000,   (F)25.179,   (F)31.698,   (F)39.905,
  (F)50.238,   (F)63.246,   (F)79.621,  (F)100.237,  (F)126.191,  (F)158.866,
 (F)200.000,  (F)251.785,  (F)316.979,  (F)399.052,  (F)502.377,  (F)632.456,
 (F)796.214, (F)1002.374, (F)1261.915, (F)1588.656, (F)2000.000, (F)2517.851,
(F)3169.786, (F)3990.525};

/* Quantization of LSF vector */
int PtrTab_1[32] = {96,52,20,54,86,114,82,68,36,121,48,92,18,120,
                         94,124,50,125,4,100,28,76,12,117,81,22,90,116,
                         127,21,108,66};
int PtrTab_2[2][16]= {{31,21,9,3,10,2,19,26,4,3,11,29,15,27,21,12},
                           {16,1,0,0,8,25,22,20,19,23,20,31,4,31,20,31}};


FLOAT noise_fg_sum[MODE][LPC_M]=
{
  {(F)2.379833e-01f, (F)2.577898e-01f, (F)2.504044e-01f, (F)2.530900e-01f,
(F)2.479934e-01f,
     (F)2.587054e-01f, (F)2.577898e-01f, (F)2.656026e-01f, (F)2.759789e-01f,
(F)2.625813e-01f}
,
  {(F)0.320883796f,  (F)0.378502704f,  (F)0.391650136f,  (F)0.363609794f,
(F)0.349357626f,
   (F)0.356157116f,  (F)0.339738164f,  (F)0.345200944f, (F)0.361461228f,
(F)0.349363712f},
};

FLOAT noise_fg_sum_inv[MODE][LPC_M]=
{
{(F)4.201788e+00f, (F)3.879025e+00f, (F)3.993530e+00f, (F)3.951048e+00f,
(F)4.032350e+00f,
     (F)3.865596e+00f, (F)3.879025e+00f, (F)3.765007e+00f, (F)3.623157e+00f,
(F)3.807978e+00f}
,
  {(F)3.11639295117289f,   (F)2.64198905168191f,   (F)2.55329925380136f,
 (F)2.75020094755754f, (F)2.86239636858535f,   (F)2.80774960003888f,
 (F)2.94344323353675f,   (F)2.89686345701303f, (F)2.76654844983817f,
 (F)2.86234650495126f}
};

FLOAT Mp[MODE]={(F)0.065942075f, (F)0.12644604f};

