/**
******************************************************************************
* @file      ecomm.h
* <AUTHOR>
* @date      2024-07-07
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_EXTERNAL_ECOMM_H_
#define SRC_APPS_MODEL_COMM_EXTERNAL_ECOMM_H_

#include "Common.h"
#include "ecomm_map.h"

#define MAX_NMEA_61162_1_2_TX_BUF_SIZE 10240

typedef enum
{
    NMEA_TYPE_NONE = 0,
    
    NMEA_TYPE_INS = NMEA_TYPE_NONE,
    NMEA_TYPE_BAM,

    NMEA_TYPE_MAX,
} NMEA_61162_1_2_Port_t;

typedef struct 
{
    uint8_t buf[MAX_NMEA_61162_1_2_TX_BUF_SIZE];
    int front;
    int rear;
} NMEA_61162_1_2_Tx_Buf_s;

uint8_t nmea_uart_is_buf_full(NMEA_61162_1_2_Tx_Buf_s *pBuf);
uint8_t nmea_uart_is_buf_empty(NMEA_61162_1_2_Tx_Buf_s *pBuf);

int nmea_uart_enqueue(NMEA_61162_1_2_Tx_Buf_s *pBuf, uint8_t *p_data, int size);
int nmea_uart_dequeue(NMEA_61162_1_2_Tx_Buf_s *pBuf);

void nmea_uart_tx_ins(void);
void nmea_uart_tx_bam(void);

void External_Comm_Bam_Task(void);
void External_Comm_Ins_Task(void);
void External_Nmea_Uart_Tx_Task(void);

void External_Comm_Reset(eCom_data_s *pCom);
void External_Comm_Init();

extern NMEA_61162_1_2_Tx_Buf_s nmea_bufs[NMEA_TYPE_MAX] __attribute__((section(".UartDmaSection")));

#endif /* SRC_APPS_MODEL_COMM_EXTERNAL_ECOMM_H_ */
