/**
******************************************************************************
* @file      au_can.h
* <AUTHOR>
* @date      2024-7-13
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef _ALARM_UNIT_CAN_H_
#define _ALARM_UNIT_CAN_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32h7xx_hal.h"	/* for data type(uint8_t, uint32_t .....) */
#include "TargetCan.h"
#include "ext_can_ctrl.h"

#define AU_PERIODIC_ID		(0x500)	// Alarm Unit periodic id for VHF
#define AU_EVENT_ID			(0x580)	// Alarm Unit event id for VHF

// define alarm unit event can message's command byte index
#define AUE_CB_IDX_MODE				(0)
#define AUE_CB_IDX_DIST_BTN_STAT	(1)
#define AUE_CB_IDX_CON_REQ			(2)

// Event Type BYTE1(Mode)
#define AUE_MODE_MASK					(0x0F)

// Event Type BYTE2(Distress Button Status)
#define AUE_DIST_BTN_STAT_TICK_MASK	(0xFC)
#define AUE_DIST_BTN_STAT_BTN_MASK	(0x03)

#define AUE_DIST_BTN_STAT_TIME_0S	(0x00)
#define AUE_DIST_BTN_STAT_TIME_1S	(0x01)
#define AUE_DIST_BTN_STAT_TIME_2S	(0x02)
#define AUE_DIST_BTN_STAT_TIME_3S	(0x03)

#define AUE_DIST_BTN_STAT_NONE1		(0x00)
#define AUE_DIST_BTN_STAT_PRESSED	(0x01)
#define AUE_DIST_BTN_STAT_RELEASED	(0x02)
#define AUE_DIST_BTN_STAT_NONE2		(0x03)

// Event Type BYTE3(Connection Request)
#define AUE_CONN_REQ_NONE1				(0x00)
#define AUE_CONN_REQ_REQUEST			(0x01)
#define AUE_CONN_REQ_NONE2				(0x02)
#define AUE_CONN_REQ_NONE3				(0x03)

// define alarm unit periodic can message's command byte index
#define AUP_CB_IDX_MODE				(0)
#define AUP_CB_IDX_MUTE_STAT		(1)
#define AUP_CB_IDX_VER_MAJ			(5)
#define AUP_CB_IDX_VER_MIN			(6)
#define AUP_CB_IDX_VER_PATCH		(7)

// Periodic Type BYTE1(Mode)
#define AUP_MODE_MASK					(0x0F)

// Periodic Type BYTE2(Mute)
#define AUP_MUTE_STATUS_MASK			(0x03)
#define AUP_MUTE_STATUS_UNMUTE		(0x00)
#define AUP_MUTE_STATUS_MUTE			(0x01)

void eCan_au_send_conn_req_ack(can_msg_s *pMsg);

void eCan_au_proc_rx_periodic_msg(can_msg_s *pMsg);
void eCan_au_proc_rx_event_msg(can_msg_s *pMsg);

#ifdef __cplusplus
}
#endif

#endif	/* _ALARM_UNIT_CAN_H_ */

