/**
******************************************************************************
* @file      TargetErrNo.h
* <AUTHOR>
* @date      2022-12-28
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef _TARGET_ERRNO_H_
#define _TARGET_ERRNO_H_

#include "TargetConfig.h"

#include "stm32h743i_eval_errno.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Common Error codes */
#define TBD_ERROR_NONE	                   BSP_ERROR_NONE
#define TBD_ERROR_NO_INIT                  BSP_ERROR_NO_INIT
#define TBD_ERROR_WRONG_PARAM              BSP_ERROR_WRONG_PARAM
#define TBD_ERROR_BUSY                     BSP_ERROR_BUSY
#define TBD_ERROR_PERIPH_FAILURE           BSP_ERROR_PERIPH_FAILURE
#define TBD_ERROR_COMPONENT_FAILURE        BSP_ERROR_COMPONENT_FAILURE
#define TBD_ERROR_UNKNOWN_FAILURE          BSP_ERROR_UNKNOWN_FAILURE
#define TBD_ERROR_UNKNOWN_COMPONENT        BSP_ERROR_UNKNOWN_COMPONENT
#define TBD_ERROR_BUS_FAILURE              BSP_ERROR_BUS_FAILURE
#define TBD_ERROR_CLOCK_FAILURE            BSP_ERROR_CLOCK_FAILURE
#define TBD_ERROR_MSP_FAILURE              BSP_ERROR_MSP_FAILURE
#define TBD_ERROR_FEATURE_NOT_SUPPORTED    BSP_ERROR_FEATURE_NOT_SUPPORTED
#define TBD_ERROR							 		(BSP_ERROR_FEATURE_NOT_SUPPORTED-1)

/* Target Board OSPI error codes */
#define TBD_ERROR_QSPI_ASSIGN_FAILURE      BSP_ERROR_QSPI_ASSIGN_FAILURE
#define TBD_ERROR_QSPI_SETUP_FAILURE       BSP_ERROR_QSPI_SETUP_FAILURE
#define TBD_ERROR_QSPI_MMP_LOCK_FAILURE    BSP_ERROR_QSPI_MMP_LOCK_FAILURE
#define TBD_ERROR_QSPI_MMP_UNLOCK_FAILURE  BSP_ERROR_QSPI_MMP_UNLOCK_FAILURE

/* Target Board TS error code */
#define TBD_ERROR_TS_TOUCH_NOT_DETECTED    BSP_ERROR_TS_TOUCH_NOT_DETECTED

/* Target BUS error codes */
#define TBD_ERROR_BUS_TRANSACTION_FAILURE  BSP_ERROR_BUS_TRANSACTION_FAILURE
#define TBD_ERROR_BUS_ARBITRATION_LOSS     BSP_ERROR_BUS_ARBITRATION_LOSS
#define TBD_ERROR_BUS_ACKNOWLEDGE_FAILURE  BSP_ERROR_BUS_ACKNOWLEDGE_FAILURE
#define TBD_ERROR_BUS_PROTOCOL_FAILURE     BSP_ERROR_BUS_PROTOCOL_FAILURE

#define TBD_ERROR_BUS_MODE_FAULT           BSP_ERROR_BUS_MODE_FAULT
#define TBD_ERROR_BUS_FRAME_ERROR          BSP_ERROR_BUS_FRAME_ERROR
#define TBD_ERROR_BUS_CRC_ERROR            BSP_ERROR_BUS_CRC_ERROR
#define TBD_ERROR_BUS_DMA_FAILURE          BSP_ERROR_BUS_DMA_FAILURE

#ifdef __cplusplus
}
#endif
#endif /* _TARGET_ERRNO_H_ */

