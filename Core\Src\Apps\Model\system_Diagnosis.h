/**
******************************************************************************
* @file      system_SelfDiagnosis.h
* <AUTHOR>
* @date      2023-11-08
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_SYSTEM_SELFDIAGNOSIS_H_
#define SRC_APPS_MODEL_SYSTEM_SELFDIAGNOSIS_H_

#define TICK_500_MSEC (500/50)
#define TICK_1_SEC (1000/50)
#define TICK_2_SEC (2000/50)
#define TICK_5_SEC (5000/50)
#define TICK_10_SEC (10000/50)
#define TICK_15_SEC (15000/50)
#define TICK_20_SEC (20000/50)
#define TICK_60_SEC (60000/50)

typedef enum
{
    DIAG_TYPE_NONE = 0,

    DIAG_TYPE_RECV_RF_518KHZ,
    DIAG_TYPE_RECV_RF_490KHZ,
    DIAG_TYPE_RECV_RF_4209_5KHZ,

    DIAG_TYPE_RECV_MSG_RF_ALL,

    DIAG_TYPE_MAX,
}DIAG_TYPE_t;

typedef enum
{
    DIAG_STATE_NONE = 0,

    DIAG_STATE_START,
    DIAG_STATE_ING,
    DIAG_STATE_END,
    DIAG_STATE_SUCCESS,
    DIAG_STATE_FAIL,
    DIAG_STATE_TIMEOUT,
}DIAG_STATE_t;

void System_Diagnosis_Init(void);
void System_Diagnosis_Run(void);

void System_Diagnosis_518KHz_Startphasing(void);
void System_Diagnosis_490KHz_Startphasing(void);
void System_Diagnosis_4209_5KHz_Startphasing(void);

void System_Diagnosis_Message_Receive_Monitor(void);


#endif /* SRC_APPS_MODEL_SYSTEM_SELFDIAGNOSIS_H_ */
