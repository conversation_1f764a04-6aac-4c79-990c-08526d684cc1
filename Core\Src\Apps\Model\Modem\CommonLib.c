﻿/**
 * @file      CommonLib.c
 * <AUTHOR>
 * @brief     공통으로 사용되는 함수 라이브러리
 * @version   0.1
 * @date      2022-08-26
 *
 * @copyright Copyright (c) 2022
 *
 */


//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#include "AllConst.h"
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
/**
 * @brief FIR 필터 초기화 작업
 * 
 * @param pFIR  tBaseFIR-pointer
 * @param pCoef FIR 필터 임펄스응답 coefficient data-pointer
 * @param dSize FIR 필터 임펄스응답 coefficient 수
 */
void  MakeInitBaseFIR(tBaseFIR *pFIR, float32_t *pCoef, uint32_t dSize)
{
      uint32_t i;
      uint32_t dTotSize;

      if (pCoef != NULL)
      {
          dTotSize = (dSize + 1) * sizeof(float32_t);

          pFIR->dSizeB = dSize;
          pFIR->pCoefH = pCoef;
      }
      else
      {
          dSize = pFIR->dSizeB;

          dTotSize = (dSize + 1) * sizeof(float32_t);
      }

      pFIR->dDataP = 0;
      pFIR->pDataB = (float32_t *)malloc(dTotSize);
      pFIR->pAddrF = pFIR->pDataB;
      pFIR->pAddrL = pFIR->pDataB + dSize;

      for (i = 0; i <= dSize; i++)
           pFIR->pDataB[i] = 0.0f;
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief FIR 필터 연산 수행
 * 
 * @param pFIR        tBaseFIR-pointer
 * @param fInData     입력-data
 * @return float32_t  FIR 필터 연산 결과
 */
float32_t CalcNewDataFIR(tBaseFIR *pFIR, float32_t fInData)
{
      //  64 ==> 1.2 us
      // 128 ==> 2.2 us
      // 256 ==> 4.1 us
      // 384 ==> 6.0 us
      // 512 ==> 7.7 us

      uint32_t i;
      float32_t  fTempX = 0.0f;
      uint32_t   dSizeB = pFIR->dSizeB;
      float32_t *pDataB = pFIR->pDataB + pFIR->dDataP;
      float32_t *pCoefH = pFIR->pCoefH;
      float32_t *pAddrF = pFIR->pAddrF;
      float32_t *pAddrL = pFIR->pAddrL;

      *pDataB = fInData;

      for (i = 0; i <= dSizeB; i++)
      {
           fTempX += (*pDataB-- * *pCoefH++);
           if (pDataB < pAddrF)
               pDataB = pAddrL;
      }

      ++pFIR->dDataP;
      if (pFIR->dDataP > pFIR->dSizeB)
          pFIR->dDataP = 0;

      return(fTempX);
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  ClearAllBuffFIR(tBaseFIR *pFIR)
{
      memset(pFIR->pDataB, 0x00, pFIR->dSizeB * sizeof(float32_t));
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief IIR 필터 초기화 작업
 * 
 * @param pIIR   tBaseIIR-pointer
 * @param pCoefA IIR 필터 a-coefficient data-pointer
 * @param pCoefB IIR 필터 b-coefficient data-pointer
 * @param dSize  IIR 필터 coefficient 수
 */
void  MakeInitBaseIIR(tBaseIIR *pIIR, float64_t *pCoefA, float64_t *pCoefB, uint32_t dSize)
{
      uint32_t i;
      uint32_t dTotSize;

      if (pCoefA != NULL)
      {
          dTotSize = (dSize + 1) * sizeof(float64_t);

          pIIR->dSizeB = dSize;
          pIIR->pCoefA = pCoefA;
          pIIR->pCoefB = pCoefB;
      }
      else
      {
          dSize = pIIR->dSizeB;

          dTotSize = (dSize + 1) * sizeof(float64_t);
      }

      pIIR->pDataB = (float64_t *)malloc(dTotSize);

      for (i = 0; i <= dSize; i++)
           pIIR->pDataB[i] = 0.0;
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief IIR 필터 연산 수행
 * 
 * @param pIIR       tBaseIIR-pointer
 * @param fInData    입력-data
 * @return float32_t IIR 필터 연산 결과
 */
float32_t CalcNewDataIIR(tBaseIIR *pIIR, float32_t fInData)
{
      uint32_t  i;
      float64_t fTempX;

      fTempX = pIIR->pDataB[0] + pIIR->pCoefB[0] * fInData;

      for (i = 1; i <= pIIR->dSizeB; i++)
           pIIR->pDataB[i - 1] =    pIIR->pCoefB[i] * fInData + 
                                    pIIR->pCoefA[i - 1] * fTempX + 
                                    pIIR->pDataB[i];

      return((float32_t)fTempX);
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  ClearAllBuffIIR(tBaseIIR *pIIR)
{
      memset(pIIR->pDataB, 0x00, pIIR->dSizeB * sizeof(float64_t));
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief Bi-Quad 필터 초기화 작업
 * 
 * @param pIIR      tBiQdIIR-pointer
 * @param dBiQdType 필터 type
 * @param rCutFreq  차단주파수
 * @param rSmpFreq  샘플링주파수
 * @param rQfactor  Q-값 (1/sqrt(2) = -3dB)
 * @param rPeakGain peak게인 (0)
 */
void  MakeInitBiQdIIR(tBiQdIIR *pIIR, eBiQdType dBiQdType, float64_t rCutFreq, float64_t rSmpFreq, float64_t rQfactor, float64_t rPeakGain)
{
      float64_t rNorm;
      float64_t K, Q;
      float64_t a0, a1, a2;
      float64_t b1, b2;

      a0 = a1 = a2 = b1 = b2 = 0.0;

//    V = pow(10.0, fabs(rPeakGain) / 20.0);
      K = tan(MAT_VAL_PI_D * (rCutFreq / rSmpFreq));
      Q = rQfactor;

    if (dBiQdType == BQ_TYPE_LOWPASS)  
    {
        rNorm = 1 / (1 + K / Q + K * K); 
        a0 = K * K * rNorm;       
        a1 = +2 * a0;                   
        a2 = +a0; b1 = 2 * (K * K - 1) * rNorm; 
        b2 = (1 - K / Q + K * K) * rNorm;
    }
    if (dBiQdType == BQ_TYPE_HIGHPASS)
    {
        rNorm = 1 / (1 + K / Q + K * K);
        a0 = 1 * rNorm;
        a1 = -2 * a0;
        a2 = +a0; 
        b1 = 2 * (K * K - 1) * rNorm; 
        b2 = (1 - K / Q + K * K) * rNorm;
    }
    if (dBiQdType == BQ_TYPE_BANDPASS)
    {
        rNorm = 1 / (1 + K / Q + K * K); 
        a0 = K / Q * rNorm;       
        a1 = 0.0;                       
        a2 = -a0; b1 = 2 * (K * K - 1) * rNorm; 
        b2 = (1 - K / Q + K * K) * rNorm;
    }
    if (dBiQdType == BQ_TYPE_NOTCH)
    {
        rNorm = 1 / (1 + K / Q + K * K);
        a0 = (1 + K * K) * rNorm;
        a1 = +2 * (K * K - 1) * rNorm;
        a2 = +a0;
        b1 = a1;
        b2 = (1 - K / Q + K * K) * rNorm;
    }

      pIIR->a0 = a0;
      pIIR->a1 = a1;
      pIIR->a2 = a2;
      pIIR->b1 = b1;
      pIIR->b2 = b2;
      pIIR->z1 = 0.0;
      pIIR->z2 = 0.0;

      pIIR->pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataBiQd;
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief Bi-Quad 필터 연산 수행
 * 
 * @param pIIR       tBiQdIIR-pointer
 * @param fInData    입력-data
 * @return float32_t Bi-Quad 필터 연산 결과
 */
float32_t CalcNewDataBiQd(tBiQdIIR *pIIR, float32_t fInData)
{
      float64_t fTempX;

      fTempX   = fInData * pIIR->a0 + pIIR->z1;
      pIIR->z1 = fInData * pIIR->a1 + pIIR->z2 - pIIR->b1 * fTempX;
      pIIR->z2 = fInData * pIIR->a2 - pIIR->b2 * fTempX;

      return((float32_t)fTempX);
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief Hilbert 필터 초기화 작업
 * 
 * @param pFIR    tHlbtFIR-pointer
 * @param pCoef   Hilbert 필터 임펄스응답 coefficient data-pointer
 * @param dSize   Hilbert 필터 임펄스응답 coefficient 수
 */
void  MakeInitBaseHBT(tHlbtFIR *pFIR, float32_t *pCoef, uint32_t dSize)
{
      uint32_t i;
      uint32_t dTotSize;

      if (pCoef != NULL)
      {
          dTotSize = (dSize + 1) * sizeof(float32_t);

          pFIR->dSizeB = dSize;
          pFIR->pCoefH = pCoef;
      }
      else
      {
          dSize = pFIR->dSizeB;

          dTotSize = (dSize + 1) * sizeof(float32_t);
      }

      pFIR->dDataP = 0;
      pFIR->pDataB = (float32_t *)malloc(dTotSize);
      pFIR->pDataR = (float32_t *)malloc(dTotSize);
      pFIR->pAddrF = pFIR->pDataB;
      pFIR->pAddrL = pFIR->pDataB + dSize;

      for (i = 0; i <= dSize; i++)
      {
           pFIR->pDataB[i] = 0.0f;
           pFIR->pDataR[i] = 0.0f;
      }
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief Hilbert 필터 연산 수행
 * 
 * @param pFIR    tHlbtFIR-pointer
 * @param fInData 입력-data
 * @param pRealV     Hilbert 필터 연산 결과 (실수)
 * @return float32_t Hilbert 필터 연산 결과 (허수)
 */
float32_t CalcNewImagHBT(tHlbtFIR *pFIR, float32_t fImagData, float32_t fRealData, float32_t *pRealV)
{
 #if FLT_HILBERT_RUN_MODE == (1)
      uint32_t i;
      float32_t  fTempX = 0.0f;
      uint32_t   dSizeB = pFIR->dSizeB;
      float32_t *pDataB = pFIR->pDataB + pFIR->dDataP;
      float32_t *pDataR = pFIR->pDataR + pFIR->dDataP;
      float32_t *pCoefH = pFIR->pCoefH;
      float32_t *pAddrF = pFIR->pAddrF;
      float32_t *pAddrL = pFIR->pAddrL;

      *pDataB = fImagData;
      *pDataR = fRealData;

      for (i = 0; i <= dSizeB; i += 2)
      {
           fTempX += (*pDataB   * *pCoefH);

           pDataB -= 2;
           if (pDataB < pAddrF)
               pDataB = pAddrL - pAddrF + pDataB + 1;

           pCoefH += 2;
      }

      ++pFIR->dDataP;
      if (pFIR->dDataP > pFIR->dSizeB)
          pFIR->dDataP = 0;

      ++pFIR->dRealP;
      if (pFIR->dRealP > pFIR->dSizeB)
          pFIR->dRealP = 0;

//    *pRealV = pFIR->pDataB[pFIR->dRealP];
      *pRealV = pFIR->pDataR[pFIR->dRealP];

      return(fTempX * pFIR->fGainH);
 #else
      uint32_t i;
      float32_t  fTempX = 0.0f;
      uint32_t   dSizeB = pFIR->dSizeB;
      float32_t *pDataB = pFIR->pDataB + pFIR->dDataP;
      float32_t *pDataR = pFIR->pDataR + pFIR->dDataP;
      float32_t *pCoefH = pFIR->pCoefH;
      float32_t *pAddrF = pFIR->pAddrF;
      float32_t *pAddrL = pFIR->pAddrL;

      *pDataB = fImagData;
      *pDataR = fRealData;

      for (i = 0; i <= dSizeB; i++)
      {
           fTempX += (*pDataB-- * *pCoefH++);
           if (pDataB < pAddrF)
               pDataB = pAddrL;
      }

      ++pFIR->dDataP;
      if (pFIR->dDataP > pFIR->dSizeB)
          pFIR->dDataP = 0;

      ++pFIR->dRealP;
      if (pFIR->dRealP > pFIR->dSizeB)
          pFIR->dRealP = 0;

//    *pRealV = pFIR->pDataB[pFIR->dRealP];
      *pRealV = pFIR->pDataR[pFIR->dRealP];

      return(fTempX);
 #endif
}
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
