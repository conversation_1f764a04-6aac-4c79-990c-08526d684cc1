/**
******************************************************************************
* @file      Tcomm_rx_datalink.h
* <AUTHOR>
* @date      2024-07-14
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/
#ifndef SRC_APPS_MODEL_COMM_TESTING_TCOMM_RX_DATALINK_H_
#define SRC_APPS_MODEL_COMM_TESTING_TCOMM_RX_DATALINK_H_

#include "Common.h"
#include "TestingComm_Map.h"

void Testing_Rx_Parsing(Tcomm_Protocol_Rx_s *pRx);

void Testing_Rx_Gpio_Control(Tcomm_Protocol_Rx_s *pRx);
void Testing_Rx_RF_Control(Tcomm_Protocol_Rx_s *pRx);

#endif /* SRC_APPS_MODEL_COMM_TESTING_TCOMM_RX_DATALINK_H_ */
