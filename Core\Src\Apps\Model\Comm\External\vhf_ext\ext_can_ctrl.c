/**
******************************************************************************
* @file      nmea2000.c
* <AUTHOR>
* @date      2024-7-13
* @brief     NMEA2000과 내부 Can 통신을 위한 컨트롤러 
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "ext_can_ctrl.h"
#include "ab_can.h"
#include "au_can.h"
#include "local_ab_can.h"
//#include "nmea2000.h"
//#include "apps_data_type.h"
//#include "CommTask.h"

#define CAN_MSG_PREIOD_MS   (1000)

extern FDCAN_HandleTypeDef hfdcan1;

/**
 * @brief  CAN 장비 부트로더 board id 출력
 * @param
 * @return
*/
uint8_t eCan_abu_get_bl_board_id(fwu_device_param_t dev)
{
	if (dev.DeviceType != FWU_DEVICE_VHF)
		return 0;

	if (dev.SubDeviceType == FWU_SUB_DEVICE_ALARM_BOX)
		return ABU_BL_BID_VHF_AB_BASE | dev.DeviceNumber;
	else if (dev.SubDeviceType == FWU_SUB_DEVICE_ALARM_UNIT)
		return ABU_BL_BID_AU;
}

int eCan_abu_send_msg(fwu_device_param_t dev, uint32_t id, uint8_t cmd, uint8_t param1, uint8_t* p_param2)
{
	can_msg_s tmp_msg;
	TBD_can_init_msg(&tmp_msg);
	tmp_msg.id = id;
	tmp_msg.data[ABU_BL_MSG_BID_IDX] = eCan_abu_get_bl_board_id(dev);
	tmp_msg.data[ABU_BL_MSG_CMD_IDX] = cmd;
	tmp_msg.data[ABU_BL_MSG_PARAM1_IDX] = param1;
	if (p_param2 != NULL)
		memcpy(&tmp_msg.data[ABU_BL_MSG_PARAM2_IDX], p_param2, 4);
	return eCan_send_msg(&tmp_msg);
}

/**
 * @brief  CAN 장비 부트로더 진입 명령
 * @param
 * @return
*/
int eCan_abu_send_entry_bl(fwu_device_param_t dev)
{
	return eCan_abu_send_msg(dev, ABU_ENTRY_BL_CMD_ID, ABU_BL_CMD_ENTRY_BL, 0, NULL);
}

/**
 * @brief  CAN 장비 시스템 리셋 명령
 * @param
 * @return
*/
int eCan_abu_send_system_reset(fwu_device_param_t dev)
{
	return eCan_abu_send_msg(dev, ABU_SYSTEM_RESET_CMD_ID, ABU_BL_CMD_SYSTEM_RESET, 0, NULL);
}

/**
 * @brief  CAN 장비 부트로더 명령 전송
 * @param
 * @return
*/
int eCan_abu_send_bl_cmd(fwu_device_param_t dev, uint8_t cmd, uint8_t param1, uint8_t* p_param2)
{
	return eCan_abu_send_msg(dev, ABU_BL_CMD_ID, cmd, param1, p_param2);
}

/**
 * @brief  알람 박스 주기 메시지 처리
 * @param  
 * @return 
*/
int eCan_send_msg(can_msg_s *pMsg)
{
	if(pMsg != NULL)
	{
		return TBD_can_tx(&hfdcan1,pMsg->id,pMsg->data,MAX_CAN_DATA_SIZE);
	}
}

/**
 * @brief  
 * @param  
 * @return 
*/
void eCan_abu_process()
{
	can_msg_s tmp_can_msg;
	if(TBD_abcan_get_msg(&tmp_can_msg) != TBD_FAIL)
	{
		switch(tmp_can_msg.id & ABU_CAN_ID_MASK)
		{
			case AB_PERIODIC_ID:
				eCan_ab_proc_rx_periodic_msg(&tmp_can_msg, (tmp_can_msg.id & ABU_DEV_ID_MASK));
				break;
			case AB_EVENT_ID:
				eCan_ab_proc_rx_event_msg(&tmp_can_msg, (tmp_can_msg.id & ABU_DEV_ID_MASK));
				break;
			case AU_PERIODIC_ID:
				eCan_au_proc_rx_periodic_msg(&tmp_can_msg);
				break;
			case AU_EVENT_ID:
				eCan_au_proc_rx_event_msg(&tmp_can_msg);
				break;
			case LAB_PERIODIC_ID:
				eCan_lab_proc_rx_periodic_msg(&tmp_can_msg);
				break;
			case LAB_EVENT_ID:
				eCan_lab_proc_rx_event_msg(&tmp_can_msg);
				break;
		}
		switch (tmp_can_msg.id)
		{
			case ABU_ENTRY_BL_CMD_ACK_ID:
			case ABU_BL_ERROR_CODE_ID:
			case ABU_BL_CMD_ACK_ID:
				FirmwareUpdate_proc_can_cmd(tmp_can_msg);
				break;
		}
	}
}

/**
 * @brief  
 * @param  
 * @return 
*/
void eCan_process()
{
	eCan_abu_process();
}


