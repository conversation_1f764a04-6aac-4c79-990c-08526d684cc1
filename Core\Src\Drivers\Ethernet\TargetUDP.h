/**
******************************************************************************
* @file      TargetUDP.h
* <AUTHOR>
* @date      2024-6-10
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_DRIVERS_ETHERNET_TARGETUDP_H_
#define SRC_DRIVERS_ETHERNET_TARGETUDP_H_

#include "Common.h"
#include "lwip/opt.h"
#include "lwip/api.h"
#include "lwip/sys.h"
#include "netif.h"
#include "udp.h"
#include "tcp.h"
#include "igmp.h"

typedef enum
{
    UDP_TYPE_START = 0,

    UDP_SERVER_NAVTEX = UDP_TYPE_START,
    UDP_SERVER_BAM_1,
    UDP_SERVER_BAM_2,
    UDP_SERVER_CAM_1,
    UDP_SERVER_CAM_2,
    UDP_CLIENT_TEST_PC,

    UDP_TYPE_MAX,
}udp_ip_port_pcb_t;

typedef struct
{
    ip4_addr_t local_ip;
    u16_t local_port;
    ip4_addr_t remote_ip;
    u16_t remote_port;
    struct udp_pcb *pcb;
}udp_ip_port_pcb_s;

void Udp_Ip_Client_Config(void const *argument);
void Udp_Ip_Server_Config(void const *argument);
void Udp_Server_Send_Str(udp_ip_port_pcb_t type, char *pMsg);
void Udp_Server_Send_Data(udp_ip_port_pcb_t type, u8 *pData, u16 len);
void Udp_Server_Connect(udp_ip_port_pcb_t type);
void Udp_Server_Disconnect(udp_ip_port_pcb_t type);
void Udp_Client_Send_Str(udp_ip_port_pcb_t type, char *pMsg);
void Udp_Client_Send_Data(udp_ip_port_pcb_t type, u8 *pData, u16 len);
void Udp_Client_Connect(udp_ip_port_pcb_t type);
void Udp_Client_Disconnect(udp_ip_port_pcb_t type);
void Udp_Recv_Callback(void *arg, struct udp_pcb *upcb, struct pbuf *p, const ip_addr_t *addr, u16_t port);

void TargetUDP_Init(void const *argument);


#endif /* SRC_DRIVERS_ETHERNET_TARGETUDP_H_ */
