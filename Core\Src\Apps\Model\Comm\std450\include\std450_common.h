///@file     std450_common.h
///@brief    450 internal private common struct header.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#ifndef STD450_COMMON_H
#define STD450_COMMON_H

//==============================================================================
///@brief Unique system Function ID. IEC 61162-450 4.2.2 [ccxxxx]
///@param talker cc: IEC 61162-1 define characters.
///@param number xxxx: 4 numeric characters.
typedef struct sfi_s {
    char talker[2];
    char number[4];
}__attribute__((aligned(1), packed)) sfi_t;

///@brief SFI infomration struct.
///@param sf_count System function count in 450 device.
///@param sfi_list System function ID list.
typedef struct sfi_info_s{
    int sf_count;
    sfi_t *sfi_list;
}sfi_info_t;

//==============================================================================
///@brief UdPbc header structer
struct UdPbC_header_s {
        char header[C_LEN_450_TOKEN]; //UdPbc
}__attribute__((aligned(1), packed));

typedef struct UdPbC_header_s UdPbC_header_t;

///@brief UdPbc datagram structer.
struct UdPbC_data_s {
        UdPbC_header_t header;
        char           data[C_MAX_450_BUF-C_LEN_450_TOKEN] ;
}__attribute__((aligned(1), packed));

typedef struct UdPbC_data_s UdPbC_data_t;

#define UDPBC_MAX_ONE_SIZE (sizeof(struct UdPbC_data_t)) /// UdPbC maximum size [6.2.4 Datagram Size.]
//==============================================================================

#define C_BIN_MSG_TYPE_DATA  0x01 /// 7.3.3.5 Message Type DATA
#define C_BIN_MSG_TYPE_QUERY 0x02 /// 7.3.3.5 Message Type DATA
#define C_BIN_MSG_TYPE_ACK   0x03 /// 7.3.3.5 Message Type DATA

///@brief binary file header structer. 7.3.3 Table 9
struct bin_header_s {
        uint8_t  Token[6];      /// RaPbC or RrPbC or NkPgN
        uint16_t Version;       /// C_VER_BIN_HEADER=2 => Fix.
        uint16_t HeaderLength;  /// Header Length => Fix
        uint8_t  SrcID[6];      /// SFI [ccxxxx] => Get SF
        uint8_t  DestID[6];     /// SFI [ccxxxx] or [XXXXXX: No assigned DEST.] =>Set App.
        uint16_t Type;          /// MessageType[DATA:0x01][QUERY:0x02][ACK:0x03]=>Set App.
        uint32_t BlockID;	/// 0~4294967295.				=>Set App.
        uint32_t SequenceNum;   /// [1~MaxSequence]				=>Set App.
        uint32_t MaxSequence;	/// [QUERY or ACK =0] [ DATA: Number of split binary file block ]
        uint8_t  Device;	/// [1~255: Device ] 
        uint8_t  Channel;	/// [1~255: Subdivision Default:1]
}__attribute__((aligned(1), packed));

///@brief binary file description structer.  7.3.4 Table 10
struct bin_descript_s {
        uint32_t Length;	/// binary file descriptor length.
        uint32_t fileLength;    /// binary file total length.
        uint16_t StatusOfAcq;   /// [Nomal Status:0, Error Condtion: Other]
        uint16_t AckDestPort;   /// [Ack Port:60006,6008~60016,60021~60030]
        uint8_t  TypeLength;	/// Data Type Length: MIME Content type.
        uint8_t  *DataType;	/// MIME Content type. [String]
        uint16_t StatusLength;  /// Status and information text field in bytes.
        uint8_t  *StatusInfor;  /// Status information[Succesful operation or error code] Last is Null.
}__attribute__((aligned(1), packed));

typedef struct bin_header_s bin_header_t;    ///Typedef declaration binary header
typedef struct bin_header_s RaUdp_header_t;  ///Typedef declaration binary header to RaUdp
typedef struct bin_header_s RrUdp_header_t;  ///Typedef declaration binary header to RrUdp
typedef struct bin_header_s NkPgN_header_t;  ///Typedef declaration binary header to NkUdp
typedef struct bin_descript_s bin_descript_t;///Typedef declaration binary descriptor 

#define BIN_HEADER_SIZE  (sizeof(struct bin_header_s))  /// binary file transmit header size.
#define BIN_MAX_ONE_SIZE (C_MAX_450_BUF-BIN_HEADER_SIZE)  /// binary file transmit header size.
#define BIN_DESC_BASE_SIZE  (sizeof(struct bin_descript_s)-sizeof(uint8_t*)-sizeof(uint8_t*))

///@brief binary file tranmit message.
struct bin_data_s {
	bin_header_t header;            ///binary header.
	char data[BIN_MAX_ONE_SIZE] ;   ///binary datagram.
}__attribute__((aligned(1), packed));

typedef struct bin_data_s bin_data_t;
typedef struct bin_data_s RaUdP_data_t;
typedef struct bin_data_s RrUdP_data_t;
typedef struct bin_data_s NkPgN_data_t;

//==============================================================================
struct std450_datablock_s{
    char    data[C_MAX_450_BUF];
}/*__attribute__((aligned(1), packed))*/;
typedef struct std450_datablock_s std450_datablock_t;


#endif /* STD450_COMMON_H */
