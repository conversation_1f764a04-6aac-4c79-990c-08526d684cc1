/**
******************************************************************************
* @file      icomm_map.h
* <AUTHOR>
* @date      2024-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_INTERNAL_ICOMM_MAP_H_
#define SRC_APPS_MODEL_COMM_INTERNAL_ICOMM_MAP_H_

#include "TargetUart.h"
#include "Common.h"
#include "ecomm_map.h"
#include "FW_Update.h"

#define NMEA_0183_COM_MAX_LEN 256

#define COMM_STATE_RESET 0
#define COMM_STATE_SET 1
#define COMM_STATE_TIMEOUT 2

#define COMM_FRAME_LEN 8

// MAX_TARGET_UART_BUF_SIZE - 2048 form control board
// start byte 4
// parameter byte 4
// crc byte 2
#define COMM_ONLY_BYTE_MAX (2048-4-12-2)

typedef enum
{
    I_COMM_TX_TYPE_START                    = 0x00,

    I_COMM_TX_TYPE_SYSTEM_PARAMETER_GETTING_REQUEST = I_COMM_TX_TYPE_START,
    I_COMM_TX_TYPE_DIAG_RESP,
    I_COMM_TX_TYPE_NAVTEX_MESSAGE_SESSION,
    I_COMM_TX_TYPE_MSG_NMEA0183_SEND,
    I_COMM_TX_TYPE_FSK_DEMOD_RESP_TEST,
    I_COMM_TX_TYPE_CRC_RESP,
    I_COMM_TX_TYPE_PARAMETER_STATE,
    I_COMM_TX_TYPE_CAN_RECEIVED_DATA_PASS,
    I_COMM_TX_TYPE_SYSTEM_PARAMETER_SENDING_SUCCESS,
    I_COMM_TX_SYSTEM_SETTING_RESPONSE,
    I_COMM_TX_SYSTEM_ALL_PARAMETER_SEND,
    I_COMM_TX_NETWORK_SETTING_INFO,
    I_COMM_TX_RF_SELF_BER_TEST_RESPONSE,

    I_COMM_TX_FIRMWARE_UPDATE_ENTRY_UPDATE = FWU_CMD_ENTRY_UPDATE_MODE,
    I_COMM_TX_FIRMWARE_UPDATE_STATUS = FWU_CMD_UPDATE_STATUS,
    I_COMM_TX_FIRMWARE_UPDATE_DEVICE_SEARCH = FWU_CMD_DEVICE_SEARCH,
    I_COMM_TX_FIRMWARE_UPDATE_DEVICE_SEARCH_ACK = FWU_CMD_DEVICE_SEARCH_ACK,
    I_COMM_TX_FIRMWARE_UPDATE_MASTER_BASE = FWU_CMD_MASTER_BASE,
    I_COMM_TX_FIRMWARE_UPDATE_SLAVE_BASE = FWU_CMD_SLAVE_BASE,

    I_COMM_TX_TYPE_MAX,
}Internal_Comm_Tx_Type_t;

typedef enum
{
    I_COMM_RX_TYPE_START                    = 0x00,

    I_COMM_RX_TYPE_SYSTEM_PARAMETER_SENDING_SUCCESS = I_COMM_RX_TYPE_START,
    I_COMM_RX_TYPE_DIAG_REQ,
    I_COMM_RX_TYPE_NMEA0183_RECV,
    I_COMM_RX_TYPE_GPIO_SET,
    I_COMM_RX_TYPE_FSK_DEMOD_REQ_TEST,
    I_COMM_RX_TYPE_CRC_RESP,
    I_COMM_RX_TYPE_CAN_SEND,
    I_COMM_RX_COMM_CHECK,
    I_COMM_RX_TYPE_BAM_DATA_SEND,
    I_COMM_RX_TYPE_SYSTEM_PARAMETER_GETTING_REQUEST,
    I_COMM_RX_SYSTEM_SETTING_REQUEST,
    I_COMM_RX_SYSTEM_ALL_PARAMETER_SEND,
    I_COMM_RX_RF_SELF_BER_TEST_REQUEST,
    I_COMM_RX_RESET_REQUEST,

    I_COMM_RX_FIRMWARE_UPDATE_ENTRY_UPDATE = FWU_CMD_ENTRY_UPDATE_MODE,
    I_COMM_RX_FIRMWARE_UPDATE_STATUS = FWU_CMD_UPDATE_STATUS,
    I_COMM_RX_FIRMWARE_UPDATE_DEVICE_SEARCH = FWU_CMD_DEVICE_SEARCH,
    I_COMM_RX_FIRMWARE_UPDATE_DEVICE_SEARCH_ACK = FWU_CMD_DEVICE_SEARCH_ACK,
    I_COMM_RX_FIRMWARE_UPDATE_MASTER_BASE = FWU_CMD_MASTER_BASE,
    I_COMM_RX_FIRMWARE_UPDATE_SLAVE_BASE = FWU_CMD_SLAVE_BASE,

    I_COMM_RX_TYPE_MAX,
}Internal_Comm_Rx_Type_t;

typedef enum
{
    I_COMM_NAV_CMD_PARM_TYPE_NONE = 0x00,
    I_COMM_NAV_CMD_PARAM_TYPE_START,
    I_COMM_NAV_CMD_PARAM_TYPE_ID,
    I_COMM_NAV_CMD_PARAM_TYPE_MSG,
    I_COMM_NAV_CMD_PARAM_TYPE_NNNN,
    I_COMM_NAV_CMD_PARAM_TYPE_END,
    I_COMM_NAV_CMD_PARAM_TYPE_ERROR,
    I_COMM_NAV_CMD_PARAM_TYPE_LOCAL_FREQ_CHANGED,
}Internal_Comm_Navtex_Msg_Cmd_Param_t;

/******************************************************************************
 * Tx 
******************************************************************************/
typedef struct
{
    u8 data[COMM_ONLY_BYTE_MAX];
} Icomm_Only_Byte_Tx_s;

typedef struct
{
    u8 State;
    u8 Dummy_1;
    u8 Dummy_2;
    u8 Dummy_3;
} Icomm_System_Parameter_Getting_Request_Tx_s;

typedef struct
{
    u8 type;
    u8 state;
    u8 dummy_0;
    u8 dummy_1;
} Icomm_Diag_Resp_Tx_s;

typedef struct
{
    u8 Data_0;
    u8 Data_1;
    u8 Data_2;
    u8 Data_3;
    u8 Data_4;
    u8 Data_5;
    u8 Data_6;
    u8 Data_7;
} Icomm_NavMsg_Id_Tx_s;

typedef struct
{
    u8 Data_0;
    u8 Data_1;
    u8 Data_2;
    u8 Data_3;
} Icomm_NavMsg_Tx_s;

typedef struct
{
    u8 Data_0;
    u8 Data_1;
    u8 Data_2;
    u8 Data_3;
    u8 Data_4;
    u8 Data_5;
    u8 Data_6;
    u8 Data_7;
} Icomm_NavMsg_End_Tx_s;

typedef struct
{
    u8 data[NMEA_0183_COM_MAX_LEN];
} Icomm_Nmea0183_Data_Tx_s;

typedef struct
{
    u16 crc;
    u16 cmd;
} Icomm_Response_CRC_Tx_s;

typedef struct
{
    u8 ch;
    u8 total_count;
    u8 mark_count;
    u8 space_count;
} Icomm_Fsk_DeMod_Test_Resp_Tx_s;

typedef struct
{
    TimeDateFor4byte_s timedate;
    u8 Ver_Receive_Hw[4];
    u8 Ver_Receive_Sw[4];
    u16 Adc_Ant;
    u16 Adc_Int_Ch;
    u16 Adc_Loc_Ch;
    u16 Adc_VREF;
    u16 Adc_PCB_Ver_1;
    u16 Adc_PCB_Ver_2;
} Icomm_Parameter_State_Tx_s;

typedef struct
{
    u32 id;
    u8 data[8];
} Icomm_CAN_Received_Data_Pass_Tx_s;

typedef struct
{
    u8 type;
    u8 dummy;
    u16 data;
} Icomm_Adc_Response_Tx_s;

typedef struct
{
    u8 type;
    u8 Dummy_0;
    u8 Dummy_1;
    u8 Dummy_2;
    
    u8 ver[4];
} Icomm_Version_Send_Tx_s;

typedef struct
{
    u8 State;
    u8 Dummy_1;
    u8 Dummy_2;
    u8 Dummy_3;
} Icomm_System_Parameter_Sending_Success_Tx_s;

typedef struct
{
    u16 type;
    u16 size;
    u16 data[10];
} Icomm_System_Setting_Response_Tx_s;

typedef struct
{
    u8 eth_link;
    u8 mode;
    u8 dummy_0;
    u8 dummy_1;
    
    u8 ip[4];
    u8 netmask[4];
    u8 gateway[4];
} Icomm_Network_Setting_Info_Tx_s;

typedef struct
{
    u8 out_select;
    u8 ch_select;
    u8 mode;
    u8 vpp;

    u8 offset;
    u8 mark_cnt;
    u8 space_cnt;
    u8 tot_cnt;
} Icomm_Rf_Self_Test_Res_Tx_s;

/******************************************************************************
 * Rx 
******************************************************************************/
typedef struct
{
    u8 data[COMM_ONLY_BYTE_MAX];
} Icomm_Only_Byte_Rx_s;

typedef struct
{
    u8 State;
    u8 Dummy_1;
    u8 Dummy_2;
    u8 Dummy_3;
} Icomm_System_Ctrl_Rx_s;

typedef struct
{
    u8 type;
    u8 state;
    u8 dummy_0;
    u8 dummy_1;
} Icomm_Diag_Req_Rx_s;

typedef struct
{
    u8 data[NMEA_0183_COM_MAX_LEN];
} Icomm_Nmea0183_Data_Rx_s;

typedef struct
{
    u8 type;
    u8 state;
    u8 Dummy_0;
    u8 Dummy_1;
} Icomm_Gpio_Set_Rx_s;

typedef struct
{
    u8 ch;
    u8 total_count;
    u8 dummy_0;
    u8 dummy_1;
} Icomm_Fsk_DeMod_Test_Req_Rx_s;

typedef struct
{
    u16 crc;
    u16 cmd;
} Icomm_Response_CRC_Rx_s;

typedef struct
{
    u32 id;
    u8 data[8];
} Icomm_Can_Send_Rx_s;

typedef struct
{
    u8 dummy_0;
    u8 dummy_1;
    u8 dummy_2;
    u8 dummy_3;
} Icomm_Comm_Check_Rx_s;

typedef struct
{
    u8 data[NMEA_0183_COM_MAX_LEN];
} Icomm_Bam_Data_Rx_s;

typedef struct
{
    u8 State;
    u8 Dummy_1;
    u8 Dummy_2;
    u8 Dummy_3;
} Icomm_System_Parameter_Getting_Request_Rx_s;

typedef struct
{
    u16 type;
    u16 size;
    u16 data[10];
} Icomm_System_Setting_Request_Rx_s;

typedef struct
{
    u8 out_select;
    u8 ch_select;
    u8 mode;
    u8 vpp;

    u8 offset;
    u8 mark_cnt;
    u8 space_cnt;
    u8 tot_cnt;
} Icomm_Rf_Self_Test_Req_Rx_s;

typedef struct
{
    u8 controller_bank_swap_flag;
    u8 receiver_bank_swap_flag;

    u8 dummy_0;
    u8 dummy_1;
    u8 dummy_2;
    u8 dummy_3;
    u8 dummy_4;
    u8 dummy_5;
    u8 dummy_6;
    u8 dummy_7;
    u8 dummy_8;
    u8 dummy_9;
} Icomm_Reset_Req_Rx_s;

/*-----------------------------------------------------------------------------*/
/*-----------------------------------------------------------------------------*/
/******************************************************************************
 * Icomm Start frame struct
******************************************************************************/
typedef struct
{
    u8 Start_0;
    u8 Start_1;
    u8 Start_2;
    u8 Start_3;
}Icomm_StartFrame_s;

/******************************************************************************
 * Icomm header frame struct
******************************************************************************/
typedef struct
{
    u16 Frm_Len;
    u16 Seq_Num;
    u16 Cmd;
    u16 Cmd_Param;
}Icomm_HeaderFrame_s;

/******************************************************************************
 * Icomm data frame Tx struct
******************************************************************************/
typedef union
{
    Icomm_Only_Byte_Tx_s byte;
    Icomm_System_Parameter_Getting_Request_Tx_s System_Parameter_Getting_Request;
    Icomm_Diag_Resp_Tx_s DiagResp;
    Icomm_NavMsg_Tx_s NavMsg;
    Icomm_NavMsg_Id_Tx_s NavMsgId;
    Icomm_NavMsg_End_Tx_s NavMsgEnd;
    Icomm_Nmea0183_Data_Tx_s Nmea0183;
    Icomm_Response_CRC_Tx_s Response;
    Icomm_Fsk_DeMod_Test_Resp_Tx_s Test_Fsk;
    Icomm_Parameter_State_Tx_s Parameter_State;
    Icomm_CAN_Received_Data_Pass_Tx_s Can_Received;
    Icomm_Adc_Response_Tx_s Adc_Resp;
    Icomm_Version_Send_Tx_s Ver;
    Icomm_System_Parameter_Sending_Success_Tx_s Sys_Para_Sending_Success;
    Icomm_System_Setting_Response_Tx_s System_Setting_Resp;
    Icomm_Network_Setting_Info_Tx_s Network_Setting_Info;
    Icomm_Rf_Self_Test_Res_Tx_s Test_Self_Rf;
}Icomm_DataFrame_Tx_s;

/******************************************************************************
 * Icomm data frame Rx struct
******************************************************************************/
typedef union
{
    Icomm_Only_Byte_Rx_s byte;
    Icomm_System_Ctrl_Rx_s System;
    Icomm_Diag_Req_Rx_s DiagReq;
    Icomm_Nmea0183_Data_Rx_s Nmea0183;
    Icomm_Gpio_Set_Rx_s Gpio_Set;
    Icomm_Fsk_DeMod_Test_Req_Rx_s Test_Fsk;
    Icomm_Response_CRC_Rx_s Response;
    Icomm_CAN_Received_Data_Pass_Tx_s Can_Received;
    Icomm_Can_Send_Rx_s Can_Send;
    Icomm_Comm_Check_Rx_s Comm_Check;
    Icomm_Bam_Data_Rx_s Bam;
    Icomm_System_Parameter_Getting_Request_Rx_s Sys_Para_Getting_Req;
    Icomm_System_Setting_Request_Rx_s System_Setting_Req;
    Icomm_Rf_Self_Test_Req_Rx_s Test_Self_Rf;
    Icomm_Reset_Req_Rx_s Reset_Req;
}Icomm_DataFrame_Rx_s;

/******************************************************************************
 * Common
******************************************************************************/
typedef union
{
    u8 Buffer[MAX_TARGET_UART_BUF_SIZE];
    struct
    {
        Icomm_StartFrame_s st;
        Icomm_HeaderFrame_s hd;
        Icomm_DataFrame_Tx_s dt;
    } st;
} Icomm_Protocol_Tx_s;

typedef union
{
    u8 Buffer[MAX_TARGET_UART_BUF_SIZE];
    struct
    {
        Icomm_StartFrame_s st;
        Icomm_HeaderFrame_s hd;
        Icomm_DataFrame_Rx_s dt;
    } st;
} Icomm_Protocol_Rx_s;

/******************************************************************************
 * Status
******************************************************************************/
typedef struct
{
    int flag;
    int timeout_tick;
    int cur_tick;
} Icomm_Status_Tx_s;

typedef struct
{
    int flag;
    int timeout_tick;
    int cur_tick;
} Icomm_Status_Rx_s;

/******************************************************************************
 * Function's definitions and extern
******************************************************************************/
void icomm_map_Init(void);

void Reset_Tx_Icomm_Handshake_Status(int cmd);
void Set_Tx_Icomm_Handshake_Status(int cmd, int val);
int Get_Tx_Icomm_Handshake_Status(int cmd);

void Reset_Rx_Icomm_Handshake_Status(int cmd, int timeout_tick);
void Set_Rx_Icomm_Handshake_Status(int cmd, int val);
int Get_Rx_Icomm_Handshake_Status(int cmd);

extern Icomm_Protocol_Tx_s gIcommData_Tx;
extern Icomm_Protocol_Rx_s gIcommData_Rx;
extern Icomm_Status_Tx_s gIcommStatus_Tx[I_COMM_TX_TYPE_MAX];
extern Icomm_Status_Rx_s gIcommStatus_Rx[I_COMM_RX_TYPE_MAX];

extern int gIcommConfig_Rx_Tb[][2];

#endif /* SRC_APPS_MODEL_COMM_INTERNAL_ICOMM_MAP_H_ */
