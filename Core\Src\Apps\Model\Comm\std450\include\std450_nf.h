///@file     std450_nf.h
///@brief    IEC 61162-450 Network Function (NF) block behavior code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#ifndef STD450_NF_H
#define STD450_NF_H

#include <std450.h>

STD450_BEGIN_DECLS

#if (USE_LINUX == 1)
#define NF_SRP_EVENT_SIG_NO     (SIGRTMAX)
#define NF_DRATE_EVENT_SIG_NO   (SIGRTMAX-1)
#else
#define NF_SRP_EVENT_SIG_NO     (90)    // Dummy value
#define NF_DRATE_EVENT_SIG_NO   (89)    // Dummy value
#endif

#define STD_DATARATE_PERIOD 10  ///10 sec.

#define NF_ERR_LOG_MAX_INDEX 99999999   ///
#define NF_ERR_LOG_PREFIX   "err_log_"  /// Error log list file name prefix.
#define NF_ERR_LOG_PATH_LEN (1024)      /// Error log list file directory path max. length;

STD450_API int  std450_new_nf(std450_t *dev450, const char *nic, const char *ip,  int igmp_ver);
STD450_API void std450_free_nf(std450_t *dev450);
STD450_API void std450_nf_datarate_init(std450_t *dev450, int period);
STD450_API int  std450_nf_datarate_get(std450_t *dev450);
STD450_API int  std450_nf_error_list_dir_set(std450_t * dev450, char *dir);
STD450_API int  std450_nf_error_addr_set(std450_t *dev450, int port, const char *ip);
STD450_API int  std450_nf_srp_addr_set(std450_t *dev450, int port, const char *ip);
STD450_API int  std450_nf_sock_open(std450_t *dev450);
STD450_API void std450_nf_sock_close(std450_t *dev450);
STD450_API void std450_nf_proc_run(std450_t *dev450);
STD450_API void std450_nf_proc_stop(std450_t *dev450);

STD450_END_DECLS


#endif  /* STD450_NF_H */