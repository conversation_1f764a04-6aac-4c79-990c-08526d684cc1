/**
******************************************************************************
* @file      TargetG729.c
* <AUTHOR>
* @date      2024-05-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/
#include "model.h"
#include "TargetBoard.h"

sDrvG729Component gDrvG729 = {0};

int G729_Encode_Apnd_Data_Buffer(i16 i_data)
{
    sDrvG729Component *pG = &gDrvG729;

    pG->encode.in_data_buffer[pG->encode.in_data_front] = i_data;

    pG->encode.in_data_front += 1;
    if(pG->encode.in_data_front >= G729_BUFFER_SIZE)
    {
        pG->encode.in_data_front -= G729_BUFFER_SIZE;
    }

    return 1;
}

i32 G729_Encode_Read_Data_Buffer(void)
{
    i32 data = 0xFFFF;
    sDrvG729Component *pG = &gDrvG729;

    if(pG->encode.in_data_front != pG->encode.in_data_rear)
    {
        data = (i32)pG->encode.in_data_buffer[pG->encode.in_data_rear];

        pG->encode.in_data_rear += 1;
        if(pG->encode.in_data_rear >= G729_BUFFER_SIZE)
        {
            pG->encode.in_data_rear -= G729_BUFFER_SIZE;
        }

        return data;
    }

    return data;
}

int G729_Encode_Apnd_Out_Data_Buffer(u8 *pData, u8 len)
{
    sDrvG729Component *pG = &gDrvG729;
    u8 i = 0;

    for(i=0; i<len; i++)
    {
        pG->encode.out_data_buffer[pG->encode.out_data_front] = pData[i];

        pG->encode.out_data_front += 1;
        if(pG->encode.out_data_front >= G729_BUFFER_SIZE)
        {
            pG->encode.out_data_front -= G729_BUFFER_SIZE;
        }
    }

    return 1;
}

int G729_Encode_Read_Out_Data_Buffer(void)
{
    sDrvG729Component *pG = &gDrvG729;
    int data = -1;

    if(pG->encode.out_data_front != pG->encode.out_data_rear)
    {
        data = (int)pG->encode.out_data_buffer[pG->encode.out_data_rear];

        pG->encode.out_data_rear += 1;
        if(pG->encode.out_data_rear >= G729_BUFFER_SIZE)
        {
            pG->encode.out_data_rear -= G729_BUFFER_SIZE;
        }

        return data;
    }

    return data;
}

int G729_Decode_Apnd_Data_Buffer(u8 *pData, u8 len)
{
	int ret = 0;
    u8 i = 0;
    sDrvG729Component *pG = &gDrvG729;

    for(i=0; i<len; i++)
    {
        pG->decode.in_data_buffer[pG->decode.in_data_front] = pData[i];

        pG->decode.in_data_front += 1;
        if(pG->decode.in_data_front >= G729_BUFFER_SIZE)
        {
            pG->decode.in_data_front -= G729_BUFFER_SIZE;
        }
    }

    return ret;
}

i32 G729_Decode_Read_Data_Buffer(void)
{
    i32 data = 0xFFFF;
    sDrvG729Component *pG = &gDrvG729;

    if(pG->decode.in_data_front != pG->decode.in_data_rear)
    {
        data = (i32)pG->decode.in_data_buffer[pG->decode.in_data_rear];

        pG->decode.in_data_rear += 1;
        if(pG->decode.in_data_rear >= G729_BUFFER_SIZE)
        {
            pG->decode.in_data_rear -= G729_BUFFER_SIZE;
        }

        return data;
    }

    return data;
}

int G729_Decode_Apnd_Out_Data_Buffer(i16 *pData, u8 len)
{
	int ret = 0;
    u8 i = 0;
    sDrvG729Component *pG = &gDrvG729;

    for(i=0; i<len; i++)
    {
        pG->decode.out_data_buffer[pG->decode.out_data_front] = pData[i];

        pG->decode.out_data_front += 1;
        if(pG->decode.out_data_front >= G729_BUFFER_SIZE)
        {
            pG->decode.out_data_front -= G729_BUFFER_SIZE;
        }
    }

    return ret;
}

i32 G729_Decode_Read_Out_Data_Buffer(void)
{
    i32 data = 0xFFFF;
    sDrvG729Component *pG = &gDrvG729;

    if(pG->decode.out_data_front != pG->decode.out_data_rear)
    {
        data = (i32)pG->decode.out_data_buffer[pG->decode.out_data_rear];

        pG->decode.out_data_rear += 1;
        if(pG->decode.out_data_rear >= G729_BUFFER_SIZE)
        {
            pG->decode.out_data_rear -= G729_BUFFER_SIZE;
        }

        return data;
    }

    return data;
}


void G729_Encode_Run(void)
{
    i32 data = 0xFFFF;
    sDrvG729Component *pG = &gDrvG729;
    u8 SysMode = g_hSysStatus.m_pStat->mode.system;
    if(SysMode != SYS_BOOT_RUN)
    {
        return;
    }

    
    do
    {
        data = G729_Encode_Read_Data_Buffer();
        if(data == 0xFFFF)
        {
            return;
        }

        pG->encode.in_enc_buffer[pG->encode.in_enc_cnt ++] = (i16)data;
        if(pG->encode.in_enc_cnt >= G729_ENCODE_IN_SIZE)
        {
            //HAL_GPIO_WritePin(DO_FSK_CHECK_INT_GPIO_Port, DO_FSK_CHECK_INT_Pin, 1);
            g729a_encoder(  &pG->encode.state,
                            pG->encode.in_enc_buffer,        // in
                            pG->encode.out_enc_buffer,       // out
                            &pG->encode.frame_size );
            //HAL_GPIO_WritePin(DO_FSK_CHECK_INT_GPIO_Port, DO_FSK_CHECK_INT_Pin, 0);

            TargetCan_Audio_Data_Tx(&pG->encode.out_enc_buffer[0], 8);
            TargetCan_Audio_Data_Tx(&pG->encode.out_enc_buffer[8], 2);
            //G729_Encode_Apnd_Out_Data_Buffer(pG->encode.out_enc_buffer, 10);

            G729_Decode_Apnd_Data_Buffer(pG->encode.out_enc_buffer, 10);

            pG->encode.in_enc_cnt -= G729_ENCODE_IN_SIZE;
        }
    } while (data != 0xFFFF);
}


void G729_Decode_Run(void)
{
    i32 data = 0xFFFF;
    sDrvG729Component *pG = &gDrvG729;
    u8 SysMode = g_hSysStatus.m_pStat->mode.system;
    if(SysMode != SYS_BOOT_RUN)
    {
        return;
    }
    
    do
    {
        data = G729_Decode_Read_Data_Buffer();
        if(data == 0xFFFF)
        {
            return;
        }

        pG->decode.in_dec_buffer[pG->decode.in_dec_cnt ++] = (i16)data;
        if(pG->decode.in_dec_cnt >= G729_DECODE_IN_SIZE)
        {
            pG->decode.frame_size = 10;

            g729a_decoder(  &pG->decode.state,
                            pG->decode.in_dec_buffer,       // in
                            pG->decode.out_dec_buffer,      // out
                            pG->decode.frame_size );

            G729_Decode_Apnd_Out_Data_Buffer(pG->decode.out_dec_buffer, 80);

            pG->decode.in_dec_cnt -= G729_DECODE_IN_SIZE;
        }
    } while (data != 0xFFFF);
}

void TargetG729_Encode_Init(void)
{
	gDrvG729.encode.dtx_enable = 0;

    g729a_encoder_init(&gDrvG729.encode.state, gDrvG729.encode.dtx_enable);
}

void TargetG729_Decode_Init(void)
{
    g729a_decoder_init(&gDrvG729.decode.state);
}

void TargetG729_Init(void)
{
    memset(&gDrvG729, 0, sizeof(sDrvG729Component));

    TargetG729_Encode_Init();
    TargetG729_Decode_Init();
}
