/**
******************************************************************************
* @file      system_config.h
* <AUTHOR>
* @date      2023-09-05
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_SYSTEM_CONFIG_H_
#define SRC_APPS_MODEL_SYSTEM_CONFIG_H_

typedef struct
{
	int year;
	int month;
	int day;
}Date_s;

typedef struct {
	int hour;
	int min;
	int sec;
	int mili_sec;
}Time_s;

/*******************************************************************************************
  * @brief enums
*******************************************************************************************/
typedef enum
{
    GPIO_TYPE_NONE = 0,

    GPIO_TYPE_RF_RELAY,
    GPIO_TYPE_ANT_POWER,
    GPIO_TYPE_ALARM_RELAY,
    GPIO_TYPE_4M_FREQ_GENERATOR,
} GPIO_TYPE_t;

typedef enum
{
    GPIO_STATE_NONE = 0,

    GPIO_STATE_HIGH,
    GPIO_STATE_LOW,
} GPIO_STATE_t;

typedef enum
{
    ADC_TYPE_NONE = 0,

    ADC_TYPE_ANT_SENSE,
    ADC_TYPE_RF_INT,
    ADC_TYPE_RF_LOCAL,

    ADC_TYPE_MAX,
} ADC_Type_t;

typedef enum
{
    SYS_ERR_TYPE_NONE,

    SYS_ERR_TYPE_RECV_UART_CH_CTRL,
    SYS_ERR_TYPE_RECV_UART_CH_INS,
    SYS_ERR_TYPE_RECV_UART_CH_BAM,
    SYS_ERR_TYPE_RECV_UART_CH_DEBUG,
    SYS_ERR_TYPE_RECV_ADC1,
    SYS_ERR_TYPE_RECV_ADC3,
    SYS_ERR_TYPE_CTRL_UART_CH_RECV,
    SYS_ERR_TYPE_CTRL_UART_CH_PRINT,
    SYS_ERR_TYPE_CTRL_UART_CH_DEBUG,
    SYS_ERR_TYPE_CTRL_ADC3,
} System_Error_Value_t;

typedef enum
{
    VER_HW_TYPE_NONE = 0,

    VER_HW_TYPE_VER = VER_HW_TYPE_NONE,
    VER_HW_TYPE_REV,
} VER_HW_t;

typedef enum
{
    VER_SW_TYPE_NONE = 0,

    VER_SW_TYPE_MAJOR = VER_SW_TYPE_NONE,
    VER_SW_TYPE_MINOR,
    VER_SW_TYPE_REV,
    VER_SW_TYPE_RC,
} VER_SW_t;

typedef enum
{
    VER_TYPE_NONE = 0,

    VER_TYPE_CTRL_SW,
    VER_TYPE_CTRL_HW,
    VER_TYPE_RECV_SW,
    VER_TYPE_RECV_HW,
    VER_TYPE_ALARM_UNIT_SW,
    VER_TYPE_ALARM_UNIT_HW,
} VER_TYPE_t;

typedef enum
{
    UNIT_TYPE_NONE = 0,
    
    UNIT_TYPE_CONTROL,
    UNIT_TYPE_RECEIVE,
    UNIT_ALARM_UNIT,
} SYSTEM_UNIT_t;

typedef enum
{
    COMM_CMD_SYS_SET_NONE,
    COMM_CMD_SYS_SET_NAVTEX_LOC_CH = COMM_CMD_SYS_SET_NONE,
    COMM_CMD_SYS_SET_ANT_TYPE,
    COMM_CMD_SYS_SET_INS_MODE_SELECT,
    COMM_CMD_SYS_SET_INS_BPS_SELECT,
    COMM_CMD_SYS_SET_BAM_MODE_SELECT,
    COMM_CMD_SYS_SET_BAM_BPS_SELECT,
    COMM_CMD_SYS_SET_PRINT_IP_SEARCH,
    COMM_CMD_SYS_SET_NETWORK_MODE,
    COMM_CMD_SYS_SET_NETWORK_IP_ADDR,
    COMM_CMD_SYS_SET_NETWORK_NETMASK,
    COMM_CMD_SYS_SET_NETWORK_GATEWAY,
    COMM_CMD_SYS_SET_NETWORK_SFI_ID,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_MISC,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_TGTD,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_SATD,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_NAVD,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_VDRD,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_RCOM,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_TIME,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_PROP,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_1,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_2,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_3,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_4,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_5,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_6,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_7,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_8,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_BAM_1,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_BAM_2,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_CAM_1,
    COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_CAM_2,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_MISC,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_TGTD,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_SATD,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_NAVD,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_VDRD,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_RCOM,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_TIME,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_PROP,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_1,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_2,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_3,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_4,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_5,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_6,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_7,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_8,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_BAM_1,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_BAM_2,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_CAM_1,
    COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_CAM_2,
    COMM_CMD_SYS_SET_MAX,
} Comm_Cmd_System_Settings_t;

typedef enum
{
    MV_CH_ANT_TYPE_NONE = 0,
    MV_CH_ANT_TYPE_ACTIVE = MV_CH_ANT_TYPE_NONE,
    MV_CH_ANT_TYPE_PASSIVE,
    MV_CH_ANT_TYPE_MAX,
} menu_val_antenna_type_t;

typedef enum
{
    MV_CH_LOC_NONE = 0,
    MV_CH_LOC_490KHZ = MV_CH_LOC_NONE,
    MV_CH_LOC_4209_5KHZ,
    MV_CH_LOC_MAX,
} menu_val_ch_t;

typedef enum
{
    MV_VAL_ONOFF_NONE = 0,
    MV_VAL_OFF = MV_VAL_ONOFF_NONE,
    MV_VAL_ON,
    MV_VAL_MAX,
} menu_val_onoff_t;

typedef enum
{
    MV_VAL_BPS_NONE = 0,
    MV_VAL_BPS_4800 = 4800,
    MV_VAL_BPS_38400 = 38400,
    MV_VAL_BPS_MAX,
} menu_val_bps_t;

typedef enum
{
    MV_VAL_MODE_NONE = 0,
    MV_VAL_MANUAL = MV_VAL_MODE_NONE,
    MV_VAL_AUTO,
    MV_VAL_MODE_MAX,
} menu_val_mode_t;

typedef enum
{
    MV_VAL_COMM_NONE = 0,
    MV_VAL_COMM_RS232 = MV_VAL_COMM_NONE,
    MV_VAL_COMM_LAN,
    MV_VAL_COMM_MAX,
} menu_val_comm_mode_t;

typedef enum
{
    MV_VAL_PRINT_MODEL_NONE = 0,
    MV_VAL_PRINT_MODEL_SRP_350PLUSIII = MV_VAL_PRINT_MODEL_NONE,

    MV_VAL_PRINT_MODEL_MAX,
} menu_val_print_model_t;

typedef enum
{
    MV_VAL_PRINT_BPS_NONE = 0,
    MV_VAL_PRINT_BPS_4800 = MV_VAL_PRINT_BPS_NONE,
    MV_VAL_PRINT_BPS_9600,
    MV_VAL_PRINT_BPS_115200,

    MV_VAL_PRINT_BPS_MAX,
} menu_val_print_bps_t;

typedef enum
{
    MV_VAL_LAN_COMM_NONE = 0,
    MV_VAL_LAN_COMM_DHCP = MV_VAL_LAN_COMM_NONE,
    MV_VAL_LAN_COMM_STATIC,
    MV_VAL_LAN_COMM_MAX,
} menu_val_lan_comm_mode_t;

typedef enum
{
    MV_VAL_TIME_MODE_NONE = 0,
    MV_VAL_TIME_MODE_LOCAL = MV_VAL_TIME_MODE_NONE,
    MV_VAL_TIME_MODE_GPS,
    MV_VAL_TIME_MODE_MAX,
} menu_val_time_mode_t;

typedef enum
{
    MV_VAL_TIME_TYPE_NONE = 0,
    MV_VAL_TIME_TYPE_12H = MV_VAL_TIME_TYPE_NONE,
    MV_VAL_TIME_TYPE_24H,
    MV_VAL_TIME_TYPE_MAX,
} menu_val_time_type_t;

typedef enum
{
    MV_VAL_TIME_AM_PM_TYPE_NONE = 0,
    MV_VAL_TIME_TYPE_AM = MV_VAL_TIME_AM_PM_TYPE_NONE,
    MV_VAL_TIME_TYPE_PM,
    MV_VAL_TIME_AM_PM_TYPE_MAX,
} menu_val_local_time_am_pm_type_t;

typedef enum
{
    MV_VAL_UTC_ADJUST_MODE_NONE = 0,
    MV_VAL_UTC_ADJUST_INC = MV_VAL_UTC_ADJUST_MODE_NONE,
    MV_VAL_UTC_ADJUST_DEC,
    MV_VAL_UTC_ADJUST_MODE_MAX,
} menu_val_utc_adjust_type_t;

typedef enum
{
    MV_VAL_DATE_TYPE_NONE = 0,
    MV_VAL_DATE_TYPE_YYYY_MM_DD = MV_VAL_DATE_TYPE_NONE,
    MV_VAL_DATE_TYPE_MM_DD_YYYY,
    MV_VAL_DATE_TYPE_DD_MM_YYYY,
    MV_VAL_DATE_TYPE_MAX,
} menu_val_date_type_t;

typedef enum
{
    MV_VAL_THEME_NONE = 0,
    MV_VAL_THEME_DAY = MV_VAL_THEME_NONE,
    MV_VAL_THEME_NIGHT,
    MV_VAL_THEME_DUSK,
    MV_VAL_THEME_MAX,
} menu_val_theme_type_t;

typedef enum
{
    MV_VAL_STD450_TG_MISC = 0,  // SF not explicitly listed below
    MV_VAL_STD450_TG_TGTD,      // Target data (AIS), tracked target messages (Radar)
    MV_VAL_STD450_TG_SATD,      // High update rate, for example ship heading, attitude data. 
    MV_VAL_STD450_TG_NAVD,      // Navigational output other than that of TGTD and SATD groups
    MV_VAL_STD450_TG_VDRD,      // Data required for the VDR according to IEC 61996
    MV_VAL_STD450_TG_RCOM,      // Radio communication equipment
    MV_VAL_STD450_TG_TIME,      // Time transmitting equipment
    MV_VAL_STD450_TG_PROP,      // Proprietary and user specified SFs
    MV_VAL_STD450_TG_USR_1,     // User defined transmission group 1 to 8
    MV_VAL_STD450_TG_USR_2,
    MV_VAL_STD450_TG_USR_3,
    MV_VAL_STD450_TG_USR_4,
    MV_VAL_STD450_TG_USR_5,
    MV_VAL_STD450_TG_USR_6,
    MV_VAL_STD450_TG_USR_7,
    MV_VAL_STD450_TG_USR_8,
    MV_VAL_STD450_TG_BAM_1,     // Optionally, BAM compliant alert source reporting to CAM
    MV_VAL_STD450_TG_BAM_2,     
    MV_VAL_STD450_TG_CAM_1,     // CAM of the BAM
    MV_VAL_STD450_TG_CAM_2,
    MV_VAL_STD450_TG_MAX,
} menu_val_network_std450_trans_group_t;

/*******************************************************************************************
  * @brief Navtex settings
*******************************************************************************************/
typedef enum
{
   NAV_STATION_MODE_NONE = 0,
   NAV_STATION_MODE_MANUAL = NAV_STATION_MODE_NONE,
   NAV_STATION_MODE_AUTO,
   NAV_STATION_MODE_MAX,
} Navtex_Settings_Station_Manual_to_Auto_t;

/*******************************************************************************************
  * @brief Dimmer Component
*******************************************************************************************/
#define MIN_DIMM        1
#define MAX_DIMM        10
#define DIMM_STEP       1
#define DEFT_DIMM_DAY   5
#define DEFT_DIMM_NIGHT 10

typedef enum
{
    DIMM_MODE_TYPE_NONE = 0x00,
    DIMM_MODE_TYPE_MANUAL = DIMM_MODE_TYPE_NONE,
    DIMM_MODE_TYPE_AUTO,
    DIMM_MODE_TYPE_MAX,
} Dimm_Mode_t;

typedef struct 
{
    int nPreMode;
    int mmu_Mode;
    int cur_Mode;
    int nValue;
} Dimmer_s;

#define A_TO_Z_NUM 26
typedef struct
{
  u8 ch_set_local;

  u8 ch_set_518khz_station[A_TO_Z_NUM];
  u8 ch_set_518khz_station_print[A_TO_Z_NUM];
  u8 ch_set_518khz_station_ins[A_TO_Z_NUM];
  u8 ch_set_518khz_message[A_TO_Z_NUM];
  u8 ch_set_518khz_message_print[A_TO_Z_NUM];
  u8 ch_set_518khz_message_ins[A_TO_Z_NUM];
  u8 ch_set_518khz_station_manual_to_auto;
  u8 ch_set_518khz_automatic_station[A_TO_Z_NUM];

  u8 ch_set_490khz_station[A_TO_Z_NUM];
  u8 ch_set_490khz_station_print[A_TO_Z_NUM];
  u8 ch_set_490khz_station_ins[A_TO_Z_NUM];
  u8 ch_set_490khz_message[A_TO_Z_NUM];
  u8 ch_set_490khz_message_print[A_TO_Z_NUM];
  u8 ch_set_490khz_message_ins[A_TO_Z_NUM];
  u8 ch_set_490khz_station_manual_to_auto;
  u8 ch_set_490khz_automatic_station[A_TO_Z_NUM];

  u8 ch_set_42095khz_station[A_TO_Z_NUM];
  u8 ch_set_42095khz_station_print[A_TO_Z_NUM];
  u8 ch_set_42095khz_station_ins[A_TO_Z_NUM];
  u8 ch_set_42095khz_message[A_TO_Z_NUM];
  u8 ch_set_42095khz_message_print[A_TO_Z_NUM];
  u8 ch_set_42095khz_message_ins[A_TO_Z_NUM];
  u8 ch_set_42095khz_automatic_station[A_TO_Z_NUM];
  u8 ch_set_42095khz_station_manual_to_auto;

} Navtex_Settings_Type_t;

/*******************************************************************************************
  * @brief Gpio
*******************************************************************************************/
typedef struct
{
    int recv_rf_relay;
    int recv_ant_power;
    int recv_alarm_relay;
    int recv_4m_freq_gen;
} RecvGPIO_s;

/*******************************************************************************************
  * @brief INS Settings
*******************************************************************************************/
typedef struct
{
    int serial_port;
    int mode;
    int lan_port;
    uint32_t bps;
} INS_Setting_s;

/*******************************************************************************************
  * @brief BAM Settings
*******************************************************************************************/
typedef struct
{
    int serial_port;
    int mode;
    int lan_port;
    uint32_t bps;
} BAM_Setting_s;

/*******************************************************************************************
  * @brief Print Settings
*******************************************************************************************/
typedef struct
{
    int model;
    int mode;
    int bps_sel;
    int ip[4];
} Print_Setting_s;

/*******************************************************************************************
  * @brief Network Settings
*******************************************************************************************/
typedef struct
{
    int mode;
    int ip[4];
    int netmask[4];
    int gateway[4];
    int static_ip[4];
    int static_netmask[4];
    int static_gateway[4];
    char sfi_id[8];
    int tx_group[20];
    int rx_group[20];
} Network_Setting_s;

/*******************************************************************************************
  * @brief Audio Settings
*******************************************************************************************/
typedef struct
{
    int key_beep_mode;
    int key_beep_volume;
    int noti_mode;
    int noti_volume;
    int warning_mode;
    int warning_volume;
} Audio_Setting_s;

/*******************************************************************************************
  * @brief DateTime Settings
*******************************************************************************************/
typedef struct
{
    int mode;
    int date_type;
    int time_type;
    int time_am_pm_type;
    Date_s loc_date;
    Time_s loc_time;
    Time_s utc_offset;
    int utc_adjust_mode;
} DateTime_Setting_s;

/*******************************************************************************************
  * @brief Antenna Settings
*******************************************************************************************/
typedef struct
{
    int type;
} Antenna_Setting_s;

/*******************************************************************************************
  * @brief Alarm Relay
*******************************************************************************************/
typedef struct
{
    int status;
} Alarm_Relay_s;

/*******************************************************************************************
  * @brief System Config Component and Handler
*******************************************************************************************/
typedef struct
{
    Navtex_Settings_Type_t nav;
    Dimmer_s dimm;
    RecvGPIO_s gpio;
    INS_Setting_s ins;
    BAM_Setting_s bam;
    Print_Setting_s print;
    Network_Setting_s network;
    Audio_Setting_s audio;
    DateTime_Setting_s datetime;
    Antenna_Setting_s antenna;
    Alarm_Relay_s alarm_relay;

    unsigned long nor_roll_store_cnt;
    int nLang;
    int theme;
    int MsgListSortMode;
} SystemConfig;

typedef struct
{
    SystemConfig *m_pCfg;
    int (*InitSysCfg)(SystemConfig *pCfg);

    SystemConfig *(*GetSysCfg)(void);
} SysConfig_Handler;

/*******************************************************************************************
  * @brief Definition Functions List
*******************************************************************************************/
void SystemConfigHandler_Init(void);

/*******************************************************************************************
  * @brief External Components
*******************************************************************************************/
extern SystemConfig        g_SysConfig __attribute__((aligned(4)));
extern SysConfig_Handler   g_hSysCfg __attribute__((aligned(4)));

#endif /* SRC_APPS_MODEL_SYSTEM_CONFIG_H_ */
