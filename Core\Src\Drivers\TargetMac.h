/**
******************************************************************************
* @file      TargetMac.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_DRIVERS_TARGETMAC_H_
#define SRC_DRIVERS_TARGETMAC_H_

#include "Common.h"

#define TBD_MAC_DEV_ADDR				0xA0
#define TBD_MAC_CMD_READ_ADDR			0xFA
#define TBD_MAC_TIMEOUT					0xFF
#define MAX_MAC_ADDR_SIZE				6

typedef struct
{
    u8 Addr[6];
}sDrvMacComponent;
extern sDrvMacComponent gDrvMac;

void TBD_Init_Eeprom(void);
void TBD_Eeprom_Mac_Read(u8 *pMac);

#endif /* SRC_DRIVERS_TARGETMAC_H_ */
