/**
 * @file      Coef3000HzIirLP.c
 * <AUTHOR>
 * @brief     VHF Main Function
 * @version   0.1
 * @date      2022-08-31
 *
 * @copyright Copyright (c) 2022
 *
 */


//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#include "AllConst.h"
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
//static const double vCoeffA[FLT_3000HZ_IIR_LP_SIZE + 0] = {-8.634671478118050, +33.991391102249500, -80.276623822710600, +125.883119207990000, -136.892263339664000, +104.510460545888000, -55.295720912121800, +19.400129951999800, -4.074886155722610, +0.389074583244990};
//------_------_------_------_------_------_------_------//
//      must invert the sign of the coefficients A       //
//------_------_------_------_------_------_------_------//
  static const double vCoeffA[FLT_3000HZ_IIR_LP_SIZE + 0] = {+8.634671478118050, -33.991391102249500, +80.276623822710600, -125.883119207990000, +136.892263339664000, -104.510460545888000, +55.295720912121800, -19.400129951999800, +4.074886155722610, -0.389074583244990};
  static const double vCoeffB[FLT_3000HZ_IIR_LP_SIZE + 1] = {+0.000549270772616,  -0.003573439856216,  +0.011255701026307,   -0.022750684952643,   +0.033287441614002,   -0.037526905313802,  +0.033287441614002,  -0.022750684952643, +0.011255701026307, -0.003573439856216, +0.000549270772616};
 //-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
tBaseIIR    G_xIirTx3000HzLP =
          {
            .dSizeB = FLT_3000HZ_IIR_LP_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };
 //-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
tBaseIIR    G_xIirRx3000HzLP =
          {
            .dSizeB = FLT_3000HZ_IIR_LP_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

