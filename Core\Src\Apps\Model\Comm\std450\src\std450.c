///@file     std450.c
///@brief    450 library api fuction file.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#include <stdio.h>
#include <stdlib.h> 
#include <string.h>
#include <std450.h>
#include <std450_private.h>

///@brief IEC 61162-450 device initialize. Using NF or ONF initialize.
///@param dev450 std450 allocate address.
///@todo  Init pointer set.
void _std450_init_common(std450_t *dev450)
{
    dev450->idx = 0;
    memset(dev450->name, 0x00, LEN_DEV_NAME); 
}

/// @brief    IEC 61162-450 device new allocate.
/// @return   allocate memory address.
STD450_API std450_t* std450_new(void)
{
    std450_t *dev450 = (std450_t *)MEM_MALLOC(sizeof(std450_t));
    memset(dev450, 0x00, sizeof(std450_t));

    if(dev450 == NULL)
    {
        DEBUG_LOG("[Error] dev450 null pointer\r\n");
        return NULL;
    }

    _std450_init_common(dev450);
    return dev450;
}

/// @brief    IEC 61162-450 device free.
/// @param    dev450 std450 allocate address.
STD450_API void std450_mem_free(std450_t *dev450)
{
    if (dev450 == NULL)
    {
        DEBUG_LOG("[Error] dev450 null pointer\r\n");
        return;
    }

    if(dev450->sfi_info.sfi_list != NULL)
    {
        MEM_FREE(dev450->sfi_info.sfi_list);
        dev450->sfi_info.sfi_list = NULL;
    }

    MEM_FREE(dev450);
    dev450 = NULL;
}

/// @brief IEC 61162-450 device name set.[Internal]
/// @param dev450 std450 allocate address.
/// @param name set name buffer.
STD450_API int std450_dev_name_set(std450_t *dev450, char *name)
{
    if(strlen(name) > LEN_DEV_NAME)
    {
        DEBUG_LOG("[Error] too long device name\r\n");
        return -1;
    }

    strcpy(dev450->name, name);
    return 0;
}

/// @brief IEC 61162-450 device name get.[Internal]
/// @param dev450 std450 allocate address.
/// @param name get name buffer.
STD450_API int std450_dev_name_get(std450_t *dev450, char *name)
{
    strcpy(name, dev450->name);
    return 0;
}

///@brief IEC 61162-450 device open.
///@param IEC450_info std450 allocate info.
///@todo  Init 450 device and set transmission groups
std450_t* std450_open(IEC450_Info *IEC450_info)
{
    std450_t *dev450 = std450_new();
    int nCH;
    int check;
    int nPort;
    char Ip_Addr[NI_MAXHOST] = "***********";

    if (std450_new_sf(dev450, C_MAX_CH_NUM) == STD450_SUCCESS)
    {
        for (nCH = 0; nCH < C_MAX_CH_NUM; nCH++)
        {
            nPort = 60000;
        
            // Check Transmission Group
            if (IEC450_info->CH_INFO[nCH].Transmission_Group < TRANSGROUP_MISC ||
                IEC450_info->CH_INFO[nCH].Transmission_Group > TRANSGROUP_RBF5)
            {
                DEBUG_LOG("[%s] Invalid transmission group!\r\n", __FUNCTION__);
                return 0;
            }

            // Allocate Multicast IP and Port as Transmission Group
            sprintf(&Ip_Addr[10], "%d", IEC450_info->CH_INFO[nCH].Transmission_Group);  // IP
            nPort = nPort + IEC450_info->CH_INFO[nCH].Transmission_Group;      // Port

            check = std450_sf_type_set(dev450, nCH, SF_COMM_TYPE_UDP, IEC450_info->CH_INFO[nCH].FuncType);
            if (check == 0)
            {
                DEBUG_LOG("[%s] 450 communication setting fail! --- (type)\r\n", __FUNCTION__);
                return 0;
            }

            check = std450_sf_open(dev450, nCH, IEC450_info->NIC_IP, Ip_Addr, nPort);
            if (check == 0)
            {
                DEBUG_LOG("[%s] 450 communication open fail! --- (IP)\r\n", __FUNCTION__);
                return 0;
            }

            // Set source and destination tocken with options
            std450_sf_set_src(dev450, nCH, IEC450_info->CH_INFO[nCH].SourceID_token, IEC450_info->CH_INFO[nCH].SourceID_num);
            std450_sf_set_dst(dev450, nCH, IEC450_info->CH_INFO[nCH].DestiID_token, IEC450_info->CH_INFO[nCH].DestiID_num);
            std450_sf_set_opt(dev450, nCH, 'd', IEC450_info->CH_INFO[nCH].Device);
            std450_sf_set_opt(dev450, nCH, 'c', IEC450_info->CH_INFO[nCH].Channel);
            std450_sf_run(dev450, nCH);
        }
    }
    else
    {
        DEBUG_LOG("[%s] 450 std450_new_sf setting fail!\r\n", __FUNCTION__);
        return 0;
    }

    DEBUG_LOG("[%s] 450 communication setting success!\r\n", __FUNCTION__);
    return dev450;
}

///@brief IEC 61162-450 device close.
///@param std450_t std450 handle structure
///@todo  close 450 device and free memory buffer
void std450_close(std450_t *dev450)
{
    if(dev450 != NULL)
    {
        for (int nCH = 0; nCH < C_MAX_CH_NUM; nCH++)
        {
            std450_sf_stop(dev450, nCH);
            std450_sf_close(dev450, nCH);
        }

        DEBUG_LOG("[%s] 450 COMMUNICATION Backand close\r\n", __FUNCTION__);
    }
}

///@brief IEC 61162-450 device mem free.
///@param std450_t
///@todo  
void std450_free(std450_t *dev450)
{
    if(dev450 != NULL)
    {
        std450_free_sf(dev450);
        std450_mem_free(dev450);

        DEBUG_LOG("[%s] 450 COMMUNICATION behavior close\r\n", __FUNCTION__);
    }
}

///@brief IEC 61162-450 send data via std450 device
///@param std450_t std450 handle structure
///@param nCh multicast channel number
///@param msg send data buffer
///@param len send data length
int std450_send(std450_t *dev450, int nCh, void *msg, int len)
{
    int rtn = 0;

    rtn = std450_sf_write_msg(dev450, nCh, msg, len);
    if (rtn == 0)
    {
        DEBUG_LOG("[%s] 450 multicast transmission fail!\r\n", __FUNCTION__);
    }

    return rtn;
}

///@brief IEC 61162-450 receive data via std450 device
///@param std450_t std450 handle structure
///@param nCh multicast channel number
///@param msg received data buffer
///@param len recieved data length
int std450_recv(std450_t *dev450, int nCh, void *msg, int len)
{
    if (dev450 == NULL)
    {
        return -1;
    }

    return  std450_sf_read_msg(dev450, nCh, msg, len);
}
