/**
******************************************************************************
* @file      TargetUDP.c
* <AUTHOR>
* @date      2024-6-10
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetEthernet.h"

static udp_ip_port_pcb_s Tbd_UdpConf[UDP_TYPE_MAX];


void Udp_Ip_Client_Config(void const *argument)
{
	IP4_ADDR(&Tbd_UdpConf[UDP_CLIENT_TEST_PC].remote_ip, 10,1,140,56);
	Tbd_UdpConf[UDP_CLIENT_TEST_PC].remote_port = 10000;
}

void Udp_Ip_Server_Config(void const *argument)
{
	struct netif *netif = (struct netif *)argument;

	IP4_ADDR(&Tbd_UdpConf[UDP_SERVER_NAVTEX].local_ip, 	(netif->ip_addr.addr & 0xFF),
														((netif->ip_addr.addr >> 8 ) & 0xFF),
														((netif->ip_addr.addr >> 16 ) & 0xFF),
														((netif->ip_addr.addr >> 24 ) & 0xFF));
	Tbd_UdpConf[UDP_SERVER_NAVTEX].local_port = 10000;
	Tbd_UdpConf[UDP_SERVER_NAVTEX].remote_port = 15000;

	IP4_ADDR(&Tbd_UdpConf[UDP_SERVER_BAM_1].local_ip, 239,192,0,17);
	Tbd_UdpConf[UDP_SERVER_BAM_1].local_port = 60017;
	Tbd_UdpConf[UDP_SERVER_BAM_1].remote_port = 60017;

	IP4_ADDR(&Tbd_UdpConf[UDP_SERVER_BAM_2].local_ip, 239,192,0,18);
	Tbd_UdpConf[UDP_SERVER_BAM_2].local_port = 60018;
	Tbd_UdpConf[UDP_SERVER_BAM_2].remote_port = 60018;

	IP4_ADDR(&Tbd_UdpConf[UDP_SERVER_CAM_1].local_ip, 239,192,0,19);
	Tbd_UdpConf[UDP_SERVER_CAM_1].local_port = 60019;
	Tbd_UdpConf[UDP_SERVER_CAM_1].remote_port = 60019;

	IP4_ADDR(&Tbd_UdpConf[UDP_SERVER_CAM_2].local_ip, 239,192,0,20);
	Tbd_UdpConf[UDP_SERVER_CAM_2].local_port = 60020;
	Tbd_UdpConf[UDP_SERVER_CAM_2].remote_port = 60020;

	igmp_joingroup((ip4_addr_t *)(&Tbd_UdpConf[UDP_SERVER_NAVTEX].local_ip),(ip4_addr_t *)(&Tbd_UdpConf[UDP_SERVER_BAM_1].local_ip));
	igmp_joingroup((ip4_addr_t *)(&Tbd_UdpConf[UDP_SERVER_NAVTEX].local_ip),(ip4_addr_t *)(&Tbd_UdpConf[UDP_SERVER_BAM_2].local_ip));
	igmp_joingroup((ip4_addr_t *)(&Tbd_UdpConf[UDP_SERVER_NAVTEX].local_ip),(ip4_addr_t *)(&Tbd_UdpConf[UDP_SERVER_CAM_1].local_ip));
	igmp_joingroup((ip4_addr_t *)(&Tbd_UdpConf[UDP_SERVER_NAVTEX].local_ip),(ip4_addr_t *)(&Tbd_UdpConf[UDP_SERVER_CAM_2].local_ip));
}

void Udp_Server_Send_Str(udp_ip_port_pcb_t type, char *pMsg)
{
	struct pbuf *p_tx;
	u16_t len = strlen(pMsg);

	p_tx = pbuf_alloc(PBUF_TRANSPORT, len, PBUF_RAM);
	
	if(p_tx != NULL)
	{
		pbuf_take(p_tx, (char*)pMsg, len);

		udp_connect(Tbd_UdpConf[type].pcb, &Tbd_UdpConf[type].local_ip, Tbd_UdpConf[type].remote_port);

		udp_send(Tbd_UdpConf[type].pcb, p_tx);

		udp_disconnect(Tbd_UdpConf[type].pcb);

		pbuf_free(p_tx);
	}
}

void Udp_Server_Send_Data(udp_ip_port_pcb_t type, u8 *pData, u16 len)
{
	struct pbuf *p_tx;

	p_tx = pbuf_alloc(PBUF_TRANSPORT, len, PBUF_RAM);
	
	if(p_tx != NULL)
	{
		pbuf_take(p_tx, (u8 *)pData, len);

		udp_connect(Tbd_UdpConf[type].pcb, &Tbd_UdpConf[type].local_ip, Tbd_UdpConf[type].remote_port);

		udp_send(Tbd_UdpConf[type].pcb, p_tx);

		udp_disconnect(Tbd_UdpConf[type].pcb);

		pbuf_free(p_tx);
	}
}

void Udp_Client_Send_Str(udp_ip_port_pcb_t type, char *pMsg)
{
	struct pbuf *p_tx;
	u16_t len = strlen(pMsg);

	p_tx = pbuf_alloc(PBUF_TRANSPORT, len, PBUF_RAM);
	
	if(p_tx != NULL)
	{
		pbuf_take(p_tx, (char*)pMsg, len);

		udp_send(Tbd_UdpConf[type].pcb, p_tx);

		pbuf_free(p_tx);
	}
}

void Udp_Client_Send_Data(udp_ip_port_pcb_t type, u8 *pData, u16 len)
{
	struct pbuf *p_tx;
	p_tx = pbuf_alloc(PBUF_TRANSPORT, len, PBUF_RAM);
	
	if(p_tx != NULL)
	{
		pbuf_take(p_tx, (u8 *)pData, len);

		udp_send(Tbd_UdpConf[type].pcb, p_tx);

		pbuf_free(p_tx);
	}
}

void Udp_Server_Connect(udp_ip_port_pcb_t type)
{
	err_t err;

	if(Tbd_UdpConf[type].pcb != NULL)
	{
		udp_remove(Tbd_UdpConf[type].pcb);
	}

	/* Create a new UDP control block  */
	Tbd_UdpConf[type].pcb = udp_new();
	if (Tbd_UdpConf[type].pcb != NULL)
	{
		/* Bind the upcb to the UDP_PORT port */
		/* Using IP_ADDR_ANY allow the upcb to be used by any local interface */
		//err = udp_bind(p->pcb, IP4_ADDR_BROADCAST, p->port);
		err = udp_bind(Tbd_UdpConf[type].pcb, &Tbd_UdpConf[type].local_ip, Tbd_UdpConf[type].local_port);

		if(err == ERR_OK)
		{
			/* Set a receive callback for the upcb */
			udp_recv(Tbd_UdpConf[type].pcb, Udp_Recv_Callback, NULL);
			DEBUG_MSG("[%s] Udp Server Connect Success Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																				(Tbd_UdpConf[type].local_ip.addr & 0xFF),
																				((Tbd_UdpConf[type].local_ip.addr >> 8) & 0xFF),
																				((Tbd_UdpConf[type].local_ip.addr >> 16) & 0xFF),
																				((Tbd_UdpConf[type].local_ip.addr >> 24) & 0xFF),
																				Tbd_UdpConf[type].local_port);
		}
		else
		{

            DEBUG_MSG("[%s] Udp Server Connect Error Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																				(Tbd_UdpConf[type].local_ip.addr & 0xFF),
																				((Tbd_UdpConf[type].local_ip.addr >> 8) & 0xFF),
																				((Tbd_UdpConf[type].local_ip.addr >> 16) & 0xFF),
																				((Tbd_UdpConf[type].local_ip.addr >> 24) & 0xFF),
																				Tbd_UdpConf[type].local_port);

			udp_remove(Tbd_UdpConf[type].pcb);
		}
	}
}

void Udp_Server_Disconnect(udp_ip_port_pcb_t type)
{
	if(Tbd_UdpConf[type].pcb == NULL)
	{
		return;
	}

	udp_remove(Tbd_UdpConf[type].pcb);

    DEBUG_MSG("[%s] Udp Remove Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																				(Tbd_UdpConf[type].local_ip.addr & 0xFF),
																				((Tbd_UdpConf[type].local_ip.addr >> 8) & 0xFF),
																				((Tbd_UdpConf[type].local_ip.addr >> 16) & 0xFF),
																				((Tbd_UdpConf[type].local_ip.addr >> 24) & 0xFF),
																				Tbd_UdpConf[type].local_port);
}

void Udp_Client_Connect(udp_ip_port_pcb_t type)
{
	err_t err;
	if(Tbd_UdpConf[type].pcb != NULL)
	{
		udp_remove(Tbd_UdpConf[type].pcb);
	}

	Tbd_UdpConf[type].pcb = udp_new();
	if(Tbd_UdpConf[type].pcb != NULL)
	{
		err = udp_connect(Tbd_UdpConf[type].pcb, &Tbd_UdpConf[type].remote_ip, Tbd_UdpConf[type].remote_port);
		if(err == ERR_OK)
		{
			udp_recv(Tbd_UdpConf[type].pcb, Udp_Recv_Callback, NULL);
			DEBUG_MSG("[%s] Udp Client Connect Success Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																				(Tbd_UdpConf[type].remote_ip.addr & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 8) & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 16) & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 24) & 0xFF),
																				Tbd_UdpConf[type].remote_port);
		}
		else
		{
            DEBUG_MSG("[%s] Udp Client Connect Error Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																				(Tbd_UdpConf[type].remote_ip.addr & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 8) & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 16) & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 24) & 0xFF),
																				Tbd_UdpConf[type].remote_port);
		}
	}
	else
	{
        DEBUG_MSG("[%s] Udp Client Connect Error Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																				(Tbd_UdpConf[type].remote_ip.addr & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 8) & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 16) & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 24) & 0xFF),
																				Tbd_UdpConf[type].remote_port);
	}
}

void Udp_Client_Disconnect(udp_ip_port_pcb_t type)
{
	if(Tbd_UdpConf[type].pcb == NULL)
	{
		return;
	}

	udp_remove(Tbd_UdpConf[type].pcb);
    DEBUG_MSG("[%s] Udp Remove Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																				(Tbd_UdpConf[type].remote_ip.addr & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 8) & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 16) & 0xFF),
																				((Tbd_UdpConf[type].remote_ip.addr >> 24) & 0xFF),
																				Tbd_UdpConf[type].remote_port);
}

void Udp_Recv_Callback(void *arg, struct udp_pcb *upcb, struct pbuf *p, const ip_addr_t *addr, u16_t port)
{
	struct pbuf *p_tx;

	/* allocate pbuf from RAM*/
	p_tx = pbuf_alloc(PBUF_TRANSPORT,	p->len, PBUF_RAM);

	if(p_tx != NULL)
	{
		if(upcb == Tbd_UdpConf[UDP_SERVER_NAVTEX].pcb)
		{
			pbuf_take(p_tx, (char*)p->payload, p->len);
			udp_connect(upcb, addr, port);
			udp_send(upcb, p_tx);
			udp_disconnect(upcb);
			pbuf_free(p_tx);
			pbuf_free(p);
		}
		else if(upcb == Tbd_UdpConf[UDP_SERVER_BAM_1].pcb)
		{
			pbuf_take(p_tx, (char*)p->payload, p->len);
			udp_connect(upcb, addr, port);
			udp_send(upcb, p_tx);
			udp_disconnect(upcb);
			pbuf_free(p_tx);
			pbuf_free(p);
		}
		else if(upcb == Tbd_UdpConf[UDP_SERVER_BAM_2].pcb)
		{
			pbuf_take(p_tx, (char*)p->payload, p->len);
			udp_connect(upcb, addr, port);
			udp_send(upcb, p_tx);
			udp_disconnect(upcb);
			pbuf_free(p_tx);
			pbuf_free(p);
		}
		else if(upcb == Tbd_UdpConf[UDP_SERVER_CAM_1].pcb)
		{
			pbuf_take(p_tx, (char*)p->payload, p->len);
			udp_connect(upcb, addr, port);
			udp_send(upcb, p_tx);
			udp_disconnect(upcb);
			pbuf_free(p_tx);
			pbuf_free(p);
		}
		else if(upcb == Tbd_UdpConf[UDP_SERVER_CAM_2].pcb)
		{
			pbuf_take(p_tx, (char*)p->payload, p->len);
			udp_connect(upcb, addr, port);
			udp_send(upcb, p_tx);
			udp_disconnect(upcb);
			pbuf_free(p_tx);
			pbuf_free(p);
		}
		else if(upcb == Tbd_UdpConf[UDP_CLIENT_TEST_PC].pcb)
		{
			pbuf_take(p_tx, (char*)p->payload, p->len);
			udp_connect(upcb, addr, port);
			udp_send(upcb, p_tx);
			udp_disconnect(upcb);
			pbuf_free(p_tx);
			pbuf_free(p);
		}
	}
}

void test_udp_thread(void const * argument)
{
    for(;;)
    {
        // Udp_Server_Send_Str(UDP_SERVER_NAVTEX, "test Local message send !!!! \r\n");
		// Udp_Server_Send_Str(UDP_SERVER_BAM_1, "test Multicast Bam1 message send !!!! \r\n");
		// Udp_Server_Send_Str(UDP_SERVER_BAM_2, "test Multicast Bam2 message send !!!! \r\n");
		// Udp_Server_Send_Str(UDP_SERVER_CAM_1, "test Multicast Cam1 message send !!!! \r\n");
		// Udp_Server_Send_Str(UDP_SERVER_CAM_2, "test Multicast Cam2 message send !!!! \r\n");
		// Udp_Client_Send_Str(UDP_CLIENT_TEST_PC, "test PC message send !!!! \r\n");
        
        osDelay(1000);
    }
}

void TargetUDP_Init(void const *argument)
{
    struct netif *netif = (struct netif *)argument;
    
    Udp_Ip_Client_Config(netif);
	Udp_Ip_Server_Config(netif);
	Udp_Server_Connect(UDP_SERVER_NAVTEX);
	Udp_Server_Connect(UDP_SERVER_BAM_1);
	Udp_Server_Connect(UDP_SERVER_BAM_2);
	Udp_Server_Connect(UDP_SERVER_CAM_1);
	Udp_Server_Connect(UDP_SERVER_CAM_2);
	Udp_Client_Connect(UDP_CLIENT_TEST_PC);

	osThreadDef(_test_udp_thread, test_udp_thread, osPriorityNormal, 0, configMINIMAL_STACK_SIZE * 5);
  	osThreadCreate (osThread(_test_udp_thread), NULL);
}
