/*
 * protocol.h
 *
 *  Created on: Apr 1, 2025
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#ifndef FWUPDATE_PROTOCOL_H_
#define FWUPDATE_PROTOCOL_H_

#include "stdint.h"

// --- 프레임 구조 관련 상수 ---
#define PROTOCOL_START_BYTE   0x24 // 프레임 시작 바이트

// --- 프레임 필드 인덱스 (2바이트 Length 기준) ---
#define PROTO_IDX_START       0  // 시작 바이트 ($)
#define PROTO_IDX_LEN_L       1  // 길이 Low Byte
#define PROTO_IDX_LEN_H       2  // 길이 High Byte
#define PROTO_IDX_SEQ_L       3  // 시퀀스 번호 Low Byte
#define PROTO_IDX_SEQ_H       4  // 시퀀스 번호 High Byte
#define PROTO_IDX_CMD_L       5  // 명령어 Low Byte
#define PROTO_IDX_CMD_H       6  // 명령어 High Byte
#define PROTO_IDX_SUB_L       7  // 서브 명령어 Low Byte
#define PROTO_IDX_SUB_H       8  // 서브 명령어 High Byte
#define PROTO_IDX_PAYLOAD     9  // 페이로드 시작

// --- 최소/최대 크기 정의 ---
// 최소 프레임 길이 (페이로드 없음): Start(1)+Len(2)+Seq(2)+Cmd(2)+Sub(2)+CRC(2) = 11
#define PROTOCOL_MIN_FRAME_SIZE   11
// 최소 Frame Length 값 (페이로드 없음): Len(2)+Seq(2)+Cmd(2)+Sub(2) = 8
#define PROTOCOL_MIN_LENGTH_VALUE  8
// 최대 페이로드 크기 (예: 주소 4 + 데이터 1024 = 1028)
#define PROTOCOL_MAX_PAYLOAD_SIZE (4 + 1024)
// 최대 프레임 길이 값 (Len+Seq+Cmd+Sub+Payload)
#define PROTOCOL_MAX_LENGTH_VALUE (PROTOCOL_MIN_LENGTH_VALUE + PROTOCOL_MAX_PAYLOAD_SIZE)
// 최대 프레임 크기 (넉넉하게 설정)
// Start(1) + Len(2) + Seq(2) + Cmd(2) + Sub(2) + Payload(Max) + Padding(Max 3) + CRC(2)
#define PROTOCOL_MAX_FRAME_SIZE   (1 + 2 + 2 + 2 + 2 + PROTOCOL_MAX_PAYLOAD_SIZE + 3 + 2)

// --- 파싱 결과 상태 ---
typedef enum
{
	PROTOCOL_OK = 0,           // 파싱 및 CRC 검증 성공
	PROTOCOL_ERROR_INCOMPLETE, // 프레임이 완전하지 않음 (데이터 부족)
	PROTOCOL_ERROR_BAD_START,  // 시작 바이트 불일치
	PROTOCOL_ERROR_BAD_LENGTH, // 길이 필드 값 오류 (너무 작거나 큼)
	PROTOCOL_ERROR_BAD_CRC,    // CRC 검증 실패
	PROTOCOL_ERROR_BUFFER_NULL,    // 버퍼 포인터가 NULL
	PROTOCOL_ERROR_INFO_NULL,  // 결과 저장 구조체 포인터가 NULL
	PROTOCOL_ERROR_BUFFER_TOO_SMALL // 제공된 버퍼가 너무 작음 (ConstructFrame)
} ProtocolStatus_t;

// --- 파싱 결과 저장 구조체 ---
typedef struct
{
	uint8_t L;
	uint8_t H;
} command_data_t;

typedef union
{
	command_data_t byte;
	uint16_t val;
} command_type_t;

typedef struct
{
	uint16_t sequence;             // 시퀀스 번호
	command_type_t command;        // 명령어
	uint16_t sub_command;    // 서브 명령어
	uint8_t *payload;              // 페이로드 시작 위치 (원본 버퍼 내 포인터)
	uint16_t payload_len;          // 페이로드 길이 (패딩 제외 실제 길이)
} ProtocolInfo_t;

// --- 함수 프로토타입 ---

/**
 * @brief 통신 프레임 생성 함수
 * @param frame_buf 프레임을 생성할 버퍼 포인터
 * @param buffer_size 버퍼의 최대 크기
 * @param cmd 명령어 코드
 * @param sub_cmd 서브 명령어 코드
 * @param payload 페이로드 데이터 포인터 (NULL 가능)
 * @param payload_len 페이로드 데이터 길이
 * @param sequence 시퀀스 번호
 * @retval 생성된 프레임의 전체 길이 (바이트 단위), 오류 시 0
 */
uint16_t ConstructFrame(uint8_t *frame_buf, uint16_t buffer_size, uint16_t cmd,
		uint16_t sub_cmd, const uint8_t *payload, uint16_t payload_len,
		uint16_t sequence);

/**
 * @brief 수신된 데이터 버퍼에서 통신 프레임 파싱 및 검증 (2바이트 Length)
 * @param buffer 수신된 데이터 버퍼 포인터
 * @param buffer_len 수신된 데이터 길이
 * @param pInfo 파싱 결과를 저장할 구조체 포인터
 * @retval ProtocolStatus_t 파싱 및 검증 결과 상태
 */
ProtocolStatus_t ParseFrame(const uint8_t *buffer, uint16_t buffer_len,
		ProtocolInfo_t *pInfo);

#endif /* FWUPDATE_PROTOCOL_H_ */
