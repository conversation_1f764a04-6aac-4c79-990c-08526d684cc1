/**
******************************************************************************
* @file      TargetBoard.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef _TARGET_BOARD_H_
#define _TARGET_BOARD_H_

#ifdef __cplusplus
 extern "C" {
#endif

#include "Common.h"

#include "TargetAdc.h"
#include "TargetBoard.h"
#include "TargetConfig.h"
#include "TargetDac.h"
#include "TargetFSK.h"
#include "TargetISR.h"
#include "TargetMac.h"
#include "TargetNorFlash.h"
#include "TargetRTC.h"
#include "TargetUart.h"
#include "TargetErrNo.h"
#include "TargetCan.h"
#include "TargetGpio.h"
#include "TargetSampleFreq.h"
#include "TargetEthernet.h"
#include "TargetG729.h"
#include "g729a.h"

#include "app_ethernet.h"
#include "mt25ql128aba.h"

void TargetBoard_Init(void);

#ifdef __cplusplus
}
#endif

#endif	/* _TARGET_BOARD_H_ */


