///@file     std450_data.h
///@brief    450 internal data struct and behavior header.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#ifndef STD450_DATA_H
#define STD450_DATA_H

#include <time.h>
#include "platform.h"

#if (USE_LINUX == 1)
#include <mqueue.h>
#include <pthread.h>
#else
#include "arch/sys_arch.h"
#endif

#define MAX_QUEUE_BUF (2048) /// Max. queue length.

typedef struct std450_node_s std450_node_t; ///Typedef declaration node
typedef struct std450_list_s std450_list_t; ///Typedef declaration list


/// @brief	Liked list node.
struct std450_node_s {
    void* data;
    struct std450_node_s *next;
};

/// @brief	Mutex Linked list.
struct std450_list_s {
    int count;              ///< Linked list count.
    sys_sem_t   sem;
    sys_mutex_t mutex;
#if (USE_LINUX == 1)
    mqd_t mq;               ///< Messge queue. For, pthread_cond_t cv operation purpose. 
    pthread_mutex_t mutex;	///< Pthread mutex.
    pthread_cond_t cv;	///< Pthread condition. 
#endif
    std450_node_t *front;   ///< Linked list input position.
    std450_node_t *rear;    ///< Linked list output position.
};

extern void std450_list_init(std450_list_t *list);
extern void std450_list_free(std450_list_t* list);
extern void std450_list_reset(std450_list_t* list);
extern int  std450_list_empty(std450_list_t* list);
#if (USE_LINUX == 1)
extern int  std450_list_mq_open(std450_list_t* list, char *name, struct mq_attr *attr);
extern int  std450_list_mq_close(std450_list_t* list, char *name);
extern int  std450_list_mq_setattr(std450_list_t* list, struct mq_attr *attr);
extern int  std450_list_mq_getattr(std450_list_t* list, struct mq_attr *attr);
extern int  std450_list_mq_get_count(std450_list_t* list);
#endif
extern void std450_list_set(std450_list_t* list, void* data);
extern void *std450_list_get(std450_list_t* list);
extern void std450_list_mutex_lock(std450_list_t* list);
extern void std450_list_mutex_unlock(std450_list_t* list);
extern void std450_list_semapore_cond_wait(std450_list_t* list);
extern int  std450_list_semapore_cond_tm_wait(std450_list_t* list, int msec);
extern void std450_list_semapore_cond_signal(std450_list_t* list);
extern unsigned int std450_list_size(std450_list_t* list);

/// @brief circular queue
/// @param rear rear point position.
/// @param front rear point position.
/// @param max  Maximum queue size.
/// @param full Full queue flag.
/// @param data Queue buffer.
struct std450_cqueue_s {
    unsigned short head;
    unsigned short tail;
    unsigned short  max;
    char full;
    char *buf;
};

typedef struct std450_cqueue_s std450_cqueue_t;

extern std450_cqueue_t* std450_cqueue_init(unsigned short cqueue_size);
extern void std450_cqueue_free(std450_cqueue_t* cbuf);
extern void std450_cqueue_reset(std450_cqueue_t* cbuf);
extern unsigned short std450_cqueue_capacity(std450_cqueue_t* cbuf);
extern unsigned short std450_cqueue_size(std450_cqueue_t* cbuf);
extern int std450_cqueue_full(std450_cqueue_t* cbuf);
extern int std450_cqueue_empty(std450_cqueue_t* cbuf);
extern int std450_cqueue_put(std450_cqueue_t* cbuf, char data);
extern int std450_cqueue_get(std450_cqueue_t* cbuf, char *data);
// extern void circular_buf_mutex_lock(struct circular_buf* cbuf);
// extern void circular_buf_mutex_unlock(struct circular_buf* cbuf);
// extern void circular_buf_cond_wait(struct circular_buf* cbuf);
// extern void circular_buf_cond_signal(struct circular_buf* cbuf);

#endif  /* STD450_DATA_H */