﻿/**
 * @file      CommonLib.h
 * <AUTHOR>
 * @brief     공통으로 사용되는 함수 라이브러리
 * @version   0.1
 * @date      2022-08-26
 * 
 * @copyright Copyright (c) 2022
 * 
 */


#if !defined(__CommonLib_H__)
#define      __CommonLib_H__

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "AllConst.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

#if defined(__cplusplus)
extern "C" {
#endif   // __cplusplus

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
typedef  struct {
         uint32_t   dSizeB;
         uint32_t   dDataP;

         float32_t *pCoefH;
         float32_t *pDataB;
         float32_t *pAddrF;
         float32_t *pAddrL;

         float32_t  (*pFuncP)(void *pFIR, float32_t fInData);
       } tBaseFIR;
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
typedef  struct {
         uint32_t   dSizeB;

         float64_t *pCoefA;
         float64_t *pCoefB;
         float64_t *pDataB;

         float32_t (*pFuncP)(void *pIIR, float32_t fInData);
       } tBaseIIR;
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
typedef  enum   {
         BQ_TYPE_LOWPASS  = 0,
         BQ_TYPE_HIGHPASS,
         BQ_TYPE_BANDPASS,
         BQ_TYPE_NOTCH,
       } eBiQdType;
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
typedef  struct {
         float64_t  a0, a1, a2;
         float64_t  b1, b2;
         float64_t  z1, z2;

         float32_t (*pFuncP)(void *pIIR, float32_t fInData);
       } tBiQdIIR;
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
typedef  struct {
         uint32_t   dSizeB;
         uint32_t   dDataP;

         float32_t *pCoefH;
         float32_t *pDataB;                 // Image
         float32_t *pDataR;                 // Real
         float32_t *pAddrF;
         float32_t *pAddrL;

         float32_t  fGainH;
         uint32_t   dSizeH;
         uint32_t   dRealP;

         float32_t  (*pFuncP)(void *pFIR, float32_t fInData, float32_t *pRealV);
       } tHlbtFIR;
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
void     MakeInitBaseFIR(tBaseFIR *pFIR, float32_t *pCoef, uint32_t dSize);
float32_t CalcNewDataFIR(tBaseFIR *pFIR, float32_t fInData);
void     ClearAllBuffFIR(tBaseFIR *pFIR);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void     MakeInitBaseIIR(tBaseIIR *pIIR, float64_t *pCoefA, float64_t *pCoefB, uint32_t dSize);
float32_t CalcNewDataIIR(tBaseIIR *pIIR, float32_t fInData);
void     ClearAllBuffIIR(tBaseIIR *pIIR);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void     MakeInitBiQdIIR(tBiQdIIR *pIIR, eBiQdType dBiQdType, float64_t rCutFreq, float64_t rSmpFreq, float64_t rQfactor, float64_t rPeakGain);
float32_t CalcNewDataBiQd(tBiQdIIR *pIIR, float32_t fInData);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void     MakeInitBaseHBT(tHlbtFIR *pFIR, float32_t *pCoef, uint32_t dSize);
float32_t CalcNewImagHBT(tHlbtFIR *pFIR, float32_t fImagData, float32_t fRealData, float32_t *pRealV);
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

#if defined(__cplusplus)
           }
#endif   // __cplusplus


#endif   // __CommonLib_H__
