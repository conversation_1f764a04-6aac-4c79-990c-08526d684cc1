/**
******************************************************************************
* @file      ct050at40h12r22.h
* <AUTHOR>
* @date      2023-3-31
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __CT050AT40H12R22_H_
#define __CT050AT40H12R22_H_

#ifdef __cplusplus
 extern "C" {
#endif 
  
#define  CT050AT40H12R22_WIDTH    ((uint16_t)800)             /* LCD PIXEL WIDTH            */
#define  CT050AT40H12R22_HEIGHT   ((uint16_t)480)             /* LCD PIXEL HEIGHT           */

/** 
  * @brief  AMPIRE640480 Timing  
  */    
#define  CT050AT40H12R22_HSYNC            ((uint16_t)8)      /* Horizontal synchronization */
#define  CT050AT40H12R22_HBP              ((uint16_t)46)     /* Horizontal back porch      */
#define  CT050AT40H12R22_HFP              ((uint16_t)210)      /* Horizontal front porch     */
#define  CT050AT40H12R22_VSYNC            ((uint16_t)4)       /* Vertical synchronization   */
#define  CT050AT40H12R22_VBP              ((uint16_t)23)      /* Vertical back porch        */
#define  CT050AT40H12R22_VFP              ((uint16_t)22)      /* Vertical front porch       */

#ifdef __cplusplus
}
#endif

#endif /* __CT050AT40H12R22_H_ */

