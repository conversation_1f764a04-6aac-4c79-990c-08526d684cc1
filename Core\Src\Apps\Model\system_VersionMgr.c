/**
 ******************************************************************************
 * @file      system_VersionMgr.c
 * <AUTHOR>
 * @date      2025-02-21
 * @brief
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2025 Intellian Technologies
 * All rights reserved.
 *
 ******************************************************************************
 */

#include "model.h"
#include "TargetBoard.h"

/********************************************************
 * vdc or adc to pcb revision
	rev		vdc			adc
	1		0.3			5957.727273
	2		0.6			11915.45455
	3		0.93		18468.95455
	4		1.26		25022.45455
	5		1.57		31178.77273
	6		1.8			35746.36364
	7		2.12		42101.27273
	8		2.41		47860.40909
********************************************************/			
u32 Adc_to_Pcb_Rev_Tb[ADC_TO_REV_MAX][2] = 
{
	{1, 5958}	,
	{2, 11915}	,
	{3, 18469}	,
	{4, 25022}	,
	{5, 31179}	,
	{6, 35746}	,
	{7, 42101}	,
	{8, 47860}	,
};

/**
  * @brief  
  * @param  
  * @retval 
  */
 u32 Get_Adc_to_Pcb_Rev(u32 adc)
 {
	int diff_1 = 0;
	int diff_2 = 0;
	u32 ret = 0xFFFFFFFF;
	int i = 0;

	if(adc < Adc_to_Pcb_Rev_Tb[0][1])
	{
		return 0;
	}
	else if(adc > Adc_to_Pcb_Rev_Tb[ADC_TO_REV_MAX-1][1])                     
	{
		return Adc_to_Pcb_Rev_Tb[ADC_TO_REV_MAX-1][0];
	}

	for(i=0; i<ADC_TO_REV_MAX-1; i++)
	{
		if((adc >= Adc_to_Pcb_Rev_Tb[i][1]) && (adc < Adc_to_Pcb_Rev_Tb[i+1][1]))
		{
			diff_1 = ABS(adc-Adc_to_Pcb_Rev_Tb[i][1]);
			diff_2 = ABS(adc-Adc_to_Pcb_Rev_Tb[i+1][1]);

			if(diff_1 <= diff_2)
			{
				ret = Adc_to_Pcb_Rev_Tb[i][0];
			}
			else
			{
				ret = Adc_to_Pcb_Rev_Tb[i+1][0];
			}
		}
	}

	return ret;
 }

void system_VersionMgr_Init(void)
{
    g_hSysStatus.m_pStat->ver.Receive_Sw[VER_SW_TYPE_MAJOR] = SYSTEM_SW_VER_MAJOR;
    g_hSysStatus.m_pStat->ver.Receive_Sw[VER_SW_TYPE_MINOR] = SYSTEM_SW_VER_MINOR;
    g_hSysStatus.m_pStat->ver.Receive_Sw[VER_SW_TYPE_REV] = SYSTEM_SW_VER_REV;
    g_hSysStatus.m_pStat->ver.Receive_Sw[VER_SW_TYPE_RC] = SYSTEM_SW_VER_RELEASE_CANDIDATE;
}

void system_VersionMgr_Task(void)
{
    g_hSysStatus.m_pStat->ver.Receive_Hw[VER_HW_TYPE_VER] = Get_Adc_to_Pcb_Rev(gDrvAdc.filtered_Adc_Ver_1);
    g_hSysStatus.m_pStat->ver.Receive_Hw[VER_HW_TYPE_REV] = Get_Adc_to_Pcb_Rev(gDrvAdc.filtered_Adc_Ver_2);
}