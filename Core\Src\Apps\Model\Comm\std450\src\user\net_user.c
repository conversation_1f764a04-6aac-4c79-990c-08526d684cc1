///@file     net_user.c
///@brief    UDP(User Datagram Protocol) user application function code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#include <stdio.h>
#include <string.h>
#include <errno.h>
#include "user/net_user.h"

#if (USE_LINUX == 1)
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/epoll.h>
#endif

/// @brief      UDP socket initialize.
/// @param      targ Ethernet socket inforamtion and file description.
/// @param      type TCP/UDP socket type define.
/// @param      sock_opt None(0), Multicast(1), Broadcast(2)
/// @return	    Success(0), Fail(-1)
int init_sock(net_targ_t *targ, int type, int sock_opt)
{
    int rtn = -1;
    char port[6] = "";
    struct addrinfo *ai = &(targ->ai);
    memset(ai, 0x00, sizeof(struct addrinfo));

    free_sock(targ);
    ai->ai_family = AF_INET;          //IPv4/IPv6
    ai->ai_socktype = type ? SOCK_DGRAM : SOCK_STREAM;  //TCP/UDP
    ai->ai_protocol = 0;
    //ai->ai_flags = trx ? AI_PASSIVE : 0;
    ai->ai_flags |= AI_NUMERICHOST | AI_NUMERICSERV;
    ai->ai_flags |= AI_ADDRCONFIG;
    sprintf(port, "%d", targ->port);

    if((rtn = getaddrinfo(targ->ip, port, ai, &targ->ai_ret)) != 0)
    {
        struct addrinfo *rp = targ->ai_ret;
        DEBUG_LOG_NET("getaddrinfo error: [IP:%s][Port:%s][rtn:%d]\n", targ->ip, port, rtn);
        DEBUG_LOG_NET("rp->ai_family : %d, rp->ai_socktype : %d, rp->ai_protocol : %d \n", rp->ai_family, rp->ai_socktype, rp->ai_protocol);
    } 
    else 
    {
        struct addrinfo *rp = targ->ai_ret;

        if((targ->fd = socket( rp->ai_family, rp->ai_socktype, rp->ai_protocol)) == -1) 
        {
            DEBUG_LOG_NET("init_sock open error.\n");
            return -1;
        } 
        else 
        {
            // '24.11.11 bugfix. send and receive timeout set
            int timeout_send = 1;
            setsockopt(targ->fd, SOL_SOCKET, SO_SNDTIMEO, (int *)&timeout_send, sizeof(timeout_send));

            int timeout_recv = 1;
            setsockopt(targ->fd, SOL_SOCKET, SO_RCVTIMEO, (int *)&timeout_recv, sizeof(timeout_recv));

            if(type == SOCK_TYPE_UDP) 
            {
                if(sock_opt)
                {
                    if(sock_opt == SOCK_OPT_BCAST)        //Broadcast
                    { 
                        //DEBUG_LOG_NET("init_sock():Broadcast Enable\n");
                        int bcast = 1; 
                        setsockopt(targ->fd, SOL_SOCKET, SO_BROADCAST, 
                                   (int *)&bcast, sizeof(bcast));	
                    }
                    else if(sock_opt == SOCK_OPT_MCAST)   //Multicast 
                    { 
                        //DEBUG_LOG_NET("init_sock():Multicast Enable\n");
                        char reuse = 1;
                        setsockopt(targ->fd, SOL_SOCKET, SO_REUSEADDR, 
                                  (char *)&reuse, sizeof(reuse));
                    }
                }

                if(bind(targ->fd, rp->ai_addr, rp->ai_addrlen) != 0)
                {
                    DEBUG_LOG_NET("init_sock error. bind():[IP:%s][Port:%s][Socket ID:%d]\r\n", targ->ip, port, targ->fd);
                    return -1;	
                } 
                else 
                {
                    DEBUG_LOG_NET("init_sock ok. bind():[IP:%s][Port:%s][Socket ID:%d]\r\n", targ->ip, port, targ->fd);
                }
            }
        }
    }

// #if (USE_RTOS_LWIP == 1)
//     if (targ->ai_ret)
//     {
//         lwip_freeaddrinfo(targ->ai_ret);
//     }
// #endif

	return 0;
}

/// @brief      Socket free.
/// @param      targ Ethernet socket inforamtion and file description.
void free_sock(net_targ_t *targ)
{
    if(targ->fd >= 0)
    {
    	freeaddrinfo(targ->ai_ret);
		targ->ai_ret = NULL;

        close(targ->fd);
        targ->fd = -1;
    }
}

/// @brief      Multicast socket set.
/// @param      targ Ethernet socket inforamtion and file description.
/// @param      nic_ip Multicast set NIC ip.
/// @param      ttl_lvl Multicast ttl
/// @return     Success(0), Fail(-1)
int set_mcast_sock(net_targ_t* targ, char* nic_ip, int ttl)
{
    int rtn = -1;
    struct ip_mreq group;
    struct in_addr local;

    group.imr_multiaddr.s_addr = inet_addr(targ->ip);
    group.imr_interface.s_addr = htonl(INADDR_ANY);     //inet_addr(nic_ip)

    if((rtn = setsockopt(targ->fd, IPPROTO_IP, IP_ADD_MEMBERSHIP,(char *)&group, sizeof(group))) < 0)
    {
        DEBUG_LOG_NET("setsockopt() error. IP_ADD_MEMBERSHIP\r\n");
        rtn = -1;
    } 
    else 
    {
        rtn = 0;
    }

    local.s_addr = inet_addr(nic_ip);
    if((rtn = setsockopt(targ->fd, IPPROTO_IP, IP_MULTICAST_IF,(char *)&local, sizeof(local))) < 0)
    {
        DEBUG_LOG_NET("setsockopt() error. IP_MULTICAST_IF\r\n");
        rtn = -1;
    } 
    else 
    {
        rtn = 0;
    }

    if((rtn = setsockopt(targ->fd, IPPROTO_IP, IP_MULTICAST_TTL, (void *)&ttl, sizeof(ttl))) < 0)
    {
        DEBUG_LOG_NET("setsockopt() error. IP_MULTICAST_IF\r\n");
        rtn = -1;
    } 
    else 
    {
        ;
    }

    return rtn;
}


/// @brief      Write(Tx) socket.
/// @param      targ UDP socket inforamtion and file description.
/// @param      buf Send data buffer.
/// @param      len Send data size.
/// @return     Send buffer size. Fail(<0)
ssize_t write_sock(net_targ_t *targ, char* buf, int len)
{
    int sockopt;
    ssize_t size = -1;
    struct sockaddr_in tx_addr;
    socklen_t len_sockopt = sizeof(sockopt);

    memset( &tx_addr, 0, sizeof(struct sockaddr_in));
    tx_addr.sin_family = AF_INET;
    tx_addr.sin_port = htons(targ->port);
    tx_addr.sin_addr.s_addr = inet_addr(targ->ip);

    if (getsockopt(targ->fd, SOL_SOCKET, SO_ERROR, &sockopt, &len_sockopt) == -1) 
    {
        DEBUG_LOG_NET("write_sock:getsockopt\n");
    }

    if (sockopt) 
    {
        DEBUG_LOG_NET("SO_ERROR: %s(%d)\n", strerror(sockopt), sockopt);
        size = -1;
    } 
    else 
    {
        size = sendto (targ->fd, buf, len, 0, ( struct sockaddr*)&tx_addr, sizeof(struct sockaddr_in));
        // DEBUG_LOG_NET("write_sock\n");
        // struct sockaddr_in *host = (struct sockaddr_in *)&tx_addr;	
        // DEBUG_LOG_NET("IP[%s] PORT[%d] TX[%ld]: ",
        //          inet_ntoa(host->sin_addr), ntohs(host->sin_port), size);
    }

    if(size == -1)
    {
        DEBUG_LOG_NET("Error sendto \r\n");
        if (errno != EAGAIN) 
        {
            size = -2;	//Other Error.
        } 
        else 
        {
            size = -1;
        }
    }

    if(targ->d)
    {
        if(size > 0) 
        {
            DEBUG_LOG_NET("write_sock[%ld]%s\n", (long) size, buf);
        } 
        else 
        {
            DEBUG_LOG_NET("[ERR]write_sock(req:%d](rep:%ld)\n",len, (long) size);
        }
    }

    return size;
}

/// @brief      rx udp data
/// @return     Read buffer size. Fail(<0)
ssize_t read_sock(net_targ_t *targ, char* buf, int len, int tout)
{
#if (USE_LINUX == 1)
    ssize_t size = -1;
    int epfd, ret_poll = -1;
    struct addrinfo *ai_ret = targ->ai_ret;
    struct epoll_event ev, events;

    memset(&events, 0x00, sizeof(struct epoll_event));

    if ((epfd = epoll_create(1)) == -1) {
        ;//DEBUG_LOG_NET("rx_sock_tcp:epoll_create error\n");
    } else {
        ev.events = EPOLLET|EPOLLIN;
        ev.data.fd = targ->fd;

        if (epoll_ctl(epfd, EPOLL_CTL_ADD, targ->fd, &ev) == -1) {
            //DEBUG_LOG_NET("rx_sock:epoll_ctl[%d] error\n",targ->fd);	
        } else {
            if ((ret_poll = epoll_wait(epfd, &events, 1, tout)) < 0) {
                //DEBUG_LOG_NET("rx_sock_udp:epoll_wait error\n");
            }  else if(ret_poll == 0) {
                //DEBUG_LOG_NET("rx_sock_udp:epoll_wait time out\n");
            } else {
                size = recvfrom(targ->fd, buf, len, 0, ai_ret->ai_addr, &ai_ret->ai_addrlen);
                if(size>0) {
                    // DEBUG_LOG_NET("read_sock\n");
                    // struct sockaddr_in *host = (struct sockaddr_in *)ai_ret->ai_addr;	
                    // DEBUG_LOG_NET("IP[%s] PORT[%d] RX: ",
                    //         inet_ntoa(host->sin_addr), ntohs(host->sin_port));
                } else {
                    if (errno != EAGAIN) {
                            size =-1;   //Other Error.
                    } else {
                            size =0;    //Full Tx Packet, Time Wait.
                    }
                }
            }
        }
    }

    if(epfd>0){
        close(epfd);
    }
#else
    ssize_t size = -1;
    struct addrinfo *ai_ret = targ->ai_ret;
    // struct sockaddr_in addr;
    // socklen_t addr_len = sizeof(addr);
    // if(getsockname(targ->fd, (struct sockaddr_in *)&addr, &addr_len) == 0)
    // {
    //     DEBUG_LOG_NET_USER("Local IP: %s\r\n", inet_ntoa(addr.sin_addr));
    //     DEBUG_LOG_NET_USER("Local Port: %d\r\n", ntohs(addr.sin_port));
    // }
    // else
    // {
    //     DEBUG_LOG_NET_USER("Failed to get local socket information\r\n");
    // }

    // if(getpeername(targ->fd, (struct sockaddr_in *)&addr, &addr_len))
    // {
    //     DEBUG_LOG_NET_USER("Remote IP: %s\r\n", inet_ntoa(addr.sin_addr));
    //     DEBUG_LOG_NET_USER("Remote Port: %d\r\n", ntohs(addr.sin_port));
    // }
    // else
    // {
    //     DEBUG_LOG_NET_USER("Failed to get remote socket information\r\n");
    // }

    if(targ->fd > 0)
    {
    	size = recvfrom(targ->fd, buf, len, 0, ai_ret->ai_addr, &ai_ret->ai_addrlen);
    }
#endif

    // if(size > 0)
    // {
    //     DEBUG_LOG_NET("Data Received : ");
    //     for(int i=0; i<len; i++)
    //     {
    //         printf("%c", buf[i]);
    //     }
    // }
    return size;
}
