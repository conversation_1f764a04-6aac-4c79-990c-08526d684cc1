/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32h7xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define DO_ETH_RESET_Pin GPIO_PIN_13
#define DO_ETH_RESET_GPIO_Port GPIOC
#define AI_PCB_VER_2_Pin GPIO_PIN_10
#define AI_PCB_VER_2_GPIO_Port GPIOF
#define AI_PCB_VER_1_Pin GPIO_PIN_3
#define AI_PCB_VER_1_GPIO_Port GPIOC
#define DO_FSK_CHECK_INT_Pin GPIO_PIN_0
#define DO_FSK_CHECK_INT_GPIO_Port GPIOA
#define AO_FSK_OUT_Pin GPIO_PIN_4
#define AO_FSK_OUT_GPIO_Port GPIOA
#define AO_FREQ_INT_Pin GPIO_PIN_5
#define AO_FREQ_INT_GPIO_Port GPIOA
#define AI_ANT_POWER_Pin GPIO_PIN_6
#define AI_ANT_POWER_GPIO_Port GPIOA
#define AI_FSK_LOC_Pin GPIO_PIN_0
#define AI_FSK_LOC_GPIO_Port GPIOB
#define AI_FSK_INT_Pin GPIO_PIN_1
#define AI_FSK_INT_GPIO_Port GPIOB
#define DO_ANT_POW_ON_Pin GPIO_PIN_10
#define DO_ANT_POW_ON_GPIO_Port GPIOH
#define DI_ETH_nINT_Pin GPIO_PIN_12
#define DI_ETH_nINT_GPIO_Port GPIOH
#define TP_PG7_Pin GPIO_PIN_7
#define TP_PG7_GPIO_Port GPIOG
#define TP_PC8_Pin GPIO_PIN_8
#define TP_PC8_GPIO_Port GPIOC
#define DO_RF_TEST_ENABLE_Pin GPIO_PIN_10
#define DO_RF_TEST_ENABLE_GPIO_Port GPIOA
#define DO_FSK_CHECK_LOC_Pin GPIO_PIN_0
#define DO_FSK_CHECK_LOC_GPIO_Port GPIOI
#define DO_Selector_4M_Pin GPIO_PIN_1
#define DO_Selector_4M_GPIO_Port GPIOI
#define DO_Alarm_relay_Pin GPIO_PIN_2
#define DO_Alarm_relay_GPIO_Port GPIOI
#define TP_PA15_Pin GPIO_PIN_15
#define TP_PA15_GPIO_Port GPIOA
#define TP_PD2_Pin GPIO_PIN_2
#define TP_PD2_GPIO_Port GPIOD
#define TP_PD3_Pin GPIO_PIN_3
#define TP_PD3_GPIO_Port GPIOD
#define TP_LED_1_Pin GPIO_PIN_4
#define TP_LED_1_GPIO_Port GPIOD
#define TP_LED_2_Pin GPIO_PIN_7
#define TP_LED_2_GPIO_Port GPIOD
#define TP_LED_3_Pin GPIO_PIN_12
#define TP_LED_3_GPIO_Port GPIOJ
#define TP_LED_4_Pin GPIO_PIN_13
#define TP_LED_4_GPIO_Port GPIOJ
#define TP_LED_5_Pin GPIO_PIN_14
#define TP_LED_5_GPIO_Port GPIOJ
#define TP_LED_6_Pin GPIO_PIN_15
#define TP_LED_6_GPIO_Port GPIOJ
#define TP_LED_7_Pin GPIO_PIN_9
#define TP_LED_7_GPIO_Port GPIOG
#define DO_4MOSC_SEL_Pin GPIO_PIN_10
#define DO_4MOSC_SEL_GPIO_Port GPIOG
#define TP_LED_8_Pin GPIO_PIN_3
#define TP_LED_8_GPIO_Port GPIOK

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
