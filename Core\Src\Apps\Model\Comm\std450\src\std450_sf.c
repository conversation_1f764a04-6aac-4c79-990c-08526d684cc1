///@file     std450_sf.c
///@brief    IEC 61162-450 System Function block behavior code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#include <stdio.h>
#include <stdlib.h> 
#include <string.h>
#include <sys/stat.h>
#include <stdarg.h>
#include <stddef.h> 
#include <errno.h>

#include <user/nic_user.h>
#include <std450_sf.h>
#include <std450_private.h>
#include <std450_sf_private.h>

#if (USE_RTOS_LWIP == 1)
#include "lwip/sockets.h"
#include "lwipopts.h"
#endif

///@brief IEC 61162-450 device SF file descriptor open.
///@param sf  System function dev.
///@param args SF's udp open argument( nic_ip, addr_ip, addr_port)
///@return Success(1), Fail(0)
STD450_API int _std450_udp_open(sf_t* sf, void* args) 
{
    int rtn = 0;
    std450_sf_udp_t *udp;
    va_list *p_args = (va_list *)args;    
    char *nic_ip = va_arg(*p_args, char*);
    char *ip = va_arg(*p_args, char*);
    int port = va_arg(*p_args, int); 

    assert(sf);

    sf->backend_data = (std450_sf_udp_t*)MEM_MALLOC(sizeof(std450_sf_udp_t));
    DEBUG_LOG_SF("sf->backend_data:Allocated memory address: %p\r\n", sf->backend_data);
    memset(sf->backend_data, 0x00, sizeof(std450_sf_udp_t));

    udp = sf->backend_data;
    memcpy(udp->nic_ip, nic_ip, strlen(nic_ip)); 
    memcpy(&udp->targ.ip, ip, strlen(ip));
    udp->targ.port = port;

    if(init_sock(&udp->targ, SOCK_TYPE_UDP, SOCK_OPT_MCAST) == 0)
    {
        if(set_mcast_sock(&udp->targ, udp->nic_ip, 1) == 0)
        {
            sf->fd = udp->targ.fd;
            rtn = 1;
        }
        else
        {
            DEBUG_LOG_SF("ERR:_std450_udp_open:set_mcast_sock()\n");
        }
    }
    else
    {
        DEBUG_LOG_SF("ERR:_std450_udp_open:init_sock()\n");
    }

    return rtn;
}

///@brief IEC 61162-450 device SF UDP file descriptor close.
///@param sf  System function dev.
///@return Success(1), Fail(0)
STD450_API int _std450_udp_close(sf_t* sf)
{
    std450_sf_udp_t *udp = sf->backend_data;
    assert(sf);

    if(udp != NULL)
    {
        free_sock(&udp->targ);

        MEM_FREE(udp);
        udp = NULL;
    }

    return 1;
}

///@brief IEC 61162-450 device SF UDP message read.
///@param sf  System function dev.
///@param msg    read message.
///@param len    read message length.
///@details Read function time-out is 5msec epoll.
STD450_API int _std450_udp_read(sf_t* sf, char* buf, int len)
{
    std450_sf_udp_t *udp;
    udp = sf->backend_data;

    if(udp == NULL)
    {
        return -1;
    }

    return read_sock(&udp->targ, buf, len, 5);
}

///@brief IEC 61162-450 device SF UDP message send.
///@param sf  System function dev.
///@param msg    send message.
///@param len    send message length.
STD450_API int _std450_udp_write(sf_t* sf, char* buf, int len)
{
    std450_sf_udp_t *udp;
    udp = sf->backend_data;
    return write_sock(&udp->targ, buf, len);
}

///@brief IEC 61162-450 device SF backend UDP behavior.
const std450_backend_t _std450_udp_backend = {
    _std450_udp_open,
    _std450_udp_close,
    _std450_udp_read,
    _std450_udp_write
};
//==============================================================================

///@brief IEC 61162-450 device SF file descriptor uart open.
///@param sf  System function dev.
///@param args SF's uart open argument
///             [device name, data bit, stop bits, parity bits, bps]
///             [mode 485, use rtu control, feedback flag]
STD450_API int _std450_uart_open(sf_t* sf, void* args) 
{
    return 0;
}

///@brief IEC 61162-450 device SF UART file descriptor close.
///@param sf  System function dev.
///@param args SF's uart open argument( nic_ip, ip, port)
STD450_API int _std450_uart_close(sf_t* sf)
{
    return 0;
}

///@brief IEC 61162-450 device SF UART message read.
///@param sf  System function dev.
///@param msg    read message.
///@param len    read message length.
STD450_API int _std450_uart_read(sf_t* sf, char* buf, int len)
{
    return 0;  
}

///@brief IEC 61162-450 device SF UART message send.
///@param sf  System function dev.
///@param msg    send message.
///@param len    send message length.
STD450_API int _std450_uart_write(sf_t* sf, char* buf, int len)
{
    return 0;
}

///@brief IEC 61162-450 device SF backend UART behavior.
const std450_backend_t _std450_uart_backend = {
    _std450_uart_open,
    _std450_uart_close,
    _std450_uart_read,
    _std450_uart_write
};

//==============================================================================

///@brief IEC 61162-450 device SF file descriptor CAN open.
///@todo Function write.
STD450_API int _std450_can_open(sf_t* sf, void* args) 
{
    return 0;
}

///@brief IEC 61162-450 device SF file descriptor CAN close.
///@todo Function write.
STD450_API int _std450_can_close(sf_t* sf)
{
    return 0;
}

///@brief IEC 61162-450 device SF file descriptor CAN port read.
///@todo Function write.
STD450_API int _std450_can_read(sf_t* sf, char* buf, int len)
{
    return 0;
}

///@brief IEC 61162-450 device SF file descriptor CAN port write.
///@todo Function write.
STD450_API int _std450_can_write(sf_t* sf, char* buf, int len)
{
    return 0;
}

///@brief IEC 61162-450 device SF backend CAN behavior.
const std450_backend_t _std450_can_backend = {
    _std450_can_open,
    _std450_can_close,
    _std450_can_read,
    _std450_can_write
};

//==============================================================================
///@brief IEC 61162-450 device SF file descriptor TCP open.
///@todo Function write.
STD450_API int _std450_tcp_open(sf_t* sf, void* args) 
{
    return 0;
}

///@brief IEC 61162-450 device SF file descriptor TCP close.
///@todo Function write.
STD450_API int _std450_tcp_close(sf_t* sf)
{
    return 0;
}

///@brief IEC 61162-450 device SF file descriptor TCP port read.
///@todo Function write.
STD450_API int _std450_tcp_read(sf_t* sf, char* buf, int len)
{
    return 0;
}

///@brief IEC 61162-450 device SF file descriptor TCP port write.
///@todo Function write.
STD450_API int _std450_tcp_write(sf_t* sf, char* buf, int len)
{
    return 0;
}

///@brief IEC 61162-450 device SF backend TCP behavior.
const std450_backend_t _std450_tcp_backend = {
    _std450_tcp_open,
    _std450_tcp_close,
    _std450_tcp_read,
    _std450_tcp_write
};

//==============================================================================
///@brief check vilad IEC 61162-450 datagram header.
///@param type datagram type
///@param data compare datagram header data buffer.
///@return Ture(1) False(0)
int check_datagram_header(int type, char* data)
{
    int rtn = 0;

    switch (type)
    {
        case SF_FUNC_TYPE_UDPBC:
            {if(memcmp(data, C_TOKEN_UDPBC, C_LEN_450_TOKEN) == 0) rtn = 1; break;}
        case SF_FUNC_TYPE_RAUDP:
            {if(memcmp(data, C_TOKEN_RAUDP, C_LEN_450_TOKEN) == 0) rtn = 1; break;}
        case SF_FUNC_TYPE_RRUDP:
            {if(memcmp(data, C_TOKEN_RRUDP, C_LEN_450_TOKEN) == 0) rtn = 1; break;}
        case SF_FUNC_TYPE_NKPGN:
            {if(memcmp(data, C_TOKEN_NKPGN, C_LEN_450_TOKEN) == 0) rtn = 1; break;}
        case SF_FUNC_TYPE_RRTCP:
            {if(memcmp(data, C_TOKEN_RRTCP, C_LEN_450_TOKEN) == 0) rtn = 1; break;}
        default: break;
    }

    return rtn;
}

//==============================================================================
///@brief UdPbC linked list data struct.
///@param size Linked list data size.
///@param data Linked list data
///@details UdPbc tx tread and rx thread working data. \n
///         At tx thread data is send data size and send data. \n
///         At return tx thread is sended data size. \n
///         At rx thread data is receving data size. \n
///         At return rx thread is used received data size and received data. \n
struct udpbc_list_data_s {
    ssize_t size;
    char    data[C_MAX_450_BUF];
};

typedef struct udpbc_list_data_s udpbc_list_data_t;

///@brief Data push at udpbc list.
///@param list List.
///@param src Push data address.
void udpbc_list_push(std450_list_t *list, udpbc_list_data_t *src)
{
    assert(list);
    assert(src);

    std450_list_set(list, (void*) src);
    std450_list_semapore_cond_signal(list);
}

///@brief Data push at udpbc list.
///@param list List.
///@param src Push data address.
void *udpbc_list_pop(std450_list_t *list)
{
    assert(list);
return std450_list_get(list);
}

//==============================================================================
//======================================
// udpbc private function.
//======================================

///@brief get_checksum
///@param data checksum data
///@param len checksum data length.
///@return checksum value.
char get_checksum(char* data, unsigned int len)
{
    unsigned int  idx = 0;
    unsigned char sum = 0;    
    sum = 0xFF;

    for (idx = 0; idx < len; idx++) {
        sum ^= data[idx];
    }

    return (~sum);
}

///@brief Passing 
///@param sf  allocated 450 device for SF.
///@param len IEC 61162-450 message length.
///@param msg IEC 61162-1 message. ($--*, exclusive checkk sum and CR,LF)
///@param datablock Make 61162-450 datablock message.
///@return IEC 61162-1 Sentence length.
///@todo get data change struct (now just nmea data-> todo get option data)
ssize_t udpbc_parsing_packet(sf_t* sf, char* msg, char* datablock, int len)
{
    ssize_t size = 0;
    char tmp = 0, *tmp_data=0;
    int idx = 0, flag_not_use = 0;
    int flag_get_opt = 0, flag_a_opt = 0;
    int opt_begin_pos = 0, opt_end_pos = 0, opt_len = 0;
    int nmea_begin_pos = 0, nmea_end_pos = 0;
    int g_opt_begin = 0, g_opt_end = 0;
    int s_opt_begin = 0, s_opt_end = 0;
    int d_opt_begin = 0, d_opt_end = 0, flag_d_opt = 1, d_len = 0, d_num = 0;
    int n_opt_begin = 0, n_opt_end = 0;
    int t_opt_begin = 0, t_opt_end = 0;
    int a_opt_begin = 0, a_opt_end = 0;
    char cksum = 0, opt_cksum = 0, nmea_cksum = 0;
    int opt_cksum_pos = 0, a_opt_cksum_pos = 0;
    char rcv_opt[10] = "";
    int rcv_opt_idx = 0, flag_rcv_opt = 0;

    if(len < 0)
    {
        return 0;
    }

    for(idx = C_MAX_SFI_LEN; idx < len; idx++)
    {
        tmp = datablock[idx];

        if((tmp == '\\') && (opt_begin_pos == 0))
        {
            opt_begin_pos = idx;
            continue;
        }

        if(opt_begin_pos)
        {
            if(tmp == 'a')
            {
                if(idx - 2 > opt_begin_pos)
                {
                    flag_a_opt = 1;
                }
            }

            if(tmp == '*')
            {
                if((flag_a_opt) && (a_opt_cksum_pos == 0))
                {
                    a_opt_cksum_pos = idx;                
                }
                else
                {
                    opt_cksum_pos = idx;
                }
            }

            if(opt_cksum_pos)
            {
                if((tmp == '$') || (tmp == '!') || (tmp == '#'))
                {
                    opt_end_pos = idx - 1;
                    if(opt_end_pos - 4 < 0)
                    {
                        break;
                    }

                    nmea_begin_pos = idx;
                    break;
                }
            }
        }
    }

    // get opt infor
    opt_len = opt_end_pos - opt_begin_pos + 1; 
    //length error.
    if(opt_len <= MIN_UDPBC_LEN)
    { 
        return size;
    }

    if(opt_cksum_pos)
    {
        sscanf(&datablock[opt_cksum_pos+1], "%2X", (int*)&cksum);
        opt_cksum = get_checksum(&datablock[opt_begin_pos+1], 
                                opt_cksum_pos-opt_begin_pos-1);
        //check sum eeror. 
        if(cksum != opt_cksum)
        { 
            return 0;
        }
        else
        {
            flag_get_opt = 1;
        }
    }
    else
    {
        return 0;
    }

    if(flag_get_opt)
    {
        for(idx = opt_begin_pos; idx <= opt_end_pos - 4; idx++)
        {
            tmp = datablock[idx];
            switch(tmp)
            {
                case 'g':
                    if(datablock[idx+1] == ':')
                    {
                        rcv_opt[rcv_opt_idx++] = tmp;
                        g_opt_begin = idx;
                        flag_rcv_opt = 1;
                    }
                    break;
                case 's':
                    if(datablock[idx+1] == ':')
                    {
                        rcv_opt[rcv_opt_idx++] = tmp;
                        s_opt_begin = idx;
                        flag_rcv_opt = 1;
                    }
                    break;
                case 'd':
                    if(datablock[idx+1] == ':')
                    {
                        if(d_opt_begin == 0)
                        {
                            rcv_opt[rcv_opt_idx++] = tmp;
                            d_opt_begin = idx;
                        }
                        flag_rcv_opt = 1;
                    }
                    break;
                case 'n':
                    if(datablock[idx+1] == ':')
                    {
                        rcv_opt[rcv_opt_idx++] = tmp;
                        n_opt_begin = idx;
                        flag_rcv_opt = 1;
                    }
                    break;
                case 't':
                    if(datablock[idx+1] == ':')
                    {
                        rcv_opt[rcv_opt_idx++] = tmp;
                        t_opt_begin = idx;
                        flag_rcv_opt = 1;
                    }
                    break;
                case 'a':
                    if(datablock[idx+1] == ':')
                    {
                        rcv_opt[rcv_opt_idx++] = tmp;
                        a_opt_begin = idx;
                        flag_rcv_opt = 1;
                    }
                    break;
            }

            if(flag_rcv_opt)
            {
                if((tmp == ',') || (tmp == '\\'))
                {
                    switch(rcv_opt[rcv_opt_idx-1])
                    {
                        case 'g': g_opt_end = idx; break;
                        case 's': s_opt_end = idx; break;
                        case 'd': d_opt_end = idx; break;
                        case 'n': n_opt_end = idx; break;
                        case 't': t_opt_end = idx; break;
                        case 'a': a_opt_end = idx; break;
                    }
                    flag_rcv_opt=0;
                }
            }
        }

        for(idx = 0; idx < 10; idx++)
        {
            if(rcv_opt[idx] == 0x00)
            {
                continue;
            }

            if(rcv_opt[idx] == 'g') // Grouping control
            {
                ;//get g
            }
            else  if(rcv_opt[idx] == 's') // Source Inentification
            {
                //("Receive Source SFI not exist.\n");
                if((s_opt_end - s_opt_begin) - 2 < C_MAX_SFI_LEN)
                { 
                    flag_not_use = 1;
                    break;
                }
                else
                {
                    //("Receive Source SFI Same.\n");
                    if(memcmp(&datablock[s_opt_begin + 2], (char*)&sf->src, C_MAX_SFI_LEN) == 0)
                    {
                        flag_not_use = 1;
                        break;
                    }
                }
            }
            else  if(rcv_opt[idx] == 'd') // Destination Identification
            {
                d_len = d_opt_end-d_opt_begin + 1;
                if(d_len-2 < C_MAX_SFI_LEN)
                {
                    break;  
                }
                else
                {
                    tmp_data = &datablock[d_opt_begin];
                    flag_d_opt = 0;
                    do
                    { 
                        //("Receive Destination SFI exsit.\n");  
                        if(memcmp(&tmp_data[d_num * 9] + 2, (char*)&sf->src,  C_MAX_SFI_LEN) == 0)
                        {
                           flag_d_opt = 1;
                           break;
                        }
                        
                        d_num++;
                    } while(d_len > d_num * 9);

                    if(flag_d_opt == 0)
                    {
                        // ("Receive Destination SFI not exsit.\n");
                        flag_not_use = 1;
                        break;
                    }
                }
            }
            else  if(rcv_opt[idx] == 'n') // Line-count parameter
            {
                ;
            } 
            else  if(rcv_opt[idx] == 't') // Text string
            {
                ;
            }
            else  if(rcv_opt[idx] == 'a')
            {
                ;
            }
            else
            {
                ;
            }
        }
    }

    if(flag_not_use)
    {
        return size;
    }

    if(nmea_begin_pos)
    {
        for(idx = nmea_begin_pos; idx < len; idx++)
        {
            tmp = datablock[idx];
            if((tmp == 0x0a) && (datablock[idx-1] == 0x0d) && (datablock[idx-4] == '*'))
            {
                nmea_end_pos = idx;
                sscanf(&datablock[nmea_end_pos-3], "%2X", (int*)&cksum);
                nmea_cksum = get_checksum(&datablock[nmea_begin_pos + 1], (nmea_end_pos-nmea_begin_pos) - 5);

                if(cksum == nmea_cksum)
                {
                    //size = (nmea_end_pos-nmea_begin_pos) - 3;
                    //size = (nmea_end_pos-nmea_begin_pos) - 1;
                	size = (nmea_end_pos-nmea_begin_pos) + 1;
                    tmp_data = &datablock[nmea_begin_pos];
                    memcpy(msg, tmp_data, size);
                } 
                else 
                {
                    size = 0;
                }
            }
        }
    }

    return size;
}

///@brief UdPbC make datablock pakcet from message.
///@param sf  allocated 450 device for SF.
///@param datablock Make 61162-450 datablock copy destinamtion address.
///@param msg IEC 61162-1 message.
///@param len IEC 61162-1 message length.
///@todo  group option define. a option length calc.
ssize_t udpbc_make_packet(sf_t* sf, void* datablock, char* msg, int len)
{
    int idx = 0, pos = 0, step = 0;
    ssize_t size = 0;
    std450_sf_udpbc_t* sf_info = sf->behavior_data;
    UdPbC_data_t* packet450 = (UdPbC_data_t*)datablock;
    UdPbC_header_t* header = &(packet450->header);
    char *data = packet450->data;
    memcpy(header->header, C_TOKEN_UDPBC, C_LEN_450_TOKEN); 
    data[0] = '\\';

    for(step=0; step < UDPBC_MAX_OPTION; step++) 
    {
        pos = strlen(data);
        switch(step)
        {
            case 0: //option g
            {
                if(sf_info->flag_group) {
                    sprintf(&data[pos],"g:%s",sf_info->_group);
                }
                break;
            }
            case 1: //option s
            {
                if(sf_info->flag_group) {
                    sprintf(&data[pos], ",s:%s", (char*)&sf->src);
                } else {
                    sprintf(&data[pos], "s:%s", (char*)&sf->src);
                }
                break;
            }
            case 2: //option d
            { 
                if(sf->dst_num > 0)
                {
                    for(idx = 0; idx < sf->dst_num; idx++)
                    {
                        if(idx == 0)
                        {
                            // snprintf(&data[pos], C_MAX_SFI_LEN+4, 
                            //         ",d:%s", &sf->dst[idx]);
                        }
                        else
                        {
                            pos = strlen(data);
                            // snprintf(&data[pos], C_MAX_SFI_LEN+2, 
                            //         ",%s", &sf->dst[idx]);
                        }
                        snprintf(&data[pos], C_MAX_SFI_LEN+4,
                                        ",d:%s", (char*)&sf->dst[idx]);
                    }
                }
                else
                {
                    ;
                }
                break;
            }
            case 3: //option n
            {
                sprintf(&data[pos], ",n:%d", sf->line_cnt);
                break;
            }
            case 4: //option t
            {
                if(sf_info->flag_text)
                {
                    sprintf(&data[pos], ",t:%s", sf_info->_text);
                }
                break;
            }
        }
    }

    pos = strlen(data);
    sprintf(&data[pos], "*%02X\\", get_checksum(&data[1], pos-1));

    if(sf_info->flag_auth)
    {
        pos = strlen(data);
        int st_pos = pos+1;
        sprintf(&data[pos], "a:%s",sf_info->_auth);
        pos= strlen(data);
        sprintf(&data[pos], "*%02X\\", get_checksum(&data[st_pos], pos-st_pos));
    }

    pos = strlen(data);
    sprintf(&data[pos], "%s*%02X\r\n", msg, get_checksum(&msg[1], len-1));
    size = strlen(data)+sizeof(UdPbC_header_t);
    return size;
}

//======================================
// udpbc write process.
//======================================
///@brief SF UdPbC thread stop.
///@param arg thread info and NF pointer.
void udpbc_write_stop(void *arg)
{
    pth_args_t *args = (pth_args_t*) arg;
    sf_pth_args_t * sf_pth_args = (sf_pth_args_t*)args->args;
    //std450_list_semapore_cond_signal(&sf_pth_args->sf->tx_list);
}

///@brief IEC 61162-450 device Error list Event Process. [Not Use]
///@param signum Event Signal.
///@param si Event arguemnt.
///@param sv Not use. For Kernel.
void udpbc_write_event(int signum, void *si, void *sv)
{
    //Not use function.
}

///@brief SF UdPbC write process.
///@param arg thread info and SF pointer.
///@details 
///@todo check mutex cond wiat. Some time hang. 
void udpbc_write_proc(void *arg)
{
    ssize_t size = 0;
    pth_args_t *args = (pth_args_t*) arg;
    sf_pth_args_t * sf_pth_args = (sf_pth_args_t*)args->args;
    sf_t* sf = sf_pth_args->sf;
    std450_list_t* list = &sf->tx_list;

    std450_list_semapore_cond_tm_wait(&sf->tx_list, MAX_TX_TIMEOUT);

    while (std450_list_size(list) > 0)
    {
        udpbc_list_data_t *list_data = udpbc_list_pop(list);

        if (list_data != NULL)
        {
            size = sf->backend->_write(sf, list_data->data, list_data->size);

            if(size == list_data->size)
            { 
                sf->line_cnt++;
                if(sf->line_cnt >= MAX_PACKET_CNT) 
                {
                    sf->line_cnt = 1;
                }
            }

            MEM_FREE(list_data);
            list_data = NULL;
        }
    }
}

//======================================
// udpbc read process.
//======================================
///@brief IEC 61162-450 device NF SRP Tx process thread Stop.
///@param arg thread info and NF pointer.
void udpbc_read_stop(void *arg)
{
    pth_args_t *args = (pth_args_t*) arg;
    sf_pth_args_t * sf_pth_args=(sf_pth_args_t*)args->args;
    //std450_list_semapore_cond_signal(&sf_pth_args->sf->rx_list);
}

///@brief IEC 61162-450 device Error list Event Process. [Not Use]
///@param signum Event Signal.
///@param si Event arguemnt.
///@param sv Not use. For Kernel.
void udpbc_read_event(int signum, void *si, void *sv)
{
    //Not use function.
}

///@brief UdPbC read thread process. Get backend data. 
///@param arg thread info and NF pointer.
///@details 
///@todo error process register.
void udpbc_read_proc(void *arg)
{
    ssize_t size = 0;
    pth_args_t *args = (pth_args_t*) arg;
    sf_pth_args_t * sf_pth_args = (sf_pth_args_t*)args->args;
    sf_t* sf = sf_pth_args->sf;

    char *pRxMsg = MEM_MALLOC(C_MAX_450_BUF);
    //DEBUG_LOG_SF("[%s] list set pRxMsg:Allocated memory address: %p\r\n",__FUNCTION__, pRxMsg);
    if (pRxMsg != NULL)
    {
        memset(pRxMsg, 0x00, C_MAX_450_BUF);
        
        size = sf->backend->_read(sf, pRxMsg, C_MAX_450_BUF);

        if(size > 0)
        {
            if(check_datagram_header(sf->func_type, pRxMsg))
            {
                udpbc_list_data_t *list_data = MEM_MALLOC(sizeof(udpbc_list_data_t));
                //DEBUG_LOG_SF("list set list_data:Allocated memory address: %p\r\n", list_data);
                if (list_data != NULL)
                {
                    memset(list_data, 0x00, sizeof(udpbc_list_data_t));
                    if((size = udpbc_parsing_packet(sf, list_data->data, pRxMsg, size)) > 0)
                    {
                        list_data->size = size;
                        udpbc_list_push(&sf->rx_list, list_data);
                        DEBUG_LOG_SF("[OK] Received Packet is pushed\r\n");
                    }
                    else
                    {
                        MEM_FREE(list_data);
                        list_data = NULL;
                        DEBUG_LOG_SF("[ERROR] Received Packet is not pushed\r\n");
                    }
                }
                else
                {
                	DEBUG_LOG_SF("[ERROR] list_data has been nulling\r\n");
                }
            }
            else
            {
                DEBUG_LOG_SF("[ERROR] Received Header is not passed from header check\r\n");
            }
        }

        if(pRxMsg != NULL)
        {
            MEM_FREE(pRxMsg);
            pRxMsg = NULL;
        }
    }
}

//======================================
// udpbc init process.
//======================================

///@brief SF UdPbC initialize about thread(Rx and Tx) and linked list.
///@param sf  allocated 450 device for SF.
///@param args Option argument.
STD450_API void _std450_udpbc_init(sf_t* sf, std450_list_t* err_list, sfi_info_t* sfi_info)
{
    sf_pth_args_t* sf_pth_args = NULL;
    
    assert(sf);

    //========== Tx
    std450_list_init(&sf->tx_list);

    sf->tx_pth_args.info = &sf->tx_tinfo;
    sf->tx_pth_args.args = (void*)MEM_MALLOC(sizeof(sf_pth_args_t));
    memset(sf->tx_pth_args.args, 0x00, sizeof(sf_pth_args_t));

    sf_pth_args = (sf_pth_args_t*)(sf->tx_pth_args.args);
    sf_pth_args->sf = sf;
    sf_pth_args->err_list = err_list;
    sf_pth_args->sfi_info = sfi_info;
    pth_proc_thread_init(&sf->tx_tinfo, &sf->tx_pth_args, 
                         udpbc_write_stop, udpbc_write_proc, udpbc_write_event);

    //========== Rx
    std450_list_init(&sf->rx_list);

    sf->rx_pth_args.info = &sf->rx_tinfo;
    sf->rx_pth_args.args = (void*)MEM_MALLOC(sizeof(sf_pth_args_t));
    memset(sf->rx_pth_args.args, 0x00, sizeof(sf_pth_args_t));

    sf_pth_args = (sf_pth_args_t*)(sf->rx_pth_args.args);
    sf_pth_args->sf = sf;
    sf_pth_args->err_list = err_list;
    sf_pth_args->sfi_info = sfi_info;
    pth_proc_thread_init(&sf->rx_tinfo, &sf->rx_pth_args, 
                         udpbc_read_stop, udpbc_read_proc, udpbc_read_event);
    //========== etc
    sf->line_cnt = 1;
}

///@brief SF UdPbC initialize about thread(Rx and Tx) and linked list.
///@param sf  allocated 450 device for SF.
///@param args Option argument.
STD450_API void _std450_udpbc_exit(sf_t* sf)
{
    //thread exit
    std450_sf_udpbc_t *sf_info = (std450_sf_udpbc_t *)sf->behavior_data;

    pth_proc_thread_exit(&sf->tx_pth_args);
    pth_proc_thread_exit(&sf->rx_pth_args);

    std450_list_free(&sf->tx_list);
    std450_list_free(&sf->rx_list);

    if(sf_info != NULL)
    {
       if(sf_info->_group != NULL)
       {
           MEM_FREE(sf_info->_group);
           sf_info->_group = NULL;
       }

       if(sf_info->_text != NULL)
       {
           MEM_FREE(sf_info->_text);
           sf_info->_text = NULL;
       }

       if(sf_info->_auth != NULL)
       {
           MEM_FREE(sf_info->_auth);
           sf_info->_auth = NULL;
       }

       MEM_FREE(sf_info);
       sf->behavior_data = NULL;
    }

    if(sf->dst != NULL)
    {
        MEM_FREE(sf->dst);
        sf->dst = NULL;
    }
}

//======================================
// udpbc public function.
//======================================

///@brief IEC 61162-450 device SF udpbc packet option.
///@param sf  allocated 450 device for SF.
///@param args Option argument.
///            va_arg 1 :Opt1 g, va_arg 2 : Group ID
///            va_arg 1 :Opt2 t, va_arg 2 : Free Text Field
///            va_arg 1 :Opt2 a, va_arg 2 : Authenitication
STD450_API int _std450_udpbc_set_opt(sf_t *sf, void* args)
{
    int rtn = 0;
    int item_len = 0;
    char opt = 0x00;
    char *tmp = NULL;
    va_list *p_args = (va_list *)args;

    if(args == NULL)
    {
        if(sf->behavior_data != NULL)
        {
            MEM_FREE(sf->behavior_data);
            sf->behavior_data = NULL;
        }
        sf->behavior_data = (std450_sf_udpbc_t*)MEM_MALLOC(sizeof(std450_sf_udpbc_t));
        memset(sf->behavior_data, 0x00, sizeof(std450_sf_udpbc_t));
    }
    else 
    {
        std450_sf_udpbc_t *udpbc_opt_data = (std450_sf_udpbc_t*)sf->behavior_data;
        opt = va_arg(*p_args, int);
        if(opt == 'g') // Grouping control
        {
            tmp = va_arg(*p_args, char*);
            item_len = strlen(tmp);
            if(item_len > 0)
            {
                udpbc_opt_data->flag_group = 1;
                if(udpbc_opt_data->_group != NULL)
                {
                    MEM_FREE(udpbc_opt_data->_group);
                    udpbc_opt_data->_group = NULL;
                }
                udpbc_opt_data->_group = MEM_MALLOC(item_len + 1);
                memset(udpbc_opt_data->_group, 0x00, item_len + 1);
                memcpy(udpbc_opt_data->_group, tmp, item_len);
            }
            else
            {
                udpbc_opt_data->flag_group = 0;
                if(udpbc_opt_data->_group != NULL)
                {
                    MEM_FREE(udpbc_opt_data->_group);
                    udpbc_opt_data->_group = NULL;
                }
            }
            rtn = 1;
        } 
        else if(opt == 't') // Text string
        {
            tmp = va_arg(*p_args, char*);
            item_len = strlen(tmp);
            if(item_len > 0)
            {
                udpbc_opt_data->flag_text = 1;
                if(udpbc_opt_data->_text != NULL)
                {
                    MEM_FREE(udpbc_opt_data->_text);
                    udpbc_opt_data->_text = NULL;
                }
                udpbc_opt_data->_text = MEM_MALLOC(item_len + 1);
                memset(udpbc_opt_data->_text, 0x00, item_len + 1);
                memcpy(udpbc_opt_data->_text, tmp, item_len);
            }
            else
            {
                udpbc_opt_data->flag_text = 0;
                if(udpbc_opt_data->_text != NULL)
                {
                    MEM_FREE(udpbc_opt_data->_text);
                    udpbc_opt_data->_text = NULL;
                }
            }
            rtn = 1;
        } 
        else if(opt=='a') 
        {
            tmp = va_arg(*p_args, char*);
            item_len = strlen(tmp);

            if(strlen(tmp) > 0)
            {                              
                udpbc_opt_data->flag_auth = 1;
                if(udpbc_opt_data->_auth != NULL)
                {
                    MEM_FREE(udpbc_opt_data->_auth);
                    udpbc_opt_data->_auth = NULL;
                }
                udpbc_opt_data->_auth = MEM_MALLOC(item_len + 1);
                memset(udpbc_opt_data->_auth, 0x00, item_len + 1);
                memcpy(udpbc_opt_data->_auth, tmp, item_len);
            }
            else
            {
                udpbc_opt_data->flag_auth = 0;
                if(udpbc_opt_data->_auth != NULL)
                {
                    MEM_FREE(udpbc_opt_data->_auth);
                    udpbc_opt_data->_auth = NULL;
                }
            }
            rtn = 1;
        }
        va_end(*p_args);
    }
    return rtn;
}

///@brief IEC 61162-450 device SF udpbc packet write .
///@param sf  allocated 450 device for SF.
///@param len  UdPbC IEC 61162-1 Senetence length.
///@param msg  Send message information.
///@return Send data paket data size.
///@todo Check mutex cond signal process(sf->tx_list).
STD450_API ssize_t _std450_udpbc_write_msg(sf_t *sf, void *msg, int len)
{
    ssize_t size = 0;

    if (sf == NULL || msg == NULL)
        return 0;

    udpbc_list_data_t *list_data = MEM_MALLOC(sizeof(udpbc_list_data_t));
    if (list_data != NULL)
    {
        memset(list_data, 0x00, sizeof(udpbc_list_data_t));
        size = udpbc_make_packet(sf, (void*)(list_data->data), (char *)msg, len);

        list_data->size = size;
        udpbc_list_push(&sf->tx_list, list_data);
    }
    else
    {
        ;
    }

    return len;
}

///@brief IEC 61162-450 device SF udpbc packet read.
///@param sf  allocated 450 device for SF.
///@param len  UdPbC IEC 61162-1 Senetence length.
///@param msg  Send message information.
STD450_API ssize_t _std450_udpbc_read_msg(sf_t *sf, void *msg, int len)
{
    ssize_t size = 0;

    if (sf == NULL || msg == NULL)
        return 0;

    if(std450_list_size(&sf->rx_list) > 0)
    {
        udpbc_list_data_t *list_data = udpbc_list_pop(&sf->rx_list);
        if (list_data != NULL)
        {
            if(list_data->size > len) 
            {
                memcpy(msg, list_data->data, len);
                size = len;
            } 
            else 
            {
                memcpy(msg, list_data->data, list_data->size);
                size = list_data->size;
            }

            MEM_FREE(list_data);
            list_data = NULL;
        }
    }
    else
    {
        std450_list_semapore_cond_tm_wait(&sf->rx_list, MAX_RX_TIMEOUT);
    }

    return size;
}

///@brief IEC 61162-450 device SF function UdPbC behavior.
const std450_behavior_t _std450_udpbc_behavior = {
        _std450_udpbc_init,
        _std450_udpbc_exit,
        _std450_udpbc_set_opt, 
        _std450_udpbc_write_msg,
        _std450_udpbc_read_msg,
};

//==============================================================================


//======================================
// raudp private function.
//======================================
struct raudp_list_data_s {
    ssize_t size;
    char    *data;
};

typedef struct raudp_list_data_s raudp_list_data_t;

///@brief Data push at raudp list.
///@param list List.
///@param src Push data address.
void raudp_list_push(std450_list_t *list, raudp_list_data_t *src)
{
    assert(list);
    assert(src);

    std450_list_set(list, (void*) src);
    std450_list_semapore_cond_signal(list);
}

///@brief Data push at raudp list.
///@param list List.
///@param src Push data address.
void *raudp_list_pop(std450_list_t *list)
{
    if(list == NULL)
    {
        return NULL;
    }

    return std450_list_get(list);
}

///@brief File control mode check.
///@param rw  Read(0) is only read ok. Write(1) file create.
///@param path Path to check.
///@return Possible using path (1), Impossible using path(0)
int check_file(int rw, char* path)
{
#if 0
    int rtn = 0;
    //int opt = 0;
    FILE* fp = NULL;

    if(rw) {
        if ((fp = fopen(path, "wb")) == NULL) {
            fp = NULL;
        } else {
            fclose(fp);
            rtn = 0;
        }
    } else {
        //opt = R_OK|X_OK;
        //rtn = access(path, opt);
    }
     
    if(rtn == 0) {
        rtn = 1;
    } else {
        rtn = 0;// error processing.
    }
        
    return rtn;
#else
	return 1;
#endif
}

///@brief Wirte binary file data.
///@param path binary file path.
///@param bin binary file.
///@param bin_len binary file length.
///@return Success (Bin address), Fail (NULL)
int write_bin(char* path, char* bin, unsigned int bin_len)
{
    int rtn = 0;
    FILE* fp = NULL;

    if ((fp = fopen(path, "wrb")) == NULL)
    {
        fp = NULL;
        return 0;
    }

    if (fwrite(bin, sizeof(char), bin_len, fp) < bin_len)
    {
        ;//error. 
    } else {
        rtn =1;
    }

    if(fp != NULL)
    {
        fclose(fp);
    }

    return rtn;
}

///@brief Get binary file data.
///@param path binary file path.
///@param bin_len binary file length.
///@return Success (Bin address), Fail (NULL)
char* read_bin(char* path, unsigned int *bin_len)
{
    *bin_len = 0;
    char *bin = NULL;
    FILE *file = fopen(path, "rb");

    if(file == NULL)
    {
        ; //error.
    }
    else
    {
        fseek(file, 0, SEEK_END);
        *bin_len = ftell(file);
        bin = (char*)MEM_MALLOC(*bin_len);
        fseek(file, 0, SEEK_SET);
        fread(bin, *bin_len, 1, file);
        fclose(file);
    }

    return bin;
}

///@brief Find binary file extension and mime type get.
///@param mime_len return mime length
///@param path binary file path.
///@return Mime type buffer address.
int get_mime_type(char *mime_type, char* path, int len)
{
    char *tmp = NULL;
    int idx =0, pos = 0, exe_len = 0;
    int  mime_len = 0;

    for(idx = 0; idx < len; idx++)
    {
        if(path[idx] == '.')
        {
            pos = idx;
        }
    }

    exe_len = len - (pos + 1) ;
    tmp = &path[pos + 1];

    if(memcmp(tmp, "bmp", exe_len) == 0) {
        mime_len = strlen(MIME_TYPE_BMP);
        memcpy(mime_type, MIME_TYPE_BMP, mime_len);
    } else if(memcmp(tmp, "gif", exe_len) == 0) {
        mime_len = strlen(MIME_TYPE_GIF);
        memcpy(mime_type, MIME_TYPE_GIF, mime_len);
    }  else if(memcmp(tmp, "gtar", exe_len) == 0) {
        mime_len = strlen(MIME_TYPE_GTAR);
        memcpy(mime_type, MIME_TYPE_GTAR, mime_len);
    } else if(memcmp(tmp, "tar", exe_len) == 0) {
        mime_len = strlen(MIME_TYPE_TAR);
        memcpy(mime_type, MIME_TYPE_TAR, mime_len);
    } else if(memcmp(tmp, "zip", exe_len) == 0) {
        mime_len = strlen(MIME_TYPE_ZIP);
        memcpy(mime_type, MIME_TYPE_ZIP, mime_len);
    } else if(memcmp(tmp, "xml", exe_len) == 0) {
        mime_len = strlen(MIME_TYPE_XML);
        memcpy(mime_type, MIME_TYPE_XML, mime_len);
    } else if(memcmp(tmp, "bin", exe_len) ==0 ) {
        mime_len=strlen(MIME_TYPE_BIN);
        memcpy(mime_type, MIME_TYPE_BIN, mime_len);
    }

    return mime_len;
}

///@brief Find extension from mime type.
///@param len extension len.
///@return file extension text.
char* get_file_extension(int *len, int mime_len, char* mime_type)
{
    int extension_len = 0;
    char *extension = NULL;

    if(memcmp(MIME_TYPE_BMP, mime_type, mime_len) == 0) {
        extension_len = strlen(EXTENSION_BMP);
        extension = MEM_MALLOC(extension_len);
        memset(extension, 0x00, extension_len);
        memcpy(extension, EXTENSION_BMP, extension_len);
    } else if(memcmp(MIME_TYPE_GIF, mime_type, mime_len) == 0) {
        extension_len = strlen(EXTENSION_GIF);
        extension = MEM_MALLOC(extension_len);
        memset(extension, 0x00, extension_len);
        memcpy(extension, EXTENSION_GIF, extension_len);
    } else if(memcmp(MIME_TYPE_GTAR, mime_type, mime_len) == 0) {
        extension_len = strlen(EXTENSION_GTAR);
        extension = MEM_MALLOC(extension_len);
        memset(extension, 0x00, extension_len);
        memcpy(extension, EXTENSION_GTAR, extension_len);
    } else if(memcmp(MIME_TYPE_TAR, mime_type, mime_len) == 0) {
        extension_len = strlen(EXTENSION_TAR);
        extension = MEM_MALLOC(extension_len);
        memset(extension, 0x00, extension_len);
        memcpy(extension, EXTENSION_TAR, extension_len);
    } else if(memcmp(MIME_TYPE_ZIP, mime_type, mime_len) == 0) {
        extension_len = strlen(EXTENSION_ZIP);
        extension = MEM_MALLOC(extension_len);
        memset(extension, 0x00, extension_len);
        memcpy(extension, EXTENSION_ZIP, extension_len);
    } else if(memcmp(MIME_TYPE_XML, mime_type, mime_len) == 0) {
        extension_len = strlen(EXTENSION_XML);
        extension = MEM_MALLOC(extension_len);
        memset(extension, 0x00, extension_len);
        memcpy(extension, EXTENSION_XML, extension_len);
    } else if(memcmp(MIME_TYPE_BIN, mime_type, mime_len)==0) {
        extension_len = strlen(EXTENSION_BIN);
        extension = MEM_MALLOC(extension_len);
        memset(extension, 0x00, extension_len);
        memcpy(extension, EXTENSION_BIN, extension_len);
    }

    *len = extension_len;
    return extension;
}

///@brief Clear binary file information.
///@param sf  Allocated 450 device for SF.
void clear_raudp_binary_info(sf_t *sf)
{
    std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;

    if(sf_opt->_sts_text != NULL)
    {
        MEM_FREE(sf_opt->_sts_text);
        sf_opt->flag_sts_text = 0;
        sf_opt->_sts_text = NULL;
    }

    sf_opt->_sts_num = 0;
    sf_opt->_max_seq = 0;
    sf_opt->_curr_seq = 0;
    sf_opt->_curr_pos = 0;
    sf_opt->_dtype = 0;
}

///@brief Binary _curr_seq pluse.
///@param sf  Allocated 450 device for SF.
void add_raudp_binary_curr_seq(sf_t *sf)
{
    std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;
    sf_opt->_curr_seq += 1;
}

///@brief Binary _curr_seq pluse.
///@param sf  Allocated 450 device for SF.
void add_raudp_binary_block_id(sf_t *sf)
{
    std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;
    sf_opt->_block_id += 1;
}

///@brief Binary _curr seq is checking seq. number.
///@param sf  Allocated 450 device for SF.
///@param seq_num check Seq. number
int check_raudp_binary_curr_seq(sf_t *sf, int seq_num)
{
    std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;
    return (sf_opt->_curr_seq == seq_num) ? (1) : (0);
}

///@brief IEC 61162-450 device SF binary descriptor packet make.
///@param bin_desc Binary description data buffrer.
///@param sf  Allocated 450 device for SF.
///@param bin_len Binary file length.
///@param mime_len Mime type length.
///@param mime_type Mime type.
///@return Binary description length.
int make_bin_desc(sf_t *sf, char *bin_desc, unsigned int bin_len, char *mime_type, int mime_len)
{
    char *tmp_stsus=NULL;
    std450_sf_udp_t *udp = (std450_sf_udp_t *)sf->backend_data;
    bin_descript_t* desc = (bin_descript_t*)bin_desc;
    int dest_port = udp->targ.port;
    int flag_sts = 0, status_len = 0, status_num=0;
    int desc_len = 0, pos=0;

    if(sf->func_type == SF_FUNC_TYPE_RAUDP)
    {
        std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;
        if(sf_opt->flag_sts_text)
        {
            flag_sts = sf_opt->flag_sts_text;
            status_len = strlen(sf_opt->_sts_text)+1;
            tmp_stsus = sf_opt->_sts_text;
        }
        status_num = sf_opt->_sts_num;
    } 
    else if(sf->func_type == SF_FUNC_TYPE_RRUDP)
    {
        ;
    }
    else
    {
        return 0;
    }

    desc_len = BIN_DESC_BASE_SIZE + mime_len;
    if(flag_sts)
    {
        desc_len += status_len;
    }

    desc->Length = htonl(desc_len);
    pos = sizeof(desc->Length);
    desc->fileLength = htonl(bin_len);
    pos += sizeof(desc->fileLength);
    desc->StatusOfAcq = htons(((uint16_t)status_num));
    pos += sizeof(desc->StatusOfAcq);
    desc->AckDestPort = htons(dest_port);
    pos += sizeof(desc->AckDestPort);
    desc->TypeLength = (uint8_t)mime_len;
    pos += sizeof(desc->TypeLength);
    memcpy(&bin_desc[pos], mime_type, mime_len);
    pos += mime_len;
    desc->StatusLength = status_len;
    pos += sizeof(desc->StatusLength);

    if(status_len)
    {
        memcpy(&bin_desc[pos], tmp_stsus, status_len - 1);
        pos += status_len;
    }

    return desc_len;
}

///@brief IEC 61162-450 device SF binary packet make.
///@param msg Make packet buffer.
///@param sf  allocated 450 device for SF.
///@param bin binary file buffer.
///@param bin_len binary file length.
///@param bin_desc binary descriptor buffer.
///@param desc_len binary descriptor buffer length.
///@return Binary description length.
int make_bin_packet(sf_t *sf, char *msg, char *bin, unsigned int bin_len, char *bin_desc, int desc_len)
{
    int msg_len = 0;
    char *_dev_id = NULL;
    char *_ch_id = NULL;
    unsigned int *_block_id = NULL;
    unsigned int *_max_seq =  NULL;
    unsigned int *_curr_seq =  NULL;
    unsigned int *_curr_pos = NULL;
    unsigned short *_dtype = NULL;

    if(sf->func_type == SF_FUNC_TYPE_RAUDP)
    {
        std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;
        _dev_id = &(sf_opt->_dev_id);
        _ch_id = &(sf_opt->_ch_id);
        _block_id = &(sf_opt->_block_id);
        _max_seq = &(sf_opt->_max_seq);
        _curr_seq = &(sf_opt->_curr_seq);
        _dtype = &(sf_opt->_dtype);
        _curr_pos = &(sf_opt->_curr_pos);
    } 
    else if(sf->func_type == SF_FUNC_TYPE_RRUDP) 
    {
        return 0;
    } 
    else 
    {
        return 0;
    }

    if((*_max_seq != 0) && (*_max_seq == *_curr_seq))     //Finish.
    {
        return 0;
    }

    bin_data_t* packet450 = (bin_data_t*)msg;
    bin_header_t* header = &packet450->header;
    char *data = packet450->data;

    if(sf->func_type == SF_FUNC_TYPE_RAUDP)
    {
        memcpy((char*) header->Token, C_TOKEN_RAUDP, C_LEN_450_TOKEN);
    } 
    else if(sf->func_type==SF_FUNC_TYPE_RRUDP) 
    {
        memcpy((char*) header->Token, C_TOKEN_RAUDP, C_LEN_450_TOKEN);
    }

    header->Version = htons(BINARY_VERSION);
    header->HeaderLength = htons(BIN_HEADER_SIZE);
    memcpy((char*) header->SrcID, (char *)&sf->src, C_MAX_SFI_LEN);

    if(sf->dst_num > 0)
    {
        memcpy((char*) header->DestID, (char *)&sf->dst[0], C_MAX_SFI_LEN);    
    } 
    else 
    {
        memcpy((char*) header->DestID, "XXXXXX", C_MAX_SFI_LEN);
    }

    if(sf->func_type == SF_FUNC_TYPE_RAUDP)
    {
        *_dtype = C_BIN_MSG_TYPE_DATA;
    }

    header->Type = htons(*_dtype);
    if(*_dtype == C_BIN_MSG_TYPE_DATA) 
    {
        header->BlockID = htonl(*_block_id);
        header->SequenceNum = htonl(*_curr_seq + 1);
        header->MaxSequence = htonl(*_max_seq);
        header->Device =  *_dev_id;
        header->Channel = *_ch_id;

        if(*_curr_seq == 0)   //send header and descriptor. max_pg calc.
        { 
            int availd_sapce = C_MAX_450_BUF - (BIN_HEADER_SIZE + desc_len); ////availd sapce.

            if(bin_len <= availd_sapce)   //max_seq ==1.
            { 
                *_max_seq  = 1;
                *_curr_pos = bin_len;
            } 
            else 
            {
                int total_pg = (bin_len - availd_sapce) / BIN_MAX_ONE_SIZE; // Div. Assemble message. 

                if((bin_len - availd_sapce) % BIN_MAX_ONE_SIZE)   // remainder. Assemble message.
                { 
                    *_max_seq = total_pg + 2;
                } 
                else 
                {
                    *_max_seq=total_pg + 1;
                }

                *_curr_pos = availd_sapce;
            }

            header->MaxSequence = htonl(*_max_seq);
            memcpy(data, bin_desc, desc_len);
            memcpy(&data[desc_len], bin, *_curr_pos);
            msg_len = BIN_HEADER_SIZE + desc_len + *_curr_pos;
        } 
        else    //send header and datablock.
        { 
            unsigned int get_size = 0;
            if(sf->func_type == SF_FUNC_TYPE_RAUDP)
            {
                if(bin_len - *_curr_pos >= BIN_MAX_ONE_SIZE) 
                {
                    get_size = BIN_MAX_ONE_SIZE;
                    msg_len = C_MAX_450_BUF;
                } 
                else 
                {
                    get_size = bin_len - *_curr_pos;
                    msg_len = get_size + BIN_HEADER_SIZE;
                }
                memcpy(data, &bin[*_curr_pos], get_size);
                *_curr_pos += get_size;
            } 
            else if(sf->func_type == SF_FUNC_TYPE_RRUDP) 
            {
                *_curr_pos = (BIN_MAX_ONE_SIZE - desc_len) + BIN_MAX_ONE_SIZE * ((*_curr_seq) - 1);

                if(*_curr_seq + 1 >= *_max_seq)     //("fin-");
                { 
                    get_size = bin_len - *_curr_seq;
                    msg_len = get_size + BIN_HEADER_SIZE;
                } 
                else    //("now-");
                {
                    get_size = BIN_MAX_ONE_SIZE;
                    msg_len = C_MAX_450_BUF;
                }
                memcpy(data, &bin[*_curr_pos], get_size);
                *_curr_pos += get_size;
            }
        }
    } 
    else if(*_dtype == C_BIN_MSG_TYPE_QUERY)  //Max. Seq. Send datagram.
    {
        //Not use function.
    } 
    else if(*_dtype == C_BIN_MSG_TYPE_ACK) 
    {
        //Not use function.
    } 
    else 
    {
        ;
    }

    return msg_len;
}

///@brief IEC 61162-450 device SF binary header packet check.
///@param msg Check packet buffer.
///@param sf  allocated 450 device for SF.
///@return Vail binary header data type.
///@todo Check whether device id and channel id are required.
int check_bin_header(char *msg, sf_t *sf)
{
    int rtn = 0;
    char *_dev_id = NULL;
    char *_ch_id = NULL;
    unsigned int *_block_id = NULL;
    unsigned int *_max_seq =  NULL;
    unsigned int *_curr_seq =  NULL;
    unsigned short *_dtype = NULL;
    bin_header_t* header = (bin_header_t *)msg;

    if(sf->func_type == SF_FUNC_TYPE_RAUDP)
    {
         std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;
         _dev_id = &(sf_opt->_dev_id);
         _ch_id = &(sf_opt->_ch_id);
         _block_id = &(sf_opt->_block_id);
         _max_seq = &(sf_opt->_max_seq);
         _curr_seq = &(sf_opt->_curr_seq);
        _dtype = &(sf_opt->_dtype);
    }
    else if(sf->func_type == SF_FUNC_TYPE_RRUDP)
    {
        ;
    }
    else
    {
        rtn = -6;
        return rtn;
    }

    if(memcmp((char*)&sf->src, (char*)header->SrcID, C_MAX_SFI_LEN) == 0)   // recursion data
    {
        rtn = -1;
        return rtn;
    }

    if(sf->func_type == SF_FUNC_TYPE_RAUDP)
    {
        if(!((memcmp((char*)&sf->src, (char*)header->DestID, C_MAX_SFI_LEN) == 0)
           ||(memcmp("XXXXXX", (char*)header->DestID, C_MAX_SFI_LEN)==0)))
        {
            rtn = -2;
            return rtn;       
        }
    }
    else if(sf->func_type == SF_FUNC_TYPE_RRUDP)
    {
        // if(memcmp((char*)&sf->src, (char*)header->DestID,6)!=0) {
        //         return rtn;
        // }
    }
    else
    {
        rtn = -3;
        return rtn;
    }

    //whether device id and channel id are required.
    if(!(header->Device == *_dev_id) && (header->Channel == *_ch_id))
    {
        rtn = -4;
        return rtn;
    }

    if(sf->func_type == SF_FUNC_TYPE_RAUDP)
    {
        if(ntohs(header->Type) != C_BIN_MSG_TYPE_DATA)
        {
            rtn = -5;
            return rtn; //error binary message type. RaUdP type onle data(0x01)
        }
        else
        {
            *_dtype = C_BIN_MSG_TYPE_DATA;
        }

        if(*_block_id == ntohl(header->BlockID))  //Saving data check.
        {
            if((ntohl(header->SequenceNum) == *_curr_seq + 1)
                &&(*_max_seq == ntohl(header->MaxSequence)))
            {
                *_curr_seq = ntohl(header->SequenceNum);
                rtn = C_BIN_MSG_TYPE_DATA;
            }
            else
            {
                rtn = -6;
            }
        }
        else    //New data
        {
            if(ntohl(header->SequenceNum) == 1)
            {
                *_block_id = ntohl(header->BlockID);
                *_max_seq = ntohl(header->MaxSequence);
                *_curr_seq = ntohl(header->SequenceNum);
                rtn = C_BIN_MSG_TYPE_DATA;
            }
            else
            {
                rtn = -7;
                //Exit Skip.
            }
        }
    } 
    else if(sf->func_type == SF_FUNC_TYPE_RRUDP)
    {
        //Not use function.
    }

    return rtn;
}

///@brief Get binary descrtion.
///@return File extention information. 
char* get_bin_desc(char *msg, bin_descript_t* bin_desc, int *extension_len)
{
    int pos = 0;
    char *extension = NULL;
    unsigned short StatusLength = 0;
    bin_descript_t* rcv_desc = (bin_descript_t*)&msg[BIN_HEADER_SIZE];

    bin_desc->Length = ntohl(rcv_desc->Length);
    bin_desc->fileLength = ntohl(rcv_desc->fileLength);
    bin_desc->StatusOfAcq = ntohs(rcv_desc->StatusOfAcq);
    bin_desc->AckDestPort = ntohs(rcv_desc->AckDestPort);
    bin_desc->TypeLength = rcv_desc->TypeLength;
    pos = offsetof(struct bin_descript_s, DataType)+BIN_HEADER_SIZE;

    if(bin_desc->TypeLength > 0) 
    {
        bin_desc->DataType = MEM_MALLOC(bin_desc->TypeLength);
        memset(bin_desc->DataType, 0x00, bin_desc->TypeLength);
        memcpy(bin_desc->DataType, &msg[pos], bin_desc->TypeLength);
        extension = get_file_extension(extension_len, bin_desc->TypeLength - 1, (char *)bin_desc->DataType);
    }

    pos += bin_desc->TypeLength;
    memcpy(&StatusLength, &msg[pos], sizeof(unsigned short));
    bin_desc->StatusLength = ntohs(StatusLength);
    pos += sizeof(unsigned short);

    if(bin_desc->StatusLength > 0) 
    {
        bin_desc->StatusInfor = MEM_MALLOC(bin_desc->StatusLength);
        memset(bin_desc->StatusInfor, 0x00, bin_desc->StatusLength);
        memcpy(bin_desc->StatusInfor, &msg[pos], bin_desc->StatusLength);
    }

    return extension;
}

///@brief Copy message to binanry. Message posion not check. Only Copy.
///@param sf  allocated 450 device for SF.
///@param msg To copy, message buffer point address.
///@param copy_len Copy length.
void copy_msg2bin(sf_t *sf, char *msg, char *bin, int copy_len)
{
    unsigned int tmp_pos = 0;
    unsigned int *_curr_pos = NULL;

    if (sf->func_type == SF_FUNC_TYPE_RAUDP)
    {
        std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;
        _curr_pos = &(sf_opt->_curr_pos);
    }
    else if (sf->func_type == SF_FUNC_TYPE_RRUDP)
    {
        //Not use function.
    }
    else
    {
        return;
    }

    tmp_pos = *_curr_pos;
    memcpy(&bin[tmp_pos], msg, copy_len);
    tmp_pos += copy_len;
    *_curr_pos = tmp_pos;
}

///@brief File length compare about receive total length and binary descriptor file length.
///@return Same(0), More(1), less(-1)
int check_raudp_binary_curr_pos(sf_t *sf, int bin_len)
{
    unsigned int tmp_pos = 0;
    unsigned int *_curr_pos = NULL;

    if (sf->func_type == SF_FUNC_TYPE_RAUDP)
    {
        std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;
        _curr_pos = &(sf_opt->_curr_pos);
    }
    else if (sf->func_type == SF_FUNC_TYPE_RRUDP)
    {
        //Not use function.
    }
    else
    {
        return 0;
    }

    tmp_pos = *_curr_pos;
    if(tmp_pos == bin_len)
    {
        return 0;
    }
    else if(tmp_pos<bin_len)
    {
        return -1;
    }
    else
    {
        return 1;
    }
}

//======================================
// raudp write process.
//======================================
///@brief SF RaUdP thread stop.
///@param arg thread info and NF pointer.
void raudp_write_stop(void *arg)
{
    pth_args_t *args = (pth_args_t*) arg;
    sf_pth_args_t * sf_pth_args = (sf_pth_args_t*)args->args;
    //std450_list_semapore_cond_signal(&sf_pth_args->sf->tx_list);
}

///@brief IEC 61162-450 device Error list Event Process. [Not Use]
///@param signum Event Signal.
///@param si Event arguemnt.
///@param sv Not use. For Kernel.
void raudp_write_event(int signum, void *si, void *sv)
{
    //Not use function.
}

///@brief SF RaUdP write process.
///@param arg thread info and SF pointer.
///@details 
///@todo check mutex cond wiat. Some time hang. 
void raudp_write_proc(void *arg)
{
    ssize_t size =0, tmp_size=0, tmp_line=0;
    unsigned int bin_len =0;
    int desc_len = 0, mime_len =0;
    char mime_type[MIME_TYPE_LEN];
    char *bin_desc = NULL;
    pth_args_t *args = (pth_args_t*) arg;
    sf_pth_args_t * sf_pth_args=(sf_pth_args_t*)args->args;
    sf_t* sf = sf_pth_args->sf;
    std450_list_t* list = &sf->tx_list;
    char *msg_data = NULL;
    int msg_len = 0;

    std450_list_semapore_cond_tm_wait(&sf->tx_list, MAX_TX_TIMEOUT);

    while(std450_list_size(list) > 0)
    {
        raudp_list_data_t* list_data = raudp_list_pop(list);

        if(list_data != NULL && list_data->size) 
        {
            if(list_data->data != NULL) 
            {
                msg_data = MEM_MALLOC(C_MAX_450_BUF);
                memset(msg_data, 0x00, C_MAX_450_BUF);

                bin_desc = MEM_MALLOC(BIN_MAX_ONE_SIZE);
                memset(bin_desc, 0x00, BIN_MAX_ONE_SIZE);

                memset(mime_type, 0x00, MIME_TYPE_LEN);
                mime_len = get_mime_type(mime_type, (char *)list_data->data, list_data->size);
                desc_len = make_bin_desc(sf, bin_desc, list_data->size, mime_type, mime_len);

                do {
                    msg_len = make_bin_packet(sf, msg_data, (char *)list_data->data, bin_len, bin_desc, desc_len);

                    if(msg_len > 0) 
                    {
                        tmp_size = sf->backend->_write(sf, msg_data, msg_len);
                        add_raudp_binary_curr_seq(sf);
                        size += tmp_size;
                    }
                } while(msg_len > 0);

                if(size > 0) 
                {
                    add_raudp_binary_block_id(sf);
                    tmp_line = sf->line_cnt;

                    if(tmp_line + 1 == 1000) 
                    {
                        sf->line_cnt = 1;
                    } 
                    else 
                    {
                        sf->line_cnt = tmp_line + 1;
                    }
                }
            } 
            else 
            {
                ;
            }
        }

        if(msg_data != NULL) 
        {
            MEM_FREE(msg_data);
            msg_data = NULL;
        }

        if(bin_desc != NULL) 
        {
            MEM_FREE(bin_desc);
            bin_desc = NULL;
        }

        clear_raudp_binary_info(sf);

        if(list_data != NULL) 
        {
            if(list_data->data != NULL) 
            {
                MEM_FREE(list_data->data);
                list_data->data = NULL;
            }                

            MEM_FREE(list_data);
            list_data = NULL;
        }
    } 
}

//======================================
// raudp read process.
//======================================
///@brief SF RaUdP thread stop.
///@param arg thread info and NF pointer.
void raudp_read_stop(void *arg)
{
    pth_args_t *args = (pth_args_t*) arg;
    sf_pth_args_t * sf_pth_args=(sf_pth_args_t*)args->args;
    //std450_list_semapore_cond_signal(&sf_pth_args->sf->rx_list);
}

///@brief IEC 61162-450 device Error list Event Process. [Not Use]
///@param signum Event Signal.
///@param si Event arguemnt.
///@param sv Not use. For Kernel.
void raudp_read_event(int signum, void *si, void *sv)
{
    //Not use function.
}

///@brief RaUdP read thread process. Get backend data. 
///@param arg thread info and NF pointer.
///@details 
///@todo error process register.
void raudp_read_proc(void *arg)
{
    int rtn = 0;
    int desc_len = 0, extension_len = 0, tmp_pos=0;
    int path_len = 0, tmp = 0;
    ssize_t size = 0, bin_len = 0, bin_tmp_len = 0, tmp_size = 0;
    char *msg = NULL, *path = NULL, *full_path = NULL;
    char **bin = NULL, **extension = NULL;
    pth_args_t *args = (pth_args_t*) arg;
    sf_pth_args_t * sf_pth_args=(sf_pth_args_t*)args->args;
    sf_t* sf = sf_pth_args->sf;
    raudp_list_data_t* list_data = NULL;
    bin_descript_t *bin_desc = NULL;

    if(std450_list_size(&sf->rx_list) > 0) 
    {
        list_data = raudp_list_pop(&sf->rx_list);

        if (list_data == NULL)
            return;

        if(list_data->size > 0) {
            path_len = list_data->size;
            path = list_data->data;
            msg = MEM_MALLOC(C_MAX_450_BUF);
        }
    } 

    if(path_len > 0) 
    {
        bin_desc = MEM_MALLOC(sizeof(bin_descript_t));
        memset(bin_desc, 0x00, sizeof(bin_descript_t));
        do 
        {
            memset(msg, 0x00, C_MAX_450_BUF);
            tmp_size = sf->backend->_read(sf, msg, C_MAX_450_BUF);
            tmp = BIN_HEADER_SIZE;
            std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;

            if(tmp_size > 0)
            {
                extension_len = sf_opt->_extension_len;
                extension =&(sf_opt->_extension);
                bin_len = sf_opt->_bin_len;
                bin = &(sf_opt->_bin);
            }

            if((tmp_size > tmp) && (tmp_size <= C_MAX_450_BUF)) 
            {
                if(check_datagram_header(SF_FUNC_TYPE_RAUDP, msg))  //Step1. Check Datagram Header.
                {
                    rtn = check_bin_header(msg, sf);

                    if(rtn > 0)   //Step2. Check Bin Heaer
                    { 
                        if(check_raudp_binary_curr_seq(sf,1))       //Step 3-1. Get Desc.& File.
                        { 
                            if((*bin) != NULL) 
                            {
                                MEM_FREE(*bin);
                                *bin = NULL;
                            }

                            if((*extension) != NULL) 
                            {
                                MEM_FREE(*extension);
                                *extension = NULL;
                            }
                            *extension = get_bin_desc(msg, bin_desc, (int *)&sf_opt->_extension_len);

                            if(*extension != NULL) 
                            {
                                desc_len = bin_desc->Length;
                                bin_len = bin_desc->fileLength;

                                if(bin_len>0) 
                                {
                                    sf_opt->_bin_len = bin_len;               
                                    *bin = MEM_MALLOC(bin_len);
                                    memset(*bin, 0x00, bin_len);
                                } 
                                else 
                                {
                                    break;
                                }

                                tmp_pos = (BIN_HEADER_SIZE+desc_len); 
                                bin_tmp_len = (tmp_size-tmp_pos);

                                if(C_MAX_450_BUF < (bin_tmp_len+tmp_pos)) 
                                {
                                    clear_raudp_binary_info(sf);
                                    if((*bin) != NULL) 
                                    {
                                        MEM_FREE(*bin);
                                        *bin = NULL;
                                    }

                                    if((*extension) != NULL) 
                                    {
                                        MEM_FREE(*extension);
                                        *extension = NULL;
                                    }
                                    break;// ("ERR:Max size\n");
                                }

                                if(bin_tmp_len > 0) 
                                {
                                    copy_msg2bin(sf,
                                                &msg[tmp_pos], 
                                                *bin, 
                                                bin_tmp_len);
                                } 
                                else 
                                {
                                    break;
                                }
                            } 
                            else    // ERR:get raudp bin desc error.
                            {
                                break;
                            }
                        } 
                        else    //Step 3-2. Get File
                        {
                            if((*bin != NULL) && (bin_len > 0)) 
                            {
                                tmp_pos = BIN_HEADER_SIZE;
                                bin_tmp_len = tmp_size-tmp_pos;
                                copy_msg2bin(sf,
                                        &msg[tmp_pos], 
                                        *bin, 
                                        bin_tmp_len);
                            }
                        }

                        size += tmp_size;
                    }
                    else    //ERR:Clear buffer. and exit.
                    { 
                        if(rtn == -6) 
                        {
                            clear_raudp_binary_info(sf);
                            if((*bin) != NULL) 
                            {
                                // DEBUG_LOG_SF("bin free\n");
                                MEM_FREE(*bin);
                                *bin = NULL;
                            }

                            if((*extension) != NULL) 
                            {
                                // DEBUG_LOG_SF("extension free\n");
                                MEM_FREE(*extension);
                                *extension = NULL;
                            }
                        }
                        break;
                    }
                } 
                else    //ERR: header token error.
                {
                    break;
                }
            } 
            else    //size error.
            { 
                break;
            }

            if(check_raudp_binary_curr_pos(sf,bin_len) >= 0)  //Max.
            { 
                break;
            }
        } while((tmp_size > 0));
    }

    if((bin_len != 0) && (check_raudp_binary_curr_pos(sf, bin_len) == 0))    //File write.
    { 
        size = 0;
        if(path_len > 0) 
        {
            full_path = MEM_MALLOC(path_len + 1);
            memset(full_path, 0x00, path_len + 1);
            memcpy(full_path, path, path_len);
        }

        if(write_bin(full_path, *bin, bin_len) == 0) 
        {
            ;//error.
        } 
        else 
        {
            clear_raudp_binary_info(sf);
            size = bin_len;
        }

        if(full_path != NULL) 
        {
            MEM_FREE(full_path);
            full_path = NULL;
        }
    } 
    else    //file write size error.
    { 
        size = 0;
    }

    if(size > 0) 
    {
        if(list_data->data != NULL) 
        {
            MEM_FREE(list_data->data);
            list_data->data = NULL;
            list_data->size = 0;
        }

        list_data->size = sizeof(ssize_t);
        list_data->data = MEM_MALLOC(list_data->size);
        memset(list_data->data, 0x00, list_data->size);
        memcpy((ssize_t*)list_data->data, &size, list_data->size);
        raudp_list_push(&sf->rx_list, list_data);
    }

    if(bin_desc != NULL) 
    {
        if(bin_desc->DataType != NULL)
        {
            MEM_FREE(bin_desc->DataType);
        }

        if(bin_desc->StatusInfor != NULL)
        {
            MEM_FREE(bin_desc->StatusInfor);
        }

        MEM_FREE(bin_desc);
        bin_desc = NULL;
    }

    if(msg != NULL) 
    {
        MEM_FREE(msg);
        msg = NULL;
    }

    if(list_data != NULL) 
    {
        if(list_data->data != NULL) 
        {
            MEM_FREE(list_data->data);
        }

        MEM_FREE(list_data);
        list_data = NULL;
    }
}

//======================================
// raudp init process.
//======================================

///@brief SF RaUdP initialize about thread(Rx and Tx) and linked list.
///@param sf  allocated 450 device for SF.
///@param args Option argument.
void _std450_raudp_init(sf_t* sf, std450_list_t* err_list, sfi_info_t* sfi_info)
{
    if(sf == NULL)
    {
        return;
    }

    sf_pth_args_t* sf_pth_args = NULL;

    //========== Tx
    std450_list_init(&sf->tx_list);

    sf->tx_pth_args.info = &sf->tx_tinfo;
    sf->tx_pth_args.args = (void*)MEM_MALLOC(sizeof(sf_pth_args_t));
    memset(sf->tx_pth_args.args, 0x00, sizeof(sf_pth_args_t));

    sf_pth_args = (sf_pth_args_t*)(sf->tx_pth_args.args);
    sf_pth_args->sf = sf;
    sf_pth_args->err_list = err_list;
    sf_pth_args->sfi_info = sfi_info;
    pth_proc_thread_init(&sf->tx_tinfo, &sf->tx_pth_args, 
                         raudp_write_stop, raudp_write_proc, raudp_write_event);

    //========== Rx
    std450_list_init(&sf->rx_list);

    sf->rx_pth_args.info = &sf->rx_tinfo;
    sf->rx_pth_args.args = (void*)MEM_MALLOC(sizeof(sf_pth_args_t));
    memset(sf->rx_pth_args.args, 0x00, sizeof(sf_pth_args_t));

    sf_pth_args = (sf_pth_args_t*)(sf->rx_pth_args.args);
    sf_pth_args->sf = sf;
    sf_pth_args->err_list = err_list;
    sf_pth_args->sfi_info = sfi_info;
    pth_proc_thread_init(&sf->rx_tinfo, &sf->rx_pth_args, 
                         raudp_read_stop, raudp_read_proc, raudp_read_event);

    //========== etc
    sf->line_cnt = 1;
}

///@brief SF RaUdP initialize about thread(Rx and Tx) and linked list.
///@param sf  allocated 450 device for SF.
///@param args Option argument.
void _std450_raudp_exit(sf_t* sf)
{
    //thread exit
    int idx =0;
    std450_sf_raudp_t *sf_opt = (std450_sf_raudp_t *)sf->behavior_data;

    pth_proc_thread_exit(&sf->tx_pth_args);
    pth_proc_thread_exit(&sf->rx_pth_args);

    std450_list_free(&sf->tx_list);
    std450_list_free(&sf->rx_list);

    if(sf_opt != NULL)
    {
       if(sf_opt->_sts_text != NULL)
       {
           MEM_FREE(sf_opt->_sts_text);
           sf_opt->_sts_text = NULL;
       }

       MEM_FREE(sf_opt);
       sf_opt = NULL;
    }

    if(sf->dst != NULL)
    {
        for(idx = 0; idx < sf->dst_num; idx++)
        {
            MEM_FREE(&sf->dst[idx]);
            sf->dst = NULL;
        }
    }
}


//======================================
// raudp public function.
//======================================

///@brief IEC 61162-450 device SF raudp packet set option.
///@param sf  allocated 450 device for SF.
///@param args Option argument.
///            va_arg 1 :Opt1 d, va_arg 2 : Device id
///            va_arg 1 :Opt2 c, va_arg 2 : Channel id
///            va_arg 1 :Opt2 s, va_arg 2 : Error status number. [Normal not using]
///            va_arg 1 :Opt2 t, va_arg 2 : Error status text information.
STD450_API int _std450_raudp_set_opt(sf_t *sf, void* args)
{
    int rtn =0;
    int item_len = 0;
    char opt = 0x00;
    char *tmp = NULL;
    int tmp_data = 0;
    va_list *p_args= (va_list *)args;
    std450_sf_raudp_t *raudp_opt_data = NULL;

    if(args == NULL) 
    {
        if(sf->behavior_data != NULL) 
        {
            MEM_FREE(sf->behavior_data);
            sf->behavior_data = NULL;
        }

        sf->behavior_data = (std450_sf_raudp_t*)MEM_MALLOC(sizeof(std450_sf_raudp_t));
        memset(sf->behavior_data, 0x00, sizeof(std450_sf_raudp_t));
        raudp_opt_data = (std450_sf_raudp_t*)sf->behavior_data;
        raudp_opt_data->_block_id = rand();
        raudp_opt_data->_sts_text=NULL;
        raudp_opt_data->_extension=NULL;
        raudp_opt_data->_bin=NULL;
    } else {
        raudp_opt_data = (std450_sf_raudp_t*)sf->behavior_data;
        opt = va_arg(*p_args, int);

        if(opt== 'd') // Destination Identification
        {
            tmp_data = va_arg(*p_args, int);
            raudp_opt_data->_dev_id=tmp_data;
            rtn =1;
        } 
        else if(opt== 'c') // Time, sec
        {
            tmp_data = va_arg(*p_args, int);
            raudp_opt_data->_ch_id=tmp_data;
            rtn =1;
        }
        else if(opt== 's') // Source Inentification
        {
            tmp_data = va_arg(*p_args, int); // status number.
            raudp_opt_data->_sts_num=tmp_data;
            tmp = va_arg(*p_args, char*);

            if(tmp!=NULL)
            {
                item_len= strlen(tmp);
            }

            if(item_len)
            {
                if(raudp_opt_data->_sts_text!=NULL) {
                    MEM_FREE(raudp_opt_data->_sts_text);
                }

                raudp_opt_data->_sts_text = MEM_MALLOC(item_len + 1);
                memset(raudp_opt_data->_sts_text, 0x00, item_len + 1);
                memcpy(raudp_opt_data->_sts_text, tmp, item_len);
                raudp_opt_data->flag_sts_text=1;
            } else {
                raudp_opt_data->flag_sts_text=0;

                if(raudp_opt_data->_sts_text!=NULL) {
                    MEM_FREE(raudp_opt_data->_sts_text);
                    raudp_opt_data->_sts_text = NULL;
                }
            }
            rtn =1;
        } else {
            ;
        }

        va_end(*p_args);
    }

    return rtn;
}

///@brief IEC 61162-450 device SF raudp packet send.
///@param sf  allocated 450 device for SF.
///@param len  File full path length.
///@param fpath  File full path.
///@return Send data paket data size. (or Success(1) /Fail(0))
///@todo Check mutex cond signal process(sf->tx_list).
STD450_API ssize_t _std450_raudp_write_msg(sf_t *sf, void *pmsg, int len)
{
    ssize_t size = len;
    raudp_list_data_t list_data;

    if (sf == NULL || pmsg == NULL)
        return 0;

    if(size) 
    {
        list_data.size = size;
        list_data.data = MEM_MALLOC(size);

        if(list_data.data != NULL)
        {
            memset(list_data.data, 0x00, size);
            memcpy(list_data.data, pmsg, size);
            raudp_list_push(&sf->tx_list, &list_data);
        }
    } 
    else 
    {
        size = 0;
    }

    return size;
}

///@brief IEC 61162-450 device SF raudp packet receive.
///@param sf  Allocated 450 device for SF.
///@param len  File full path length.
///@param fpath  File full path.
STD450_API ssize_t _std450_raudp_read_msg(sf_t *sf, void *pmsg, int len)
{
    ssize_t size = 0;

    if (sf == NULL || pmsg == NULL)
        return 0;

    if (std450_list_size(&sf->rx_list) > 0)
    {
        raudp_list_data_t *list_data = raudp_list_pop(&sf->rx_list);

        if (list_data != NULL)
        {
            if (list_data->data != NULL)
            {
                if(list_data->size > len)
                {
                    memcpy(pmsg, list_data->data, len);
                    size = len;
                }
                else
                {
                    memcpy(pmsg, list_data->data, list_data->size);
                    size = list_data->size;
                }

                MEM_FREE(list_data->data);
                list_data->data = NULL;
                list_data->size = 0;
            }

            MEM_FREE(list_data);
            list_data = NULL;
        }
    }
    else
    {
        std450_list_semapore_cond_tm_wait(&sf->rx_list, MAX_RX_TIMEOUT);
    }

    return size;
}

///@brief IEC 61162-450 device SF function RaUdP behavior.
const std450_behavior_t _std450_raudp_behavior = {
    _std450_raudp_init,
    _std450_raudp_exit,
    _std450_raudp_set_opt, 
    _std450_raudp_write_msg,
    _std450_raudp_read_msg,
};


//==============================================================================
///@brief IEC 61162-450 device SF function RrUdP behavior.
const std450_behavior_t _std450_rrudp_behavior = {
    // _std450_rrudp_make, 
    // _std450_rrudp_desc_make,
    // _std450_rrudp_check_header,
    // _std450_rrudp_desc_get,
    // _std450_rrudp_msg_get,
};

//==============================================================================
///@brief IEC 61162-450 device SF function NkPgN behavior.
const std450_behavior_t _std450_nkpgn_behavior = {
    // _std450_nkpgn_make, 
    // _std450_nkpgn_desc_make,
    // _std450_nkpgn_check_header,
    // _std450_nkpgn_desc_get,
    // _std450_nkpgn_msg_get,
};

//==============================================================================
///@brief IEC 61162-450 device SF function RrTcP behavior.
const std450_behavior_t _std450_rrtcp_behavior = {
    // _std450_rrtcp_make, 
    // _std450_rrtcp_desc_make,
    // _std450_rrtcp_check_header,
    // _std450_rrtcp_desc_get,
    // _std450_rrtcp_msg_get,
};

//==============================================================================
///@brief IEC 61162-450 device SF allocate.
///@param dev450 allocate 450 device for SF.
///@param sf_count The number to create on the device.
///@return Success(1), Fail (0)
STD450_API int  std450_new_sf(std450_t *dev450, int sf_count)
{
    int rtn = 1, idx = 0;
    sf_t **sf_group;
    dev450->sfi_info.sf_count = sf_count;

    if(dev450->sfi_info.sfi_list != NULL)
    {
        return 0;
    }

    if (sf_count < 1)
    {
        return 0;
    }

    dev450->sfi_info.sfi_list = (sfi_t*)MEM_MALLOC(sizeof(sfi_t) * sf_count);
    DEBUG_LOG_SF("dev450->sfi_info.sfi_list:Allocated memory address: %p\r\n", dev450->sfi_info.sfi_list);
    if(dev450->sfi_info.sfi_list == NULL)
    {
        return 0;
    }

    sf_group = (sf_t**)MEM_MALLOC(sf_count * sizeof(sf_t*));
    DEBUG_LOG_SF("sf_group:Allocated memory address: %p, count : %d\r\n", sf_group, sf_count);
    memset(sf_group, 0x00, sf_count * sizeof(sf_t*));
    if(sf_group != NULL) 
    {
        for(idx = 0; idx < sf_count; idx++) 
        {
            sf_group[idx] = (sf_t*)MEM_MALLOC(sizeof(sf_t));
            DEBUG_LOG_SF("sf_group[idx]:Allocated memory address: %p, idx : %d\r\n", sf_group[idx], idx);
            memset(sf_group[idx], 0x00, sizeof(sf_t));
            if(sf_group[idx] == NULL) 
            {
                rtn = 0;
                break;
            }
        }

        if(rtn == 0)
        {
            for(idx = 0; idx < sf_count; idx++) 
            {
                if(sf_group[idx] != NULL) 
                {
                    MEM_FREE(sf_group[idx]);
                    sf_group[idx] = NULL;
                }
            }

            MEM_FREE(sf_group);
            sf_group = NULL;
        }
    } 
    else
    {
        rtn = 0;
    }

    if(rtn == 1)
    {
        dev450->sf = sf_group;
    }

    return rtn;
}

///@brief IEC 61162-450 device SF NIC set.
///@param dev450 allocate 450 device for SF.
STD450_API void std450_free_sf(std450_t *dev450)
{
    int idx = 0;
    sf_t** sf_group = dev450->sf;

    if(sf_group == NULL) {
        return;
    }

    for(idx = 0; idx < dev450->sfi_info.sf_count; idx++)
    {
        if(sf_group[idx] != NULL)
        {
            if(sf_group[idx]->behavior != NULL)
            {
                sf_group[idx]->behavior->_exit(sf_group[idx]);
            }

            if (sf_group[idx]->dst != NULL)
            {
                MEM_FREE(sf_group[idx]->dst);
                sf_group[idx]->dst = NULL;
            }

            MEM_FREE(sf_group[idx]);
            sf_group[idx] = NULL;
        }
    }

    if(sf_group != NULL)
    {    
        MEM_FREE(sf_group);
        dev450->sf = NULL;
    }

    if(dev450->sfi_info.sfi_list != NULL)
    {
        MEM_FREE(dev450->sfi_info.sfi_list);
        dev450->sfi_info.sfi_list = NULL;
        dev450->sfi_info.sf_count = 0;
    }
}

///@brief IEC 61162-450 device SF open API.
///@param dev450 allocate 450 device for SF.
STD450_API int  std450_sf_open(std450_t *dev450, int idx, ...)
{
    if(idx>dev450->sfi_info.sf_count) {
        return 0;
    }

    sf_t** sf_group = dev450->sf;
    sf_t* sf = sf_group[idx];
    va_list p_args;
    va_start(p_args, idx);

    return sf->backend->_open(sf, (void*)&p_args);
}

///@brief IEC 61162-450 device SF close API.
///@param dev450 allocate 450 device for SF.
STD450_API int  std450_sf_close(std450_t *dev450, int idx)
{
    if(idx>dev450->sfi_info.sf_count) {
        return 0;
    }

    sf_t** sf_group = dev450->sf;
    sf_t* sf = sf_group[idx];

    return sf->backend->_close(sf);
}

///@brief IEC 61162-450 device SF backend and behavior set.
///@param dev450 450 devices with SF allocated. 
///@param idx SF idx number to set type.
///@param comm_type SF communication type. 
///@param func_type SF 450 function type.
///@return Success(1), Fail (0)
STD450_API int  std450_sf_type_set(std450_t *dev450, int idx, int comm_type, int func_type)
{
    sf_t** sf_group = dev450->sf;
    sf_t*  sf = sf_group[idx];

    if((sf == NULL) || (sf_group == NULL)) 
    {
        return 0;
    }

    if((comm_type < SF_COMM_TYPE_NONE) || (comm_type > SF_COMM_TYPE_MAX)) 
    {
        return 0;
    }

    if((func_type < SF_FUNC_TYPE_NONE) || (func_type > SF_FUNC_TYPE_MAX)) 
    {
        return 0;
    }

    sf->id = idx;
    sf->comm_type = comm_type;
    sf->func_type = func_type;

    switch (comm_type) {
    case SF_COMM_TYPE_UDP:
        sf->backend = &_std450_udp_backend;
        sf->backend_data = NULL;
        break;
    case SF_COMM_TYPE_UART:
        sf->backend = &_std450_uart_backend;
        sf->backend_data = NULL;
        break;
    case SF_COMM_TYPE_CAN:
        sf->backend = &_std450_can_backend;
        sf->backend_data = NULL;
        break;
    case SF_COMM_TYPE_TCP:
        sf->backend = &_std450_tcp_backend;
        sf->backend_data = NULL;
        break;
    default:
        break;
    }

    switch (func_type) {
    case SF_FUNC_TYPE_UDPBC:
        sf->behavior = &_std450_udpbc_behavior;
        sf->behavior_data = NULL;

        if(dev450->nf == NULL) {
            sf->behavior->_init(sf, NULL, &dev450->sfi_info);
        } else {
            sf->behavior->_init(sf, &dev450->nf->err_list, &dev450->sfi_info);
        }
        sf->behavior->_set_opt(sf, NULL);
        break;
    case SF_FUNC_TYPE_RAUDP:
        sf->behavior = &_std450_raudp_behavior;
        sf->behavior_data = NULL;
        if(dev450->nf == NULL) {
            sf->behavior->_init(sf, NULL, &dev450->sfi_info);
        } else {
            sf->behavior->_init(sf, &dev450->nf->err_list, &dev450->sfi_info);
        }
        sf->behavior->_set_opt(sf, NULL);
        break;
    case SF_FUNC_TYPE_RRUDP:
        sf->behavior = &_std450_rrudp_behavior;
        sf->behavior_data = NULL;
        //sf->behavior->_set_opt();
        break;
    case SF_FUNC_TYPE_NKPGN:
        sf->behavior = &_std450_nkpgn_behavior;
        sf->behavior_data = NULL;
        //sf->behavior->_set_opt();
        break;
    case SF_FUNC_TYPE_RRTCP:
        sf->behavior = &_std450_rrtcp_behavior;
        sf->behavior_data = NULL;
        //sf->behavior->_set_opt();
        break;
    default:
        break;
    }

    return 1;
}

///@brief IEC 61162-450 device source SFI register.
///@param dev450 allocated 450 device for SF.
///@param idx    sf index in the device to sfi setting.
///@param talker src sfi talker[cc]
///@param number src sfi number[xxxx].
///@return Success(1), Fail(0)
STD450_API int std450_sf_set_src(std450_t *dev450, int idx, char* talker, int number)
{
    int rtn = 0;
    sf_t** sf_group = dev450->sf;
    sf_t *sf = sf_group[idx];

    if (talker != NULL && strlen(talker) >= C_LEN_TCKEN)
    {
        sprintf((char*)&sf->src, "%c%c%04d", talker[0], talker[1], number);
        if(dev450->sfi_info.sfi_list != NULL)
        {
            memcpy(&dev450->sfi_info.sfi_list[idx], &sf->src, sizeof(sfi_t));
            rtn = 1;
        }
    }

    return rtn;
}

///@brief IEC 61162-450 device destination SFI register.
///@param dev450 allocated 450 device for SF.
///@param idx    sf index in the device to sfi setting.
///@param talker dst sfi talker[cc]
///@param number dst sfi number[xxxx].
///@return Success(1), Fail(0)
STD450_API int std450_sf_set_dst(std450_t *dev450, int idx, char* talker, int number)
{
    int rtn = 0;
    sf_t** sf_group = dev450->sf;
    sf_t *sf = sf_group[idx];

    if (talker != NULL && strlen(talker) >= C_LEN_TCKEN)
    {
        if(sf->dst != NULL)
        {
            MEM_FREE(sf->dst);
            sf->dst = NULL;
        }

        sf->dst = (sfi_t*)MEM_MALLOC(sizeof(sfi_t));
        if(sf->dst != NULL)
        {
            memset(sf->dst, 0x00, sizeof(sfi_t));
            sprintf((char*)&sf->dst[0],"%c%c%04d", talker[0], talker[1], number);
            sf->dst_num = 1;
            rtn = 1;
        }
    }

    return rtn;
}

///@brief IEC 61162-450 device destination SFI adding.
///@param dev450 allocated 450 device for SF.
///@param idx    sf index in the device to sfi setting.
///@param talker dst sfi talker[cc]
///@param number dst sfi number[xxxx].
///@return Success(1), Fail (0)
STD450_API int  std450_sf_add_dst(std450_t *dev450, int idx, char* talker, int number)
{
    int rtn = 1;
    sf_t** sf_group = dev450->sf;
    sf_t *sf = sf_group[idx];

    if (talker != NULL && strlen(talker) >= C_LEN_TCKEN)
    {
        if (sf->dst == NULL)
        {
            sf->dst = (sfi_t*)MEM_MALLOC(sizeof(sfi_t));
            if(sf->dst != NULL)
            {
                memset(sf->dst, 0x00, sizeof(sfi_t));
                sprintf((char*)&sf->dst[0],"%c%c%04d", talker[0], talker[1], number);
                sf->dst_num = 1;
            }
            else
            {
                rtn = 0;
            }
        }
        else
        {
            sfi_t *tmp = sf->dst;
            sf->dst = (sfi_t*)MEM_MALLOC(sizeof(sfi_t) * (sf->dst_num + 1));
            if(sf->dst != NULL)
            {
                memcpy(sf->dst, tmp, sizeof(sfi_t) * sf->dst_num);
                MEM_FREE(tmp);

                sprintf((char*)&sf->dst[sf->dst_num], "%c%c%04d", talker[0], talker[1], number);
                sf->dst_num++;
            }
            else
            {
                sf->dst = tmp;
                rtn = 0;
            }
        }
    }

    return rtn;
}

///@brief IEC 61162-450 device source SFI register.
///@param dev450 allocated 450 device for SF.
///@param idx    sf index in the device to sfi setting.
///@param src    source sfi copy character buffer.
STD450_API void std450_sf_get_src(std450_t *dev450, int idx, char *src)
{
    sf_t** sf_group = dev450->sf;
    sf_t *sf = sf_group[idx];

    memcpy(src, &sf->src, C_MAX_SFI_LEN);
}

///@brief IEC 61162-450 device source SFI register.
///@param dev450 allocated 450 device for SF.
///@param idx    sf index in the device to sfi getting.
///@param dst    destination sfi copy character buffer.
STD450_API int std450_sf_get_dst(std450_t *dev450, int idx, char* dst)
{
    int dst_num = 0;
    sf_t** sf_group = dev450->sf;
    sf_t *sf = sf_group[idx];

    dst_num = sf->dst_num;
    memcpy(dst, &sf->dst, (dst_num)*sizeof(sfi_t));

    return dst_num;
}

///@brief IEC 61162-450 device SF setting option set.
///@param dev450 allocated 450 device for SF.
///@param idx    sf index in the device to be write.
///@param ... 
STD450_API int  std450_sf_set_opt(std450_t *dev450, int idx, ...)
{
    if(idx > dev450->sfi_info.sf_count) {
        return 0;
    }

    sf_t** sf_group = dev450->sf;
    sf_t* sf = sf_group[idx];
    va_list p_args;
    va_start(p_args, idx);

    return sf->behavior->_set_opt(sf, (void*)&p_args);
}

///@brief IEC 61162-450 device SF thread process resume (running).
///@param dev450 allocated 450 device for SF.
///@param idx    sf index in the device to be write.
STD450_API void std450_sf_run(std450_t *dev450, int idx)
{
    if(idx > dev450->sfi_info.sf_count) {
        return;
    }

    sf_t** sf_group = dev450->sf;
    sf_t* sf = sf_group[idx];
    pth_proc_resume(&sf->tx_tinfo);
    pth_proc_resume(&sf->rx_tinfo);
}

///@brief IEC 61162-450 device SF thread process stop(pause).
///@param dev450 allocated 450 device for SF.
///@param idx    sf index in the device to be write.
STD450_API void std450_sf_stop(std450_t *dev450, int idx)
{
    if(idx > dev450->sfi_info.sf_count) {
        return;
    }

    sf_t** sf_group = dev450->sf;
    sf_t* sf = sf_group[idx];
    pth_proc_pause(&sf->tx_tinfo);
    pth_proc_pause(&sf->rx_tinfo);
}

///@brief IEC 61162-450 device SF file.
///@param dev450 allocated 450 device for SF.
///@param idx    sf index in the device to be write.
///@param len    send message length. [Not Usinge RaUdP, RrUdP, RrTcP]
///@param msg    send message.
///              Header UdPbC is IEC 61162-1 sentence.
///              Header RaUdP and RrUdP is image data path.
///@return Succes(1), Fail (0)
///@todo  NkPgN and RrTcP define.
STD450_API ssize_t std450_sf_write_msg(std450_t *dev450, int idx, void *msg, int len)
{
    ssize_t size = 0;

    if(idx > dev450->sfi_info.sf_count)
    {
        return 0;
    }

    sf_t** sf_group = dev450->sf;
    sf_t* sf = sf_group[idx];
    size = sf->behavior->_write_msg(sf, msg, len);

    return size;
}

///@brief IEC 61162-450 device SF file descriptor read.
///@param dev450 allocated 450 device for SF.
///@param idx    sf index in the device to be read.
///@param msg    read message.
STD450_API ssize_t std450_sf_read_msg(std450_t *dev450, int idx, void *msg, int len)
{
    ssize_t size = 0;

    if(idx > dev450->sfi_info.sf_count)
    {
        return 0;
    }

    sf_t** sf_group = dev450->sf;
    sf_t* sf = sf_group[idx];

    if(sf->behavior != NULL)
    {
    	size = sf->behavior->_read_msg(sf, msg, len);
    }

    return size;
}
