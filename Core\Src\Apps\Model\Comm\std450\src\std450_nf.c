///@file     std450_nf.c
///@brief    IEC 61162-450 Network Function (NF) block behavior code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#include <stdio.h>
#include <stdlib.h> 
#include <string.h>
#include <sys/stat.h>
#include <errno.h>

#include <user/nic_user.h>

#include <std450.h>
#include <std450_nf.h>
#include <std450_private.h>
#include <std450_nf_private.h>

void nf_srp_rx_stop(void *arg);
void nf_srp_rx_proc(void *arg);
void nf_srp_rx_event(int signum, void *si, void *sv);

void nf_srp_tx_stop(void *arg);
void nf_srp_tx_proc(void *arg);
void nf_srp_tx_event(int signum, void *si, void *sv);

void nf_err_stop(void *arg);
void nf_err_proc(void *arg);
void nf_err_event(int signum, void *si, void *sv);

void nf_drate_stop(void *arg);
void nf_drate_proc(void *arg);
void nf_drate_event(int signum, void *si, void *sv);

///@brief IEC 61162-450 device NF NIC set.
///@param nf IEC 61162-450 device NF.
///@param nic Set using NF NIC name.
///@return Success(0), Error Code(0<)
STD450_API int  _std450_nf_set_nic(nf_t *nf, const char *nic)
{
    int rtn = -1;
    if(nf == NULL)
    {
        return rtn;
    }

    strcpy(nf->nic, nic);
    if(get_nic_count() < 1)
    {
        rtn = -1;
        //DEBUG_LOG_NF("get_nic_count is zero\n");
    }
    else
    {
        //check nic exist.
        if(check_nic(nic) == 0)
        {
            rtn = 0;
        }
        else
        {
            rtn=-2;
            // DEBUG_LOG_NF("check_nic error\n");
        }
    }
    return rtn;
}

///@brief IEC 61162-450 device NF IP set.
///@param nf IEC 61162-450 device NF.
///@param ip Set using NF NIC IP.
STD450_API void _std450_nf_set_ip(nf_t *nf, const char *ip)
{
    if(nf != NULL)
    {
        strcpy(nf->ip, ip);
    }        
}

///@brief IEC 61162-450 device NF datarate get.
///@param nf IEC 61162-450 device NF.
///@param path Error log list save directory path.
///@return Success(1), Fail(0)
STD450_API int _std450_nf_err_list_dir_set(nf_t *nf, char *path)
{
    int rtn = 0;

#if (USE_LINUX == 1)
    if(nf == NULL)
    {
        return rtn;
    }

    DIR*  dir = opendir(path);
    if (dir)                    //Directory exists
    {
        closedir(dir);
        strcpy(nf->err_dir, path);
        rtn = 1;
    } 
    else if (ENOENT == errno)   // Directory does not exist.
    { 
        mkdir(path, 0755);
        if((dir = opendir(path)))
        {
            closedir(dir);
            strcpy(nf->err_dir, path);
            rtn = 1;
        }
        else
        {
            ;//Fail.
        }
    }
    else    // opendir() failed for some other reason. */
    { 
        ;//Fail
    }
#endif
    return rtn;
}

#if (USE_LINUX == 1)
///@brief Error list directory filter.
///@return List in(1), List out(0)
STD450_API int _std450_nf_error_list_filter(struct dirent *info)
{
    int rtn = 0;

    char *ext = NULL;
    ext = strrchr(info->d_name, '.');

    if(ext == NULL)
    {
        return rtn;
    }

    if(strcmp(ext,".log")==0)
    {
        if(strncmp(info->d_name, NF_ERR_LOG_PREFIX, strlen(NF_ERR_LOG_PREFIX)) == 0)
        {
            rtn = 1;
        }
    }

    return rtn;
}
#endif


///@brief IEC 61162-450 device NF error list file set.
///@param nf IEC 61162-450 device NF.
///@return Success(1), Fail(0)
///@todo err_log name setting sequence mode. Now PREFIX_number.
STD450_API int _std450_nf_err_list_file_set(nf_t *nf)
{
    int rtn = 0;

#if (USE_LINUX == 1)
    if(nf == NULL)
    {
        return rtn;
    }
    
    DIR*  dir= opendir(nf->err_dir);
    if(dir)    //file check and file size check.
    { 
        closedir(dir);
        int cnt=0, idx=0;
        struct dirent **filelist;

        if((cnt = scandir(nf->err_dir, &filelist, NULL, alphasort)) == -1)
        {
            ;//scandir error.
        }
        else
        {
            if(cnt > 0)
            {
                int err_log = 0;

                for(idx = 0; idx < cnt; idx++)
                {
                    int idx_pos = strlen(NF_ERR_LOG_PREFIX);
                    char *ptr = &(filelist[idx]->d_name[idx_pos]);
                    sscanf(ptr,"%d.log", &err_log);
                }

                if(err_log <= 0)
                {
                    err_log = 1;
                }
                else
                {
                    err_log += 1;
                }

                snprintf(nf->err_log, NF_ERR_LOG_FILE_LEN, "%s%08d.log", 
                                      NF_ERR_LOG_PREFIX, err_log);

                for(idx = 0; idx < cnt; idx++)
                {
                    free(filelist[idx]);
                }

                rtn = 1;
            }
            free(filelist);
        }
    }
    else
    {
        ;//dirctory not exist. 
    }
#endif
    return rtn;
}

///@brief IEC 61162-450 device NF error count init.
///@param nf IEC 61162-450 device NF.
///@param port SRP port
STD450_API void _std450_nf_err_list_clear(nf_t *nf)
{
    if(nf != NULL) 
    {
        std450_list_free(&nf->err_list);
        std450_list_init(&nf->err_list);
    }
}

///@brief IEC 61162-450 device NF external error log tx IP set.
///@param nf IEC 61162-450 device NF.
///@param ip Errlor log ip.
STD450_API void _std450_nf_err_ip_set(nf_t *nf, const char* ip)
{
    if(nf != NULL)
    {
        memset(nf->err_targ.ip, 0x00, IPADDR_STRLEN_MAX);
        memcpy(nf->err_targ.ip, ip, strlen(ip));
    }
}

///@brief IEC 61162-450 device NF external error log tx port set.
///@param nf IEC 61162-450 device NF.
///@param port Errlor log port.
STD450_API void _std450_nf_err_port_set(nf_t *nf, int port)
{
    if(nf != NULL)
    {
        nf->err_targ.port = port;
    }
}

///@brief IEC 61162-450 device NF error count init.
///@param nf IEC 61162-450 device NF.
///@param port SRP port
///@return Success(1), Fail(0)
STD450_API int _std450_nf_err_socket_open(nf_t *nf)
{
    int rtn = 0;
    if(nf != NULL)
    {
        if(init_sock(&nf->err_targ, SOCK_TYPE_UDP, SOCK_OPT_MCAST) == 0)
        {
            if(set_mcast_sock(&nf->err_targ, nf->ip, 1) == 0)
            {
                rtn = 1;
            }
            else
            {
                free_sock(&nf->err_targ);
            }
        }
    }
    return rtn;
}

///@brief IEC 61162-450 device NF error socket close.
///@param nf IEC 61162-450 device NF.
STD450_API void _std450_nf_err_socket_close(nf_t *nf)
{
    if(nf != NULL)
    {
        free_sock(&nf->err_targ);
    }
}

//==============================================================================
///@brief IEC 61162-450 device  NF SRP Tx/Rx IP set.
///@param nf IEC 61162-450 device NF.
///@param ip SRP ip.
STD450_API void _std450_nf_srp_ip_set(nf_t *nf, const char* ip)
{
    if(nf != NULL)
    {
        memset(nf->srp_targ.ip, 0x00, NI_MAXHOST);
        memcpy(nf->srp_targ.ip, ip, strlen(ip));
    }
}

///@brief IEC 61162-450 device NF SRP Tx/Rx port set.
///@param nf IEC 61162-450 device NF.
///@param port SRP port
STD450_API void _std450_nf_srp_port_set(nf_t *nf, int port)
{
    if(nf != NULL)
    {
        nf->srp_targ.port = port;
    }
}


///@brief IEC 61162-450 device NF SRP Tx linked list init.
///@param nf IEC 61162-450 device NF.
///@param port SRP port
STD450_API void _std450_nf_srp_list_clear(nf_t *nf)
{
    if(nf != NULL)
    {
        std450_list_free(&nf->srp_list);
        std450_list_init(&nf->srp_list);
    }
}

///@brief IEC 61162-450 device NF SRP socket open.
///@param nf IEC 61162-450 device NF.
///@return Success(1), Fail(0)
STD450_API int _std450_nf_srp_socket_open(nf_t *nf)
{
    int rtn = 0;
    if(nf != NULL)
    {
        if(init_sock(&nf->srp_targ, SOCK_TYPE_UDP, SOCK_OPT_MCAST) == 0)
        {
            if(set_mcast_sock(&nf->srp_targ, nf->ip, 1) == 0)
            {
                rtn = 1;
            }
            else
            {
                free_sock(&nf->srp_targ);
            }
        }
    }
    return rtn;
}

///@brief IEC 61162-450 device NF SRP socket close.
///@param nf IEC 61162-450 device NF.
STD450_API void _std450_nf_srp_socket_close(nf_t *nf)
{
    if(nf != NULL)
    {
        free_sock(&nf->srp_targ);
    }
}

//==============================================================================
///@brief IEC 61162-450 device NF datarate data initialize.
///@param nf IEC 61162-450 device NF.
///@param period Datarate calculate period. [sec]
STD450_API void _std450_nf_set_datarate_period(nf_t *nf, int period)
{
    if(nf == NULL)
    {
        return;
    }

    nf->drate_info.datarate = 0;
    nf->drate_info.rate_buf_pos = 0;
    nf->drate_info.rate_period = period;
    // memset(&nf->tx_size, 0x00, MAX_STORE_RATE);
    // memset(&nf->rx_size, 0x00, MAX_STORE_RATE);
}

///@brief IEC 61162-450 device NF datarate get.
///@param nf IEC 61162-450 device NF.
STD450_API int  _std450_nf_datarate_get(nf_t *nf)
{
    return nf->drate_info.datarate;
}

///@brief IEC 61162-450 device NF rx data size add.
///@param nf IEC 61162-450 device NF.
///@param size Add tx size.
STD450_API int  _std450_nf_tx_datasize_add(nf_t *nf, uint32_t size)
{
    int pos = nf->drate_info.rate_buf_pos;
    //pthread_mutex_lock(&nf->drate_info.mutex);
    nf->drate_info.tx_size[pos]+=size;
    //pthread_mutex_unlock(&nf->drate_info.mutex);

    return 0;
}

///@brief IEC 61162-450 device NF rx data size add.
///@param nf IEC 61162-450 device NF.
///@param size Add rx size.
STD450_API int  _std450_nf_rx_datasize_add(nf_t *nf, uint32_t size)
{
    int pos = nf->drate_info.rate_buf_pos;
    //pthread_mutex_lock(&nf->drate_info.mutex);
    nf->drate_info.rx_size[pos]+=size;
    //pthread_mutex_unlock(&nf->drate_info.mutex);

    return 0;
}

//==============================================================================
///@brief IEC 61162-450 device NF error process initialize. 
///       Thread initialize.
///@param nf IEC 61162-450 device NF.
///@todo  pth_proc_thread_init argument change. For send data.
STD450_API void _std450_nf_err_proc_init(nf_t *nf)
{
    nf->err_tinfo = (pthread_info_t*)MEM_MALLOC(sizeof(pthread_info_t));
    memset(nf->err_tinfo, 0x00, sizeof(pthread_info_t));
    if(nf->err_tinfo != NULL)
    {
        nf->err_arg = (pth_args_t*)MEM_MALLOC(sizeof(pth_args_t));
        memset(nf->err_arg, 0x00, sizeof(pth_args_t));
        nf->err_arg->info = nf->err_tinfo;
        nf->err_arg->args = nf;
        pth_proc_thread_init(nf->err_tinfo, nf->err_arg, 
                             nf_err_stop, nf_err_proc, nf_err_event);
    }
}

///@brief IEC 61162-450 device NF error process exit. 
///@param nf IEC 61162-450 device NF.d
STD450_API void _std450_err_proc_exit(nf_t *nf)
{
    if((nf->err_tinfo != NULL)&&(nf->err_arg != NULL))
    {
        pth_proc_thread_exit(nf->err_arg);
        if(nf->err_arg != NULL)
        {
            MEM_FREE(nf->err_arg);
        }
        MEM_FREE(nf->err_tinfo);
    }
}

///@brief IEC 61162-450 device NF error process resume. 
///@param nf IEC 61162-450 device NF.
STD450_API void _std450_err_proc_run(nf_t *nf)
{
    pth_proc_resume(nf->err_tinfo);
}

///@brief IEC 61162-450 device NF error process pause. 
///@param nf IEC 61162-450 device NF.
STD450_API void _std450_err_proc_stop(nf_t *nf)
{
    pth_proc_pause(nf->err_tinfo);
}

//==============================================================================
///@brief IEC 61162-450 device NF srp process initialize. 
///       SRP Tx thread initialize and SRP Rx thread initialize.
///@param nf IEC 61162-450 device NF.
///@todo  pth_proc_thread_init argument change. For send data.
STD450_API void _std450_nf_srp_proc_init(nf_t *nf)
{
    //Rx
    nf->srp_rx_tinfo = (pthread_info_t*)MEM_MALLOC(sizeof(pthread_info_t));
    memset(nf->srp_rx_tinfo, 0x00, sizeof(pthread_info_t));
    if(nf->srp_rx_tinfo != NULL) 
    {
        nf->srp_rx_arg = (pth_args_t*)MEM_MALLOC(sizeof(pth_args_t));
        memset(nf->srp_rx_arg, 0x00, sizeof(pth_args_t));
        nf->srp_rx_arg->info = nf->srp_rx_tinfo;
        nf->srp_rx_arg->args = nf;
        pth_proc_thread_init(nf->srp_rx_tinfo, nf->srp_rx_arg, 
                             nf_srp_rx_stop, nf_srp_rx_proc, nf_srp_rx_event);
        // DEBUG_LOG_NF("[NF:%ld][_std450_nf_srp_proc_init][%ld][%ld][%ld]\n",
        //         nf,
        //         nf->srp_args, 
        //         nf->srp_args->info, 
        //         nf->srp_args->args);
        // DEBUG_LOG_NF("pth_event_set start\n");
    }

    //Tx
    nf->srp_tx_tinfo = (pthread_info_t*)MEM_MALLOC(sizeof(pthread_info_t));
    memset(nf->srp_tx_tinfo, 0x00, sizeof(pthread_info_t));
    if(nf->srp_tx_tinfo != NULL)
    {
        nf->srp_tx_arg = (pth_args_t*)MEM_MALLOC(sizeof(pth_args_t));
        memset(nf->srp_tx_arg, 0x00, sizeof(pth_args_t));
        nf->srp_tx_arg->info = nf->srp_tx_tinfo;
        nf->srp_tx_arg->args = nf;
        pth_proc_thread_init(nf->srp_tx_tinfo, nf->srp_tx_arg, 
                             nf_srp_tx_stop, nf_srp_tx_proc, nf_srp_tx_event);
        // DEBUG_LOG_NF("[NF:%ld][_std450_nf_srp_proc_init][%ld][%ld][%ld]\n",
        //         nf,
        //         nf->srp_args, 
        //         nf->srp_args->info, 
        //         nf->srp_args->args);
        // DEBUG_LOG_NF("pth_event_set start\n");
        pth_proc_tspec_set(nf->srp_tx_tinfo, 1, 0, 0, 0);
        pth_event_signo_set(nf->srp_tx_tinfo, NF_SRP_EVENT_SIG_NO);
    }
}

///@brief IEC 61162-450 device NF srp process exit. 
///@param nf IEC 61162-450 device NF.
STD450_API void _std450_srp_proc_exit(nf_t *nf)
{
    if((nf->srp_rx_tinfo != NULL) && (nf->srp_rx_arg != NULL))
    {
        pth_proc_thread_exit(nf->srp_rx_arg);
        if(nf->srp_rx_arg != NULL)
        {
            nf->srp_rx_arg = NULL;
        }
        MEM_FREE(nf->srp_rx_tinfo);
    }

    if((nf->srp_tx_tinfo != NULL) && (nf->srp_tx_arg != NULL))
    {
        pth_proc_thread_exit(nf->srp_tx_arg);
        if(nf->srp_tx_arg != NULL)
        {
            nf->srp_tx_arg = NULL;
        }
        MEM_FREE(nf->srp_tx_tinfo);
    }
}


///@brief IEC 61162-450 device NF error process exit. 
///@param nf IEC 61162-450 device NF.
STD450_API void _std450_srp_proc_run(nf_t *nf)
{
    pth_proc_resume(nf->srp_rx_tinfo);
    pth_proc_resume(nf->srp_tx_tinfo);
}

///@brief IEC 61162-450 device NF error process exit. 
///@param nf IEC 61162-450 device NF.
STD450_API void _std450_srp_proc_stop(nf_t *nf)
{
    pth_proc_pause(nf->srp_rx_tinfo);
    pth_proc_pause(nf->srp_tx_tinfo);
}

//==============================================================================
///@brief IEC 61162-450 device NF error process initialize. 
///       Thread initialize.
///@param nf IEC 61162-450 device NF.
///@todo  pth_proc_thread_init argument change. For send data.
STD450_API void _std450_nf_drate_proc_init(nf_t *nf)
{
    //nf->drate_tinfo=MEM_MALLOC(sizeof(pthread_info_t));
    nf->drate_tinfo = MEM_MALLOC(sizeof(pthread_info_t));
    memset(nf->drate_tinfo, 0x00, sizeof(pthread_info_t));
    if(nf->drate_tinfo != NULL) 
    {
        nf->drate_arg = (pth_args_t*)MEM_MALLOC(sizeof(pth_args_t));
        memset(nf->drate_arg, 0x00, sizeof(pth_args_t));
        nf->drate_arg->info = nf->drate_tinfo;
        nf->drate_arg->args = nf;
        pth_proc_thread_init(nf->drate_tinfo, nf->drate_arg, nf_drate_stop, 
                             nf_drate_proc, nf_drate_event);
        //DEBUG_LOG_NF("[_std450_nf_error_proc_init][TID]%d\n",nf->err_tinfo->pth_pid);
        pth_proc_tspec_set(nf->drate_tinfo, 1, 0, 1, 0);
        pth_event_signo_set(nf->drate_tinfo, NF_DRATE_EVENT_SIG_NO);
    }
}

///@brief IEC 61162-450 device NF error process exit. 
///@param nf IEC 61162-450 device NF.
STD450_API void _std450_drate_proc_exit(nf_t *nf)
{
    if((nf->drate_tinfo != NULL) && (nf->drate_arg != NULL))
    {
        pth_proc_thread_exit(nf->drate_arg);
        if(nf->drate_arg != NULL)
        {
            MEM_FREE(nf->drate_arg);
        }
        MEM_FREE(nf->drate_tinfo);
    }
}

///@brief IEC 61162-450 device NF error process resume. 
///@param nf IEC 61162-450 device NF.
STD450_API void _std450_drate_proc_run(nf_t *nf)
{
    pth_proc_resume(nf->drate_tinfo);
}

///@brief IEC 61162-450 device NF error process pause. 
///@param nf IEC 61162-450 device NF.
STD450_API void _std450_drate_proc_stop(nf_t *nf)
{
    pth_proc_pause(nf->drate_tinfo);
}

//==============================================================================
///@brief IEC 61162-450 device NF standard initialzie.
///@param nf IEC 61162-450 device NF.
///@return Sucess(1), Fail(0)
STD450_API int  _std450_nf_init(nf_t *nf)
{
    _std450_nf_srp_ip_set(nf, C_SRP_IP);
    _std450_nf_srp_port_set(nf, C_SRP_PORT);
    _std450_nf_srp_list_clear(nf);

    _std450_nf_err_ip_set(nf, C_SYSLOG_IP);
    _std450_nf_err_port_set(nf, C_SYSLOG_PORT);        
    _std450_nf_err_list_clear(nf);
    
    _std450_nf_set_datarate_period(nf, STD_DATARATE_PERIOD);
    _std450_nf_srp_proc_init(nf);
    _std450_nf_err_proc_init(nf);        
    _std450_nf_drate_proc_init(nf);
    return 1;
}

///@brief IEC 61162-450 device NF allocate and initialize setting.\n
///       NF pre-behavior. NIC multicast set. IGMP version set. 
///@param dev450 allocate 450 device for NF.
///@param nic Network Interface Card for 450 networking.
///@param ip  450 networking nic basic ip information seting parameter.
///@param igmp_ver IGMP Version setting parameter
///@return Success(1), Fail(0)
STD450_API int  std450_new_nf(std450_t *dev450, const char *nic, const char *ip,  int igmp_ver)
{
    int rtn=0;
    nf_t *nf = (nf_t*)MEM_MALLOC(sizeof(nf_t));
    memset(nf, 0x00, sizeof(nf_t));

    if(nf != NULL)
    {
        _std450_nf_set_ip(nf, ip);
        if(_std450_nf_set_nic(nf, nic))
        {
            DEBUG_LOG_NF("ERR:std450_new_nf:nic set error\n");
        }
        else
        {
            set_multicast(nic, 0);
            set_igmp_ver(nic, igmp_ver);
            set_multicast(nic, 1);
            if(check_multicast(nic))
            {
                DEBUG_LOG_NF("ERR:std450_new_nf:multicast set error\n");
            }
            else
            {
                _std450_nf_init(nf);
                dev450->nf = nf;
                nf->sfi_info=&dev450->sfi_info;
                rtn = 1;
            }
        }
    }
    return rtn;
}

///@brief IEC 61162-450 device NF free
///@param dev450 allocate 450 device for NF.
STD450_API void std450_free_nf(std450_t *dev450)
{
    nf_t *nf = dev450->nf;
    if(nf != NULL)
    {
        //err_list
        _std450_srp_proc_exit(dev450->nf);
        _std450_err_proc_exit(dev450->nf);
        _std450_drate_proc_exit(dev450->nf);
        MEM_FREE(dev450->nf);
    }
}

///@brief IEC 61162-450 device NF SRP tx/rx address(ip&port) set.
///@param dev450 allocate 450 device for NF.
///@param port SRP port.
///@param ip   SRP ip.
STD450_API int  std450_nf_srp_addr_set(std450_t *dev450, int port, const char *ip)
{
    _std450_nf_srp_ip_set(dev450->nf, C_SRP_IP);
    _std450_nf_srp_port_set(dev450->nf, C_SRP_PORT);
    return 1;
}

///@brief IEC 61162-450 device NF datarate get.
///@param dev450 allocate 450 device for NF.
///@param dir error log list save directory path.
///@return Success(1), Fail(0)
STD450_API int std450_nf_error_list_dir_set(std450_t * dev450, char *dir)
{
    int rtn=0;
    if(_std450_nf_err_list_dir_set(dev450->nf, dir))
    {
        if(_std450_nf_err_list_file_set(dev450->nf))
        {
            ; //list init ok.
        }
    }
    else
    {
        ;// list directory path set error.
    }
    return rtn;
}

///@brief IEC 61162-450 device NF external error log tx address(ip&port) set.
///@param dev450 allocate 450 device for NF.
///@param port Errlor log port.
///@param ip   Errlor log ip.
///@return     Success(1) Fail(0)
STD450_API int  std450_nf_error_addr_set(std450_t *dev450, int port, const char *ip)
{
    _std450_nf_err_ip_set(dev450->nf, C_SYSLOG_IP);
    _std450_nf_err_port_set(dev450->nf, C_SYSLOG_PORT);
    return 1;
}

///@brief IEC 61162-450 device NF datarate inforation initialize.
///@param dev450 allocate 450 device for NF.
///@param period Datarate calculate period. [sec]
STD450_API void std450_nf_datarate_init(std450_t *dev450, int period)
{
    _std450_nf_set_datarate_period(dev450->nf, period);
}

///@brief IEC 61162-450 device NF datarate get.
///@param dev450 allocate 450 device for NF.
STD450_API int  std450_nf_datarate_get(std450_t *dev450)
{
    return _std450_nf_datarate_get(dev450->nf);
}

///@brief IEC 61162-450 device NF tx data size add.
///@param dev450 allocate 450 device for NF.
///@param size Add tx data size.
STD450_API void std450_nf_tx_datasize_add(std450_t *dev450, uint32_t size)
{
    _std450_nf_tx_datasize_add(dev450->nf, size);
}

///@brief IEC 61162-450 device NF rx data size add.
///@param dev450 allocate 450 device for NF.
///@param size Add rx data size.
STD450_API void std450_nf_rx_datasize_add(std450_t *dev450, uint32_t size)
{
    _std450_nf_rx_datasize_add(dev450->nf, size);
}

///@brief IEC 61162-450 device NF network socket init & open.
///@param dev450 allocate 450 device for NF.
///@return Success(0) Fail(-1,-2,-3)
STD450_API int  std450_nf_sock_open(std450_t *dev450)
{
    int rtn = 0, step=0;

    if(_std450_nf_err_socket_open(dev450->nf) == 0)
    {
        rtn |= 1<<(step);
    }

    step++;
    if(_std450_nf_srp_socket_open(dev450->nf) == 0)
    {
        rtn |= 1<<step;
    }
    return rtn;
}

///@brief IEC 61162-450 device NF network socket close
///@param dev450 allocate 450 device for NF.
STD450_API void std450_nf_sock_close(std450_t *dev450)
{
    _std450_nf_err_socket_close(dev450->nf);
    _std450_nf_srp_socket_close(dev450->nf);
}

///@brief IEC 61162-450 device NF error process thread running.
///@param dev450 allocate 450 device for NF.
STD450_API void std450_nf_proc_run(std450_t *dev450)
{
    _std450_err_proc_run(dev450->nf);
    _std450_srp_proc_run(dev450->nf);
    _std450_drate_proc_run(dev450->nf);
}

///@brief IEC 61162-450 device NF srp process thread running.
///@param dev450 allocate 450 device for NF.
STD450_API void std450_nf_proc_stop(std450_t *dev450)
{
    _std450_err_proc_stop(dev450->nf);
    _std450_srp_proc_stop(dev450->nf);
    _std450_drate_proc_stop(dev450->nf);
}

//================================================================================
// Error Linked List Input and Get Function.
//================================================================================

///@brief Error data list node.
///@param size error data size.
///@todo  error data inform add.
struct err_list_data_s {
    uint32_t size;
};

typedef struct err_list_data_s err_list_data_t;

///@brief Rule about error node push function.
///@param src Push node data.
///@todo  Push error data struct and process add.
void *err_list_func_push(void *src)
{
    err_list_data_t *s = (err_list_data_t *)src;
    err_list_data_t *d = NULL;

    if(s->size > 0)
    {
        d = (err_list_data_t*)MEM_MALLOC(sizeof(err_list_data_t));
        if(d != NULL)
        {
            d->size = s->size;
            //memcpy(d->talker, s->talker, s->size);
        }
    }
    return (void*)d;
}

///@brief Rule about error node push function 
///@param src Node in list.
///@param dst Pop node pointer address.
///@todo  Pop error data struct and process add.
void err_list_func_pop(void* dst, void *src)
{
    err_list_data_t *s = (err_list_data_t *)src;
	err_list_data_t *d = (err_list_data_t *)dst;

    if((dst == NULL) || (src == NULL))
    {
        return;
    }

    if(s->size > 0)
    {
        d->size = s->size;
        //memcpy(d->talker, s->talker, s->size);
        //DEBUG_LOG_NF("srp_list_func_pop\n");
    }
    // if(s->data!=NULL) {
    //         MEM_FREE(s->data);
    //         s->data = NULL;
    // }
}

///@brief Data push at SRP list.
///@param list List.
///@param src Push data address.
void err_list_push(std450_list_t *list, err_list_data_t *src)
{
    if((list == NULL) || (src == NULL))
    {
        return;
    }

    std450_list_set(list, (void*) src);
}

///@brief Data pop at SRP list.
///@param list List.
///@param dst Pop data address.
void *err_list_pop(std450_list_t *list)
{
    if(list == NULL)
    {
        return NULL;
    }

    return std450_list_get(list);
}


///@brief NF error message push for using SF.
///@todo  NF error message recevie at SF device and copy msg.
STD450_API void _std450_nf_err_msg(nf_t *nf, char* msg)
{
    //err_list_push(nf->err_list, msg);
}


//================================================================================
// Error list operation thread process.
//================================================================================
///@brief IEC 61162-450 device NF Error list process thread Stop.
///@param arg thread info and NF pointer.
void nf_err_stop(void *arg)
{
    DEBUG_LOG_NF("nf_err_stop\n");
}

///@brief IEC 61162-450 device Error list Event Process. [Not Use]
///@param signum Event Signal.
///@param si Event arguemnt.
///@param sv Not use. For Kernel.
void nf_err_event(int signum, void *si, void *sv)
{
    //Not use function.
}

///@brief IEC 61162-450 device NF Error list process thread running.
///@param arg thread info and NF pointer.
///@details If SRP requests from the outside, linked list set.
void nf_err_proc(void *arg)
{
    pth_args_t* args = arg;
    nf_t* nf_args = args->args;

    /// Error list get data.
    /// Error list file write.
    /// Error list write socket.
    if(std450_list_size(&nf_args->err_list) > 0)
    {
        err_list_data_t *err_data = err_list_pop(&nf_args->err_list);
        //write_tx
        if (err_data)
        {
            MEM_FREE(err_data);
        }
    }
}

//================================================================================
// SRP Linked List Input and Get Function.
//================================================================================
struct srp_list_data_s {
    uint32_t size;
    uint8_t  talker[2];
};

typedef struct srp_list_data_s srp_list_data_t;

///@brief Data push at SRP list.
///@param list List.
///@param src Push data address.
void srp_list_push(std450_list_t *list, srp_list_data_t *src)
{
    if((list == NULL) || (src == NULL))
    {
        return;
    }

    std450_list_set(list, (void*) src);
}

///@brief Data pop at SRP list.
///@param list List.
///@param dst Pop data address.
void *srp_list_pop(std450_list_t *list)
{
    if(list == NULL)
    {
        return NULL;
    }
       
    return std450_list_get(list);
}

//================================================================================
// SRP (Rx/Tx) operation thread process.
//================================================================================
///@brief IEC 61162-450 device SRP Rx Event Process. [Not Use]
///@param signum Event Signal.
///@param si Event arguemnt.
///@param sv Not use. For Kernel.
void nf_srp_rx_event(int signum, void *si, void *sv)
{
    ;//Not use function.
}

///@brief IEC 61162-450 device NF SRP Rx process thread Stop.
///@param arg thread info and NF pointer.
void nf_srp_rx_stop(void *arg)
{
    DEBUG_LOG_NF("nf_srp_rx_stop\n");
}

///@brief IEC 61162-450 device NF SRP Rx process thread Stop.
///@param data Passing SRP data
///@param msg  Receive data. 
///@param len  Receive data length.
///@return SRP Data Receive TRUE(1), FALSE(0)
int nf_srp_rx_procedure (srp_list_data_t *data, char *msg, int len)
{
    int rtn=0;
    ///
    ///
    return rtn;
}


///@brief IEC 61162-450 device NF SRP Rx process thread running.
///@param arg thread info and NF pointer.
///@details If SRP requests from the outside, linked list set.
void nf_srp_rx_proc(void *arg)
{
    pth_args_t* args = arg;
    nf_t* nf_args = args->args;
    char msg[256]="";
    int msg_len = 256;
    msg_len = read_sock(&nf_args->srp_targ, msg, 256, 100);

    if(msg_len > 0)
    {
        srp_list_data_t *srp_data=(srp_list_data_t *)MEM_MALLOC(sizeof(srp_list_data_t));
        memset(srp_data, 0x00, sizeof(srp_list_data_t));

        if(nf_srp_rx_procedure(srp_data, msg, msg_len))
        {
            srp_list_push(&nf_args->srp_list, srp_data);
        } 
        else 
        {
            //err_list_push();
        }
        MEM_FREE(srp_data);
    }
}

///@brief IEC 61162-450 device NF SRP Tx process thread Stop.
///@param arg thread info and NF pointer.
void nf_srp_tx_stop(void *arg)
{
    DEBUG_LOG_NF("nf_srp_tx_stop\n");
    //linked list delete.
}

///@brief IEC 61162-450 device SRP Tx Event Process. 
///@param signum Event Signal.
///@param si Event arguemnt.
///@param sv Not use. For Kernel.
///@todo  Decide srp list thread condiont signal send.
void nf_srp_tx_event(int signum, void *si, void *sv)
{
/*
    pth_args_t* args = si->si_value.sival_ptr;
    pthread_info_t *info = args->info;
    nf_t* nf_args = args->args;
    srp_list_data_t srp_data;

    memset(&srp_data, 0x00, sizeof(srp_list_data_t));        

    srp_data.size = 2;
    srp_data.talker[0]=0x00;
    srp_data.talker[1]=0x00;
    // DEBUG_LOG_NF("[%d]nf_srp_evnet:%ld[%ld][%ld]\n", nf_args->srp_cnt,
    //         args, args->info, nf_args);
    if(nf_args->srp_ev_cnt==0) 
    {
        nf_args->srp_ev_cnt=1;
        pth_proc_tspec_set(info, 60, 0, 0, 0); //after 60 sec event.
        pth_event_reset(info, args);
        srp_list_push(nf_args->srp_list, &srp_data);
        //std450_list_semapore_cond_signal(nf_args->srp_list);
    }
    else if(nf_args->srp_ev_cnt==1)
    {
        nf_args->srp_ev_cnt=2;
        pth_proc_tspec_set(info, 300, 0, 0, 0); //after 300 sec event.
        pth_event_reset(info, args);
        srp_list_push(nf_args->srp_list, &srp_data);
        //std450_list_semapore_cond_signal(nf_args->srp_list);
    }
    else
    { //clear event.
        pth_event_clear(info);
        srp_list_push(nf_args->srp_list, &srp_data);
        //std450_list_semapore_cond_signal(nf_args->srp_list);
    }
*/
}

///@brief IEC 61162-450 device NF SRP Tx process thread running.
///@param arg thread info and NF pointer.
///@details This function is a process that receives changes in the linked list as signals \n
///         and transmits them to the outside.
///@todo    SRP Write sentence process.
void nf_srp_tx_proc(void *arg)
{
    pth_args_t* args = arg;
    nf_t* nf_args = args->args;

    if(std450_list_size(&nf_args->srp_list) > 0)
    {
        srp_list_data_t *srp_data = srp_list_pop(&nf_args->srp_list);
        //write_tx

        if (srp_data)
        {
            MEM_FREE(srp_data);
        }
    }
}

//================================================================================
// Datarate operation thread process.
//================================================================================

///@brief IEC 61162-450 device NF data rate process thread Stop.
///@param arg thread info and NF pointer.
void nf_drate_stop(void *arg)
{
    DEBUG_LOG_NF("nf_drate_stop\n");
}

///@brief IEC 61162-450 device data rate calcurate event. Per 1's calc.
///
///@param signum Event Signal.
///@param si Event arguemnt.
///@param sv Not use. For Kernel.
void nf_drate_event(int signum, void *si, void *sv)
{
/*
    pth_args_t* args = si->si_value.sival_ptr;
    pthread_info_t *info = args->info;
    nf_t* nf_args = args->args;
    int idx =0;
    double drate = 0.0;
    uint8_t tmp_buf_pos =0;
    uint32_t total_data = 0;
    uint32_t total_tx =0;
    uint32_t total_rx =0;
    uint32_t period = nf_args->drate_info.rate_period;
    uint32_t *tx_dsize=(uint32_t *)MEM_MALLOC(period+1,sizeof(uint32_t));
    uint32_t *rx_dsize=(uint32_t *)MEM_MALLOC(period+1,sizeof(uint32_t));
    uint32_t cp_size = (period+1)*sizeof(uint32_t);

    pthread_mutex_lock(&nf_args->drate_info.mutex); //mutex lock;
    if(nf_args->drate_info.rate_buf_pos<period)
    {
        tmp_buf_pos = nf_args->drate_info.rate_buf_pos;
        nf_args->drate_info.rate_buf_pos=tmp_buf_pos+1;
    }
    else
    {
        nf_args->drate_info.rate_buf_pos=0;
    }
    memcpy(tx_dsize, &nf_args->drate_info.tx_size, cp_size);
    memcpy(rx_dsize, &nf_args->drate_info.rx_size, cp_size);
    pthread_mutex_unlock(&nf_args->drate_info.mutex); //mutex unlock;
    for(idx =0; idx<=period; idx++)
    {
        if(nf_args->drate_info.rate_buf_pos == idx)
        {
            continue;
        }
        total_tx+=tx_dsize[idx];
        total_rx+=rx_dsize[idx];
    }

    total_data = total_tx+total_rx;
    drate = (double)(total_data/period);
    nf_args->drate_info.datarate = (uint32_t)drate;
    MEM_FREE(tx_dsize);
    MEM_FREE(rx_dsize);        
    //DEBUG_LOG_NF("nf_drate_event[%d][%d][%d][%d]\n\n",nf_args->drate_info.rate_buf_pos, nf_args->drate_info.datarate);
*/
}

///@brief IEC 61162-450 device NF Error list process thread running.
///@param arg thread info and NF pointer.
///@details Data rate calcurate per 1 second. 
void nf_drate_proc(void *arg)
{
    //pth_args_t* args = arg;
    //pthread_info_t *info = args->info;
    //nf_t* nf_args = args->args;
}
