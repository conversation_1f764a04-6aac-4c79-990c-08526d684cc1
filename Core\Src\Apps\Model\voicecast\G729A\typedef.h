/*
  ITU-T G.729A Speech Coder with Annex B    ANSI-C Source Code
*/

/*
----------------------------------------------------------------------
                    COPYRIGHT NOTICE
----------------------------------------------------------------------
   ITU-T G.729 Annex C ANSI C source code
   Copyright (C) 1998, AT&T, France Telecom, NTT, Universite of
   Sherbrooke.  All rights reserved.

----------------------------------------------------------------------
*/

/*
   Types definitions
*/
#define __HIGHC__
#if defined(__BORLANDC__) || defined (__WATCOMC__) || defined(_MSC_VER) || defined(__ZTC__) || defined(__HIGHC__)
typedef  long  int   INT32;
typedef  short int   INT16;
#elif defined (__sun__) || defined (__sun)
typedef short  INT16;
typedef long  INT32;
#elif defined(__alpha)
typedef short INT16;
typedef int   INT32;
#elif defined(VMS) || defined(__VMS) || defined(VAX)
typedef short  INT16;
typedef long  INT32;
#elif defined (__unix__) || defined (__unix)
typedef short INT16;
typedef int   INT32;
#else
#error  COMPILER NOT TESTED typedef.h needs to be updated, see readme
#endif

#define single
#if defined (_single) || defined (single)
typedef  float  FLOAT;
#else
typedef  double  FLOAT;
#endif
#define F   FLOAT
