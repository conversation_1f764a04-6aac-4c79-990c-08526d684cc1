/**
******************************************************************************
* @file      system_main.c
* <AUTHOR>
* @date      2023-09-14
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "Common.h"
#include "model.h"
#include "TargetBoard.h"

// void Msi_Event_Count_Update(void)
// {
//     static u16 preCntNew = 0xFFFF;
//     u16 i = 0;
//     u16 cntNew = 0;
//     u8 sysMode = 0;

//     sysMode = g_hSysStatus.m_pStat->mode.system;
//     if(sysMode != SYS_BOOT_RUN)
//     {
//         return;
//     }
// }

void System_Demo_Task(void)
{

}
