///@file     std450_nf_private.h
///@brief    450 internal NF private code header.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#ifndef STD450_NF_PRIVATE_H
#define STD450_NF_PRIVATE_H

#include <std450_common.h>
#include <user/net_user.h>
#include <user/std450_data.h>
#include <user/pthread_user.h>

STD450_BEGIN_DECLS

#define NF_ERR_LOG_FILE_LEN   (32) /// Error log file name max. length.
#define NF_MAX_DATA_RATE_BUF  (64) /// Datarate buffer index. (1 sec shift)
#define NF_MAX_AVG_TIME       (60) /// Datarate max. avg. time.

/// @brief NF Data rate informaiont struct.
struct nf_drate_s {
    uint8_t rate_buf_pos;                   /// Datarate store buffer position.
    uint8_t rate_period;                    /// Datarate period.
    uint32_t tx_size[NF_MAX_DATA_RATE_BUF]; /// Network per 1 second total transmisson count.
    uint32_t rx_size[NF_MAX_DATA_RATE_BUF]; /// Network per 1 second total recevie count.
    uint32_t datarate;      /// Last check time(period) datarate. [4.3.2 Maximum data rate requirements]

#if (USE_LINUX == 1)
    pthread_mutex_t mutex;	///< Pthread mutex.
    pthread_cond_t cv;	    ///< Pthread condition.
#else
    sys_sem_t   sem;
    sys_mutex_t mutex;
#endif
};

typedef struct nf_drate_s nf_drate_t;

/// @brief Network function struct. IEC 61162-450 4.3 NF requirements
struct nf_s {
        char nic[16];                       /// NIC name.
        char ip[NI_MAXHOST];         /// NIC IP address.
        uint8_t  srp_ev_cnt;                /// SRP initialize count. [0, 60, 300]
        nf_drate_t drate_info;              /// Data rate inoramtion.     
        net_targ_t srp_targ;                /// SRP UDP socket & address configration. [7.5.2 Transmitter function.]
        net_targ_t err_targ;                /// External error UDP socket & address configration. [******* External loggin.]
        sfi_info_t* sfi_info;               /// SFI information pointer address.
        char err_log[NF_ERR_LOG_FILE_LEN]; /// Error logging file name.
        char err_dir[NF_ERR_LOG_PATH_LEN]; /// Error logging direcotry full path.
        std450_list_t err_list; /// Error log message list.
        std450_list_t srp_list; /// SRP Tx Request list.
        pthread_info_t* srp_tx_tinfo; /// SRP thread information.
        pthread_info_t* srp_rx_tinfo; /// SRP thread information.
        pthread_info_t* err_tinfo; /// Error thread information. 
        pthread_info_t* drate_tinfo; /// Data rate information.
        pth_args_t *srp_tx_arg;         /// SRP thread argumnet pointer.
        pth_args_t *srp_rx_arg;         /// SRP thread argumnet pointer.
        pth_args_t *err_arg;         /// Error thread argumnet pointer.
        pth_args_t *drate_arg;       /// Date thread argumnet pointer.
};

typedef struct nf_s nf_t;       ///Typedef declaration nf

STD450_END_DECLS

#endif  /* STD450_NF_PRIVATE_H */
