#ifndef _rtp_packet_h_
#define _rtp_packet_h_

#include "rtp-header.h"

#define RTP_MAX_CSRC 		4
#define RTP_CSRC_LEN 		4
#define RTP_EXT_LEN 		4

#define RTP_FIXED_HEADER  	12
#define RTP_FIXED_PAYLOAD 	10
#define RTP_FIXED_CSRC    	(RTP_MAX_CSRC*RTP_CSRC_LEN)
#define RTP_FIXED_SERIAL  	(RTP_FIXED_HEADER+RTP_FIXED_CSRC+RTP_EXT_LEN+RTP_FIXED_PAYLOAD)

struct rtp_packet_t
{
	rtp_header_t rtp;							// RTP header
	uint32_t     csrc[RTP_FIXED_CSRC];			// CSRC list	
	const void*  extension; 					// extension(valid only if rtp.x = 1)
	uint16_t     extlen;       					// extension length in bytes
	uint16_t     extprofile;   					// extension reserved
	uint8_t      payload[RTP_FIXED_PAYLOAD]; 	// payload
	int          payloadlen;        			// payload length in bytes
	uint8_t      serialbuf[RTP_FIXED_SERIAL]; 	// serialized header + payload
	int          serialbuflen;     				// serialized length in byte
};

void* rtp_pack_create(uint8_t pt, uint16_t seq, uint32_t ssrc);
void rtp_pack_destroy(void* p);
void rtp_pack_get_info(void* p, uint16_t* seq, uint32_t* timestamp);
int rtp_pack_input(void* p, const void* data, int bytes);

///@return 0-ok, other-error
int rtp_packet_deserialize(struct rtp_packet_t *pkt, const void* data, int bytes);

///@return <0-error, >0-rtp packet size, =0-impossible
int rtp_packet_serialize(const struct rtp_packet_t *pkt, void* data, int bytes);

#endif /* !_rtp_packet_h_ */
