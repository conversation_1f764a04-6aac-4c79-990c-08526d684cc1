﻿/**
 * @file      Coef300HzFirHP.h
 * <AUTHOR>
 * @brief     VHF Main Function
 * @version   0.1
 * @date      2022-08-31
 * 
 * @copyright Copyright (c) 2022
 * 
 */


#if !defined(__Coef3000HzFirLP_H__)
#define      __Coef3000HzFirLP_H__

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "AllConst.h"
#include "CommonLib.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  FLT_3000HZ_FIR_LP_SIZE        (31)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

#if defined(__cplusplus)
extern "C" {
#endif   // __cplusplus


//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
extern tBaseFIR    G_xFirTx3000HzLP;
extern tBaseFIR    G_xFirRx3000HzLP;
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

#if defined(__cplusplus)
           }
#endif   // __cplusplus


#endif   // __Coef3000HzFirLP_H__
