/*
 * LowPassFilter.c
 *
 *  Created on: Sep 8, 2024
 *      Author: jay.yang
 */


#include "audio_bc.h"
#include "Coef3000HzFirLP.h"

volatile xDWSX  G_vDwsRunData;
volatile bool   G_bIsDataReady = false;

void audio_bc_create(void)
{
    // Initialize FIR filter(3KHz LPF)
    MakeInitBaseFIR(&G_xFirRx3000HzLP , NULL, 0);

    // Allocate downsampling buffer
    for (int i = 0; i < DWSBUFFCNT; i++)
    {
        G_vDwsRunData.pData[i] = (uint16_t *)calloc(DWSBUFFSIZE, sizeof(uint16_t));
    }

    audio_bc_reset();
    G_bIsDataReady = true;
}

void audio_bc_destroy(void)
{
    for (int i = 0; i < DWSBUFFCNT; i++)
    {
        if (G_vDwsRunData.pData[i] != NULL)
        {
            free(G_vDwsRunData.pData[i]);
            G_vDwsRunData.pData[i] = NULL;
        }
    }
    G_bIsDataReady = false;
}

void audio_bc_reset(void)
{
    G_vDwsRunData.fSmplAvr = 0;
    G_vDwsRunData.dSmplCnt = 0;
    G_vDwsRunData.nHead = 0;
    G_vDwsRunData.nTail = 0;

    for (int i = 0; i < DWSBUFFCNT; i++)
    {
        memset(G_vDwsRunData.pData[i], 0, DWSBUFFSIZE * sizeof(uint16_t));
    }
}

inline uint16_t audio_bc_lpf_apply(uint16_t fIn)
{
    if (!G_bIsDataReady)
    {
        return fIn;
    }

    float fTempX = CodecIntDataToFloat(fIn);
    fTempX = G_xFirRx3000HzLP.pFuncP(&G_xFirRx3000HzLP, fTempX);   // LPF(3000Hz)
    audio_bc_dwn_smpl(fTempX);
    return CodecFloatToIntData(fTempX);
}

inline void audio_bc_dwn_smpl(float fIn)
{
    G_vDwsRunData.fSmplAvr += fIn;

    G_vDwsRunData.dSmplCnt++;
    if (G_vDwsRunData.dSmplCnt >= DECIMATE_FACTOR)
    {
        G_vDwsRunData.pData[G_vDwsRunData.nHead][G_vDwsRunData.nPosX] = CodecScaledFloatToIntData(G_vDwsRunData.fSmplAvr / DECIMATE_FACTOR);
        G_vDwsRunData.fSmplAvr = 0;
        G_vDwsRunData.dSmplCnt = 0;

        G_vDwsRunData.nPosX++;
        if (G_vDwsRunData.nPosX >= DWSBUFFSIZE)
        {
            G_vDwsRunData.nPosX = 0;
            G_vDwsRunData.nHead++;
            if (G_vDwsRunData.nHead >= DWSBUFFCNT)
            {
                G_vDwsRunData.nHead = 0;
            }
        }
    }
}

inline bool audio_bc_is_data_ready(void)
{
    return G_vDwsRunData.nHead != G_vDwsRunData.nTail;
}

inline uint16_t *audio_bc_get_data(void)
{
    if (!G_bIsDataReady)
    {
        return NULL;
    }

    uint16_t *pData = G_vDwsRunData.pData[G_vDwsRunData.nTail];

    G_vDwsRunData.nTail++;
    if (G_vDwsRunData.nTail >= DWSBUFFCNT)
    {
        G_vDwsRunData.nTail = 0;
    }

    return pData;
}

inline float CodecIntDataToFloat(int16_t nData)
{
	return CODEC_DATA_INT_TO_FLOAT*nData;
}

inline int16_t CodecFloatToIntData(float fData)
{
	int tmp = CODEC_DATA_FLOAT_TO_INT * fData;

	if(tmp >= I2S_AUDIO_DATA_MAX)
	{
		tmp = I2S_AUDIO_DATA_MAX;
	}
	else if(tmp <= I2S_AUDIO_DATA_MIN)
	{
		tmp = I2S_AUDIO_DATA_MIN;
	}

	return tmp;
}

inline int16_t CodecScaledFloatToIntData(float fData)
{
	int tmp = CODEC_OUT_SCALED_FACTOR * fData;

	if(tmp >= I2S_AUDIO_DATA_MAX)
	{
		tmp = I2S_AUDIO_DATA_MAX;
	}
	else if(tmp <= I2S_AUDIO_DATA_MIN)
	{
		tmp = I2S_AUDIO_DATA_MIN;
	}

	return tmp;
}
