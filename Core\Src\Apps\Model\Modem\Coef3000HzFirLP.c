﻿/**
 * @file      Coef300HzFirHP.c
 * <AUTHOR>
 * @brief     VHF Main Function
 * @version   0.1
 * @date      2022-08-31
 *
 * @copyright Copyright (c) 2022
 *
 */


//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#include "AllConst.h"
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
/**************************************************************
Filter type: Low Pass
Filter model: Raised Cosine
Roll Off Factor: 0.500000
Sampling Frequency: 48 KHz
Cut Frequency: 3.000000 KHz
Coefficents Quantization: float
***************************************************************/
static const float32_t vCoeffH[FLT_3000HZ_FIR_LP_SIZE + 1]  = {
-0.0025107214,-0.0067228351,-0.0113347268,-0.0153577958,-0.0175619151,-0.0166629585,-0.0115594172,-0.0015760290,
 0.0133346715, 0.0324788681, 0.0544285888, 0.0771603931, 0.0983109973, 0.1155110143, 0.1267404532, 0.1306428250,
 0.1267404532, 0.1155110143, 0.0983109973, 0.0771603931, 0.0544285888, 0.0324788681, 0.0133346715,-0.0015760290,
-0.0115594172,-0.0166629585,-0.0175619151,-0.0153577958,-0.0113347268,-0.0067228351,-0.0025107214
};
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
tBaseFIR    G_xFirTx3000HzLP = 
          {
            .dSizeB = FLT_3000HZ_FIR_LP_SIZE,
            .dDataP = 0,

            .pCoefH = (float32_t *)vCoeffH,
            .pDataB = NULL,
            .pAddrF = NULL,
            .pAddrL = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataFIR
          };
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
tBaseFIR    G_xFirRx3000HzLP = 
          {
            .dSizeB = FLT_3000HZ_FIR_LP_SIZE,
            .dDataP = 0,

            .pCoefH = (float32_t *)vCoeffH,
            .pDataB = NULL,
            .pAddrF = NULL,
            .pAddrL = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataFIR
          };
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

