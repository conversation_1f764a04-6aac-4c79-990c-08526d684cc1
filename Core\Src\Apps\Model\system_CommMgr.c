/**
******************************************************************************
* @file      system_CommMgr.c
* <AUTHOR>
* @date      2025-07-17
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2025 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "model.h"
#include "TargetBoard.h"

void system_CommMgr_Init(void)
{

}

void system_CommMgr_Task(void)
{
    int status = 0;

    status = Get_Rx_Icomm_Handshake_Status(I_COMM_RX_COMM_CHECK);
    if(status == COMM_STATE_TIMEOUT)
    {
        TBD_init_setting_uarts(TARGET_UART_NAVTEX_CONTROL, TARGET_BAUD_UART_NAVTEX_CONTROL, 0);
        Reset_Rx_Icomm_Handshake_Status(I_COMM_RX_COMM_CHECK, 5000);
        DEBUG_MSG("[ERROR] Init Internal Comm... \r\n");
    }
    else if(status == COMM_STATE_SET)
    {
        Reset_Rx_Icomm_Handshake_Status(I_COMM_RX_COMM_CHECK, 5000);        
    }

    Internal_Tx_Parameter_State_Update();
}