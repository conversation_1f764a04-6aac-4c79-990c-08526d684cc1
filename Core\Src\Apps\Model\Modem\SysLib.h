﻿/**
 * @file      SysLib.h
 * <AUTHOR>
 * @brief     시스템에서 많이 사용되는 함수 라이브러리
 * @version   0.1
 * @date      2022-08-11
 * 
 * @copyright Copyright (c) 2022
 * 
 */


#if !defined(__SysLib_H__)
#define      __SysLib_H__

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "AllConst.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

#if defined(__cplusplus)
extern "C" {
#endif   // __cplusplus

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
void     SysRunDelayLoop(volatile uint32_t dLoopCnt);
void     SysRunDelayMiliSec(uint32_t dDelayMili);
void     SysRunDelayMicroSec(uint32_t dDelayMicro);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
uint32_t SysCalcTickToMili(uint32_t dTick);
uint32_t SysCalcMiliToTick(uint32_t dMili);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
uint32_t SysCalcDiffTimeTick(uint32_t dTick);
uint32_t SysCalcDiffTimeMili(uint32_t dTick);
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

#if defined(__cplusplus)
           }
#endif   // __cplusplus


#endif   // __SysLib_H__
