/**
  ******************************************************************************
  * @file    LwIP/LwIP_TCP_Echo_Server/Inc/app_ethernet.h 
  * <AUTHOR> Application Team
  * @brief   Header for app_ethernet.c module
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __APP_ETHERNET_H
#define __APP_ETHERNET_H

#ifdef __cplusplus
 extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "Common.h"
#include "lwip/netif.h"

// Ip address
#define IP_ADDR_A	((uint8_t) 192U)
#define IP_ADDR_B	((uint8_t) 168U)
#define IP_ADDR_C	((uint8_t) 0U)
#define IP_ADDR_D	((uint8_t) 10U)

// Netmask
#define NETMASK_ADDR_A	((uint8_t) 255U)
#define NETMASK_ADDR_B	((uint8_t) 255U)
#define NETMASK_ADDR_C	((uint8_t) 255U)
#define NETMASK_ADDR_D	((uint8_t) 0U)

// GateWay
#define GATEWAY_ADDR_A	((uint8_t) 192U)
#define GATEWAY_ADDR_B	((uint8_t) 168U)
#define GATEWAY_ADDR_C	((uint8_t) 0U)
#define GATEWAY_ADDR_D	((uint8_t) 1U)

#define GEN_IP4_ADDR(a,b,c,d) PP_HTONL(LWIP_MAKEU32(a,b,c,d))

//#define ENABLE_TCP_TEST


/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* DHCP process states */
#if LWIP_DHCP

typedef enum{
    DHCP_OFF,
    DHCP_START,
    DHCP_WAIT_ADDRESS,
    DHCP_ADDRESS_ASSIGNED,
    DHCP_TIMEOUT,
    DHCP_LINK_DOWN,
}DHCP_State_t;

void DHCP_Thread(void const * argument);

void Set_DHCP_State(int state);
int Get_DHCP_State(void);
void Set_address_assinged(int state);
int Get_address_assinged(void);
#endif
void Std450_Recv_Task(void);

/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
void ethernet_link_status_updated(struct netif *netif);
void Netif_Config(void);
void Set_Static_Network(int *p_ip, int *p_netmask, int *p_gateway);

#ifdef __cplusplus
}
#endif

#endif /* __APP_ETHERNET_H */





