﻿/**
 * @file      AscConst.h
 * <AUTHOR>
 * @brief     전역으로 사용되는 Ascii 코드 정의
 * @version   0.1
 * @date      2022-08-11
 * 
 * @copyright Copyright (c) 2022
 * 
 */


#if !defined(__AscConst_H__)
#define      __AscConst_H__

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#define  ASC_CHR_NUL                   (0x00)
#define  ASC_CHR_SOH                   (0x01)
#define  ASC_CHR_STX                   (0x02)
#define  ASC_CHR_ETX                   (0x03)
#define  ASC_CHR_EOT                   (0x04)
#define  ASC_CHR_ENQ                   (0x05)
#define  ASC_CHR_ACK                   (0x06)
#define  ASC_CHR_BEL                   (0x07)
#define  ASC_CHR_BS                    (0x08)
#define  ASC_CHR_HT                    (0x09)
#define  ASC_CHR_LF                    (0x0a)
#define  ASC_CHR_VT                    (0x0b)
#define  ASC_CHR_FF                    (0x0c)
#define  ASC_CHR_CR                    (0x0d)
#define  ASC_CHR_SO                    (0x0e)
#define  ASC_CHR_SI                    (0x0f)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  ASC_CHR_DLE                   (0x10)
#define  ASC_CHR_DC1                   (0x11)
#define  ASC_CHR_DC2                   (0x12)
#define  ASC_CHR_DC3                   (0x13)
#define  ASC_CHR_DC4                   (0x14)
#define  ASC_CHR_NAK                   (0x15)
#define  ASC_CHR_SYN                   (0x16)
#define  ASC_CHR_ETB                   (0x17)
#define  ASC_CHR_CAN                   (0x18)
#define  ASC_CHR_EM	                   (0x19)
#define  ASC_CHR_SUB                   (0x1a)
#define  ASC_CHR_ESC                   (0x1b)
#define  ASC_CHR_FS	                   (0x1c)
#define  ASC_CHR_GS	                   (0x1d)
#define  ASC_CHR_RS	                   (0x1e)
#define  ASC_CHR_US	                   (0x1f)
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

#endif   // __AscConst_H__
