/**
******************************************************************************
* @file      ext_comm_frame_type.h
* <AUTHOR>
* @date      2023-7-18
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef _EXTERNAL_COMMUNICATION_FRAME_TYPE_H_
#define _EXTERNAL_COMMUNICATION_FRAME_TYPE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32h7xx_hal.h"	/* for data type(uint8_t, uint32_t .....) */

#ifdef __cplusplus
}
#endif

#endif	/* _EXTERNAL_COMMUNICATION_FRAME_TYPE_H_ */

