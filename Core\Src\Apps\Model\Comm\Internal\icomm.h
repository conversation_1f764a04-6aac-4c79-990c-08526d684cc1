/**
******************************************************************************
* @file      icomm.h
* <AUTHOR>
* @date      2024-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_ICOMM_H_
#define SRC_APPS_MODEL_COMM_ICOMM_H_

#include "Common.h"

void Internal_Comm_Send(Icomm_Protocol_Tx_s *pComm);
void Internal_Comm_Handshake_EnQueue(Icomm_Protocol_Tx_s *pComm);
void Internal_Comm_Task(void);
void Internal_Buffer_Init(u8 *pData, u16 len);

void Comm_Debug_Rx_Msg(u8 *pData, u16 len);
void Comm_Debug_Tx_Msg(u8 *pData, u16 len);

#endif /* SRC_APPS_MODEL_COMM_ICOMM_H_ */
