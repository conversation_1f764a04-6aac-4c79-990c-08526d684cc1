/**
******************************************************************************
* @file      system_SelfDiagnosis.c
* <AUTHOR>
* @date      2023-11-08
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "model.h"
#include "TargetBoard.h"

void System_Diagnosis_Init(void)
{
    memset(&g_hSysStatus.m_pStat->diag, 0, sizeof(sDiagnosis));
}

void System_Diagnosis_518KHz_Startphasing(void)
{
    sDiagnosis *pDiag = (sDiagnosis *)&g_hSysStatus.m_pStat->diag;

    static int tickCnt = 0;  
    static int seq = 0;
    int tickRet = 0;
    int ret = 0;

    if(pDiag->Recv_Result_RF_518KHz == DIAG_STATE_START)
    {
        switch(seq)
        {
            case 0:
                seq += 1;
                Set_Gpio_Ant_Power(0);
            break;

            case 1:
                tickRet = Common_Tick_End_Timer(&tickCnt, TICK_500_MSEC);
                if(tickRet == 1)
                {
                    (void)Proc_Self_Diag_StartPhasing_Test(&gNavtexRx_518KHz, 0);
                    seq += 1;
                }
            break;

            case 2:
                tickRet = Common_Tick_End_Timer(&tickCnt, TICK_15_SEC);
                if(tickRet == 1)
                {
                    Internal_Tx_Diag_Response(DIAG_TYPE_RECV_RF_518KHZ, DIAG_STATE_TIMEOUT);
                    pDiag->Recv_Result_RF_518KHz = DIAG_STATE_NONE;

                    DEBUG_MSG("[ERROR] 518KHz Diag Start phasing Test Timeout\r\n");
                }
                else
                {
                    ret = Proc_Self_Diag_StartPhasing_Test(&gNavtexRx_518KHz, 1);
                    if(ret == RECV_DIAG_TYPE_OK)
                    {
                        pDiag->Recv_Result_RF_518KHz = DIAG_STATE_NONE;
                        DEBUG_MSG("[OK] 518KHz Diag Start phasing Test End\r\n");

                        Internal_Tx_Diag_Response(DIAG_TYPE_RECV_RF_518KHZ, DIAG_STATE_SUCCESS);
                    }
                }
            break;
        }
    }
    else
    {
        tickCnt = 0;
        seq = 0;
    }
}

void System_Diagnosis_490KHz_Startphasing(void)
{
    sDiagnosis *pDiag = (sDiagnosis *)&g_hSysStatus.m_pStat->diag;

    static int tickCnt = 0;  
    static int seq = 0;
    int tickRet = 0;
    int ret = 0;

    if(pDiag->Recv_Result_RF_490KHz == DIAG_STATE_START)
    {
        switch(seq)
        {
            case 0:
                Set_Gpio_RF_Relay(RF_RELAY_CH_490KHZ);
                Set_Gpio_Ant_Power(0);

                seq += 1;
            break;

            case 1:
                tickRet = Common_Tick_End_Timer(&tickCnt, TICK_500_MSEC);
                if(tickRet == 1)
                {
                    (void)Proc_Self_Diag_StartPhasing_Test(&gNavtexRx_490KHz, 0);
                    seq += 1;
                }
            break;

            case 2:
                tickRet = Common_Tick_End_Timer(&tickCnt, TICK_15_SEC);
                if(tickRet == 1)
                {
                    Internal_Tx_Diag_Response(DIAG_TYPE_RECV_RF_490KHZ, DIAG_STATE_TIMEOUT);
                    pDiag->Recv_Result_RF_490KHz = DIAG_STATE_NONE;

                    DEBUG_MSG("[ERROR] 490KHz Diag Start phasing Test Timeout\r\n");
                }
                else
                {
                    ret = Proc_Self_Diag_StartPhasing_Test(&gNavtexRx_490KHz, 1);
                    if(ret == RECV_DIAG_TYPE_OK)
                    {
                        pDiag->Recv_Result_RF_490KHz = DIAG_STATE_NONE;
                        DEBUG_MSG("[OK] 490KHz Diag Start phasing Test End\r\n");

                        Internal_Tx_Diag_Response(DIAG_TYPE_RECV_RF_490KHZ, DIAG_STATE_SUCCESS);
                    }
                }
            break;
        }
    }
    else
    {
        tickCnt = 0;
        seq = 0;
    }
}

void System_Diagnosis_4209_5KHz_Startphasing(void)
{
    sDiagnosis *pDiag = (sDiagnosis *)&g_hSysStatus.m_pStat->diag;

    static int tickCnt = 0;  
    static int seq = 0;
    int tickRet = 0;
    int ret = 0;

    if(pDiag->Recv_Result_RF_4209_5KHz == DIAG_STATE_START)
    {
        switch(seq)
        {
            case 0:
                Set_Gpio_RF_Relay(RF_RELAY_CH_4209_5KHZ);
                Set_Gpio_Ant_Power(0);

                seq += 1;
            break;

            case 1:
                tickRet = Common_Tick_End_Timer(&tickCnt, TICK_500_MSEC);
                if(tickRet == 1)
                {
                    (void)Proc_Self_Diag_StartPhasing_Test(&gNavtexRx_490KHz, 0);
                    seq += 1;
                }
            break;

            case 2:
                tickRet = Common_Tick_End_Timer(&tickCnt, TICK_15_SEC);
                if(tickRet == 1)
                {
                    Internal_Tx_Diag_Response(DIAG_TYPE_RECV_RF_4209_5KHZ, DIAG_STATE_TIMEOUT);
                    pDiag->Recv_Result_RF_4209_5KHz = DIAG_STATE_NONE;

                    DEBUG_MSG("[ERROR] 4209.5KHz Diag Start phasing Test Timeout\r\n");
                }
                else
                {
                    ret = Proc_Self_Diag_StartPhasing_Test(&gNavtexRx_490KHz, 1);
                    if(ret == RECV_DIAG_TYPE_OK)
                    {
                        pDiag->Recv_Result_RF_4209_5KHz = DIAG_STATE_NONE;
                        DEBUG_MSG("[OK] 4209.5KHz Diag Start phasing Test End\r\n");

                        Internal_Tx_Diag_Response(DIAG_TYPE_RECV_RF_4209_5KHZ, DIAG_STATE_SUCCESS);    
                    }
                }
            break;
        }
    }
    else
    {
        tickCnt = 0;
        seq = 0;
    }
}

void System_Diagnosis_Message_Receive_Monitor(void)
{
    sDiagnosis *pDiag = (sDiagnosis *)&g_hSysStatus.m_pStat->diag;

    static int tickCnt = 0;  
    static int seq = 0;
    static int rf_relay = 0;
    static int ant_power = 0;
    static int pre_state = 0;
    int tickRet = 0;
    int ret = 0;

    if(pDiag->Msg_Recv_Mon_State == DIAG_STATE_START)
    {
        switch(seq)
        {
            case 0:
                rf_relay = Get_Gpio_RF_Relay();
                Set_Gpio_RF_Relay(RF_RELAY_CH_490KHZ);

                ant_power = Get_Gpio_Ant_Power();
                Set_Gpio_Ant_Power(0);
                seq += 1;
            break;

            case 1:
                tickRet = Common_Tick_End_Timer(&tickCnt, TICK_500_MSEC);
                if(tickRet == 1)
                {
                    (void)Proc_Self_Diag_Message_Tx(0);
                    seq += 1;
                }
            break;

            case 2:
                tickRet = Common_Tick_End_Timer(&tickCnt, TICK_60_SEC);
                if(tickRet == 1)
                {
                    Internal_Tx_Diag_Response(DIAG_TYPE_RECV_MSG_RF_ALL, DIAG_STATE_TIMEOUT);
                    seq = 0;
                    pDiag->Msg_Recv_Mon_State = DIAG_STATE_NONE;
                }
                else
                {
                    ret = Proc_Self_Diag_Message_Tx(1);
                    if(ret == RECV_DIAG_TYPE_OK)
                    {
                        Set_Gpio_RF_Relay(RF_RELAY_CH_4209_5KHZ);
                        seq += 1;
                    }
                }
            break;

            case 3:
                tickRet = Common_Tick_End_Timer(&tickCnt, TICK_500_MSEC);
                if(tickRet == 1)
                {
                    (void)Proc_Self_Diag_Message_Tx(0);
                    seq += 1;
                }
            break;

            case 4:
                tickRet = Common_Tick_End_Timer(&tickCnt, TICK_60_SEC);
                if(tickRet == 1)
                {
                    Internal_Tx_Diag_Response(DIAG_TYPE_RECV_MSG_RF_ALL, DIAG_STATE_TIMEOUT);

                    seq = 0;
                    pDiag->Msg_Recv_Mon_State = DIAG_STATE_NONE;
                }
                else
                {
                    ret = Proc_Self_Diag_Message_Tx(1);
                    if(ret == RECV_DIAG_TYPE_OK)
                    {
                        Internal_Tx_Diag_Response(DIAG_TYPE_RECV_MSG_RF_ALL, DIAG_STATE_END);

                        seq = 0;
                        pDiag->Msg_Recv_Mon_State = DIAG_STATE_NONE;
                    }
                }
            break;
        }
    }
    else
    {
        if(pre_state == DIAG_STATE_START)
        {
            (void)Proc_Self_Diag_Message_Tx(0);
            Set_Gpio_RF_Relay(rf_relay);
            Set_Gpio_Ant_Power(ant_power);
        }
        tickCnt = 0;
        seq = 0;
    }

    pre_state = pDiag->Msg_Recv_Mon_State;
}

void System_Diagnosis_Run(void)
{
    System_Diagnosis_518KHz_Startphasing();
    System_Diagnosis_490KHz_Startphasing();
    System_Diagnosis_4209_5KHz_Startphasing();
    System_Diagnosis_Message_Receive_Monitor();
}

