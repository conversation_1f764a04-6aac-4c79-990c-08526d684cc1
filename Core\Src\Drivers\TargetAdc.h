/**
******************************************************************************
* @file      TargetAdc.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef _TARGET_ADC_H_
#define _TARGET_ADC_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "Common.h"

typedef enum
{
	ADC1_TYPE_START = 0,
	ADC1_TYPE_ANT_SENSE = ADC1_TYPE_START,	// PA6 - ADC1_INP3
	ADC1_TYPE_RF_518KHZ,							// (FSK INT) PB1 - ADC1_INP5 <-> PA4 - dac ch1
	ADC1_TYPE_RF_490KHZ,							// (FSK LOC) PB0 - ADC1_INP9 <-> PA5 - dac ch2
	ADC1_TYPE_RF_42095KHZ,
	ADC1_MAX,
} ADC1_Type_t;

typedef enum
{
	ADC3_TYPE_START = 0,
	ADC3_TYPE_VREFINT = ADC3_TYPE_START,	// Vrefint
	ADC3_TYPE_PCB_VER_1,
	ADC3_TYPE_PCB_VER_2,

	ADC3_MAX,
} ADC3_Type_t;

typedef struct
{
	u32 Vrefint_adc;
	u32 Vrefint_cal;
	u32 Vref_adc;
	u32 Full_scale;

	u32 Adc1_IsrCnt;
	u32 Adc1_ErrorCnt;

	u32 Adc3_IsrCnt;
	u32 Adc3_ErrorCnt;

	u32 Adc_RF_518KHz;
	u32 Adc_RF_490KHz;
	u32 Adc_RF_42095KHz;

	u32 Adc_ANT_SENSE;
	u32 filtered_Adc_ANT_SENSE;

	u32 Adc_Ver_1;
	u32 filtered_Adc_Ver_1;

	u32 Adc_Ver_2;
	u32 filtered_Adc_Ver_2;

	u32 Samplling_Adc1_Val[ADC1_MAX];
	u32 Samplling_Adc3_Val[ADC3_MAX];

	u32 Samplling_Adc1_Val_Voltage[ADC1_MAX];
} sDrvAdcComponent;
extern sDrvAdcComponent gDrvAdc;

void TBD_init_adc(void);
void TBD_adc_read(void);
void TBD_Adc_Antenna_Sense_Filter(void);
void TBD_Adc_Receive_PCB_Ver_Filter(void);

#ifdef __cplusplus
}
#endif

#endif	/* _TARGET_ADC_H_ */
