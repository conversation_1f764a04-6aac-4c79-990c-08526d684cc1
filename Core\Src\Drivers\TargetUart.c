/**
******************************************************************************
* @file      TargetUart.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "model.h"
#include "TargetBoard.h"

extern UART_HandleTypeDef huart7;
extern UART_HandleTypeDef huart3;
extern UART_HandleTypeDef huart2;
extern UART_HandleTypeDef huart4;
extern UART_HandleTypeDef huart5;
extern UART_HandleTypeDef huart1;

target_uart_s tbd_uarts[MAX_TARGET_UART] __attribute__((section(".UartDmaSection")));
uint8_t tbd_uart_tx_data __attribute__((section(".UartDmaSection")));
uint8_t tx_isr_buf[MAX_TARGET_UART][MAX_TARGET_UART_BUF_SIZE] __attribute__((section(".UartDmaSection")));
uint8_t tbd_uart_rx_data[MAX_TARGET_UART] __attribute__((section(".UartDmaSection")));

// target_uart_s tbd_uarts[MAX_TARGET_UART];
// uint8_t tbd_uart_tx_data;
// uint8_t tx_isr_buf[MAX_TARGET_UART][MAX_TARGET_UART_BUF_SIZE];
// uint8_t tbd_uart_rx_data[MAX_TARGET_UART];

static uint8_t TBD_uart_is_buf_full(target_uart_buf_s *pBuf);
static uint8_t TBD_uart_is_buf_empty(target_uart_buf_s *pBuf);
static int TBD_uart_dequeue(target_uart_buf_s *pBuf);
static uint8_t TBD_uart_enqueue(target_uart_buf_s *pBuf, uint8_t data);
int TBD_uart_enqueue_ex(target_uart_buf_s *pBuf, uint8_t *p_data, int size);

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
static uint8_t TBD_uart_is_buf_full(target_uart_buf_s *pBuf)
{
	uint8_t ret = TBD_FALSE;

	if(pBuf != NULL)
	{
		if(((pBuf->rear+1) % MAX_TARGET_UART_BUF_SIZE) == pBuf->front)
		{
			ret = TBD_TRUE;
		}
	}
	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
static uint8_t TBD_uart_is_buf_empty(target_uart_buf_s *pBuf)
{
	uint8_t ret = TBD_FALSE;

	if(pBuf != NULL)
	{
		if(pBuf->front == pBuf->rear)
		{
			ret = TBD_TRUE;
		}
	}
	
	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
void TBD_uart_clear_all(target_uart_s *pUart)
{
	if(pUart != NULL)
	{
		pUart->lock_tx = TBD_FALSE;
		TBD_uart_init_buf(&pUart->tx);
		TBD_uart_init_buf(&pUart->rx);
	}
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
uint8_t TBD_uart_init_buf(target_uart_buf_s *pBuf)
{
	uint8_t ret = TBD_FAIL;

	if(pBuf != NULL)
	{
		pBuf->front = 0;
		pBuf->rear = 0;
		memset(pBuf->buf,0x00,MAX_TARGET_UART_BUF_SIZE);
		ret = TBD_SUCCESS;
	}
	return ret;
}


/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
static int TBD_uart_dequeue(target_uart_buf_s *pBuf)
{
	int ret = TARGET_UART_NULL_DATA;
	if(pBuf != NULL)
	{
		if(TBD_uart_is_buf_empty(pBuf) == TBD_FALSE)
		{
			ret = pBuf->buf[pBuf->front];
			pBuf->front = ((pBuf->front + 1)%MAX_TARGET_UART_BUF_SIZE);
		}
	}
	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
static uint8_t TBD_uart_enqueue(target_uart_buf_s *pBuf, uint8_t data)
{
	uint8_t ret = TBD_FAIL;

	if(pBuf != NULL)
	{
		if(TBD_uart_is_buf_full(pBuf) == TBD_FALSE)
		{
			pBuf->buf[pBuf->rear] = data;
			pBuf->rear = ((pBuf->rear + 1)%MAX_TARGET_UART_BUF_SIZE);
			ret = TBD_SUCCESS;
		}
	}
	
	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
int TBD_uart_enqueue_ex(target_uart_buf_s *pBuf, uint8_t *p_data, int size)
{
	int ret = TBD_FAIL;
	int i = 0;
	uint32_t stop_time = 0;

	if((pBuf != NULL) && (p_data != NULL))
	{
		for(i=0; i<size; i++)
		{
			while(TBD_uart_is_buf_full(pBuf) == TBD_TRUE)
			{
				stop_time = 0;
				if(stop_time >= 0xFFFFFF)
				{
					return TBD_FAIL;
				}
			}

			pBuf->buf[pBuf->rear] = p_data[i];
			pBuf->rear = (pBuf->rear + 1)%MAX_TARGET_UART_BUF_SIZE;
		}
		ret = TBD_SUCCESS;
	}
	
	return ret;
}

/**
 * @brief
 * @param  
 * @retval 
 * @see
 */
int TBD_uart_get_data(uint8_t uart_no)
{
	int data = TARGET_UART_NULL_DATA;
	if(uart_no < MAX_TARGET_UART)
	{
		// for isr
		// data = TBD_uart_dequeue(&tbd_uarts[uart_no].rx);

		// for dma
		tbd_uarts[uart_no].rx.front = (MAX_TARGET_UART_BUF_SIZE - __HAL_DMA_GET_COUNTER(tbd_uarts[uart_no].pHandle->hdmarx));
		if(tbd_uarts[uart_no].rx.rear != tbd_uarts[uart_no].rx.front)
		{
			data = tbd_uarts[uart_no].rx.buf[tbd_uarts[uart_no].rx.rear];

			tbd_uarts[uart_no].rx.rear += 1;
			if(tbd_uarts[uart_no].rx.rear >= MAX_TARGET_UART_BUF_SIZE)
			{
				tbd_uarts[uart_no].rx.rear -= MAX_TARGET_UART_BUF_SIZE;
			}
		}
		return data;
	}
	return data;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
int TBD_uart_send_data(uint8_t uart_no, uint8_t *pData, int nSize)
{
	uint32_t stop_time = 0;
	int nRet = 0;

	if((uart_no < MAX_TARGET_UART) && (tbd_uarts[uart_no].pHandle != NULL) && (pData != NULL))
	{
		while(tbd_uarts[uart_no].isEnQeueuIng_tx == TBD_TRUE)
		{
			stop_time += 1;
			if(stop_time >= 0xFFFFFFFF)
			{
				break;
			}
		}
		stop_time = 0;
		tbd_uarts[uart_no].isEnQeueuIng_tx = TBD_TRUE;

		if(tbd_uarts[uart_no].lock_tx == TBD_TRUE)
		{
			if(TBD_uart_enqueue_ex(&tbd_uarts[uart_no].tx,pData,nSize) == TBD_SUCCESS)
			{
				nRet = nSize;
			}
		}
		else
		{
			tbd_uarts[uart_no].lock_tx = TBD_TRUE;

			// for isr
			// HAL_UART_Transmit_IT(tbd_uarts[uart_no].pHandle, (uint8_t *)pData, nSize);

			// for dma
			SCB_CleanDCache_by_Addr((uint32_t*)pData, nSize);
			HAL_UART_Transmit_DMA(tbd_uarts[uart_no].pHandle,(uint8_t *)pData,nSize);
			SCB_InvalidateDCache_by_Addr((uint32_t *)pData, nSize);
			nRet = nSize;			
		}
		tbd_uarts[uart_no].isEnQeueuIng_tx = TBD_FALSE;
	}
	return nRet;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
//#pragma optimize("", off)
int TBD_uart_direct_send_data(uint8_t uart_no, uint8_t *pData, int nSize)
{	
	int nRet = 0;

	if((uart_no < MAX_TARGET_UART) && (tbd_uarts[uart_no].pHandle != NULL) && (pData != NULL))
	{
		// for isr
		// HAL_UART_Transmit_IT(tbd_uarts[uart_no].pHandle, (uint8_t *)pData, nSize);

		// for dma
		SCB_CleanDCache_by_Addr((uint32_t*)pData, nSize);
		HAL_UART_Transmit_DMA(tbd_uarts[uart_no].pHandle, (uint8_t *)pData, nSize);
		SCB_InvalidateDCache_by_Addr((uint32_t *)pData, nSize);
	}
	return nRet;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
void TBD_uart_send_str(uint8_t uart_no, uint8_t *pBuffer)
{
	int len = 0;

	len = strlen((const char *)pBuffer);
	(void)TBD_uart_send_data(uart_no, (uint8_t *)pBuffer, len);
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
uint8_t TBD_config_uart(uint8_t uart_no, uint32_t baud, uint32_t data_bits, uint32_t stop_bit, uint32_t parity, uint32_t auto_bps)
{
	uint8_t ret = TBD_FAIL;

	if(uart_no < MAX_TARGET_UART)
	{
		HAL_UART_DeInit(tbd_uarts[uart_no].pHandle);
		HAL_DMA_DeInit(tbd_uarts[uart_no].pHandle->hdmatx);
		HAL_DMA_DeInit(tbd_uarts[uart_no].pHandle->hdmarx);

		tbd_uarts[uart_no].pHandle->Init.BaudRate = baud;
		tbd_uarts[uart_no].pHandle->Init.WordLength = data_bits;
		tbd_uarts[uart_no].pHandle->Init.StopBits = stop_bit;
		tbd_uarts[uart_no].pHandle->Init.Parity = parity;
		tbd_uarts[uart_no].pHandle->Init.Mode = UART_MODE_TX_RX;
		tbd_uarts[uart_no].pHandle->Init.HwFlowCtl = UART_HWCONTROL_NONE;
		tbd_uarts[uart_no].pHandle->Init.OverSampling = UART_OVERSAMPLING_16;
		tbd_uarts[uart_no].pHandle->Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
		tbd_uarts[uart_no].pHandle->Init.ClockPrescaler = UART_PRESCALER_DIV1;

		if(auto_bps == 1)
		{
			tbd_uarts[uart_no].pHandle->AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_AUTOBAUDRATE_INIT;
			tbd_uarts[uart_no].pHandle->AdvancedInit.AutoBaudRateEnable = UART_ADVFEATURE_AUTOBAUDRATE_ENABLE;
			tbd_uarts[uart_no].pHandle->AdvancedInit.AutoBaudRateMode = UART_ADVFEATURE_AUTOBAUDRATE_ONSTARTBIT;
		}
		else
		{
			tbd_uarts[uart_no].pHandle->AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
		}

		if(HAL_UART_Init(tbd_uarts[uart_no].pHandle) != HAL_OK)
		{
			Error_Handler();
		}
		if (HAL_UARTEx_SetTxFifoThreshold(tbd_uarts[uart_no].pHandle, UART_TXFIFO_THRESHOLD_1_8) != HAL_OK)
		{
			Error_Handler();
		}
		if (HAL_UARTEx_SetRxFifoThreshold(tbd_uarts[uart_no].pHandle, UART_RXFIFO_THRESHOLD_1_8) != HAL_OK)
		{
			Error_Handler();
		}
		if (HAL_UARTEx_DisableFifoMode(tbd_uarts[uart_no].pHandle) != HAL_OK)
		{
			Error_Handler();
		}

		TBD_uart_clear_all(&tbd_uarts[uart_no]);

		// for dma
		ret = HAL_UART_Receive_DMA(tbd_uarts[uart_no].pHandle, tbd_uarts[uart_no].rx.buf, MAX_TARGET_UART_BUF_SIZE);

		// for isr
		// ret = HAL_UART_Receive_IT(tbd_uarts[uart_no].pHandle,(uint8_t *)&tbd_uart_rx_data[uart_no],1);
		// __HAL_UART_ENABLE_IT(tbd_uarts[uart_no].pHandle, UART_IT_RXNE);
		__HAL_UART_ENABLE_IT(tbd_uarts[uart_no].pHandle, UART_IT_TC);
		__HAL_UART_ENABLE_IT(tbd_uarts[uart_no].pHandle, UART_IT_ERR);
		if(ret == HAL_ERROR)
		{
			DEBUG_MSG("[%s] HAL_UART_Receive_DMA Set Error", __FUNCTION__);
			return ret;
		}

		ret = TBD_SUCCESS;
	}
	return ret;
}


/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
void TBD_init_setting_uarts(uint32_t uart_no, uint32_t buadrate, uint32_t auto_baud)
{
	TBD_uart_clear_all(&tbd_uarts[uart_no]);
	TBD_config_uart(uart_no, buadrate, UART_WORDLENGTH_8B, UART_STOPBITS_1, UART_PARITY_NONE, auto_baud);
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
void TBD_init_uarts()
{
	int i = 0;
	for(i=0; i<MAX_TARGET_UART; i++)
	{
		memset(&tbd_uarts[i], 0x00, sizeof(target_uart_s));
	}
	
	tbd_uarts[TARGET_UART_NAVTEX_CONTROL].pHandle = &huart7;
	TBD_uart_clear_all(&tbd_uarts[TARGET_UART_NAVTEX_CONTROL]);
	TBD_config_uart(TARGET_UART_NAVTEX_CONTROL, TARGET_BAUD_UART_NAVTEX_CONTROL, UART_WORDLENGTH_8B, UART_STOPBITS_1, UART_PARITY_NONE, 0);

	tbd_uarts[TARGET_UART_INS].pHandle = &huart2;
	TBD_uart_clear_all(&tbd_uarts[TARGET_UART_INS]);
	TBD_config_uart(TARGET_UART_INS, TARGET_BAUD_UART_NAVTEX_INS, UART_WORDLENGTH_8B, UART_STOPBITS_1, UART_PARITY_NONE, 1);

	tbd_uarts[TARGET_UART_BAM].pHandle = &huart4;
	TBD_uart_clear_all(&tbd_uarts[TARGET_UART_BAM]);
	TBD_config_uart(TARGET_UART_BAM, TARGET_BAUD_UART_NAVTEX_BAM, UART_WORDLENGTH_8B, UART_STOPBITS_1, UART_PARITY_NONE, 0);

	tbd_uarts[TARGET_UART_DEBUG].pHandle = &huart1;
	TBD_uart_clear_all(&tbd_uarts[TARGET_UART_DEBUG]);
	TBD_config_uart(TARGET_UART_DEBUG, TARGET_BAUD_UART_NAVTEX_DEBUG, UART_WORDLENGTH_8B, UART_STOPBITS_1, UART_PARITY_NONE, 0);
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	// target_uart_no_t target_uart_no;
	// int data = 0;

	// if(huart == tbd_uarts[TARGET_UART_NAVTEX_CONTROL].pHandle)
	// {
	// 	target_uart_no = TARGET_UART_NAVTEX_CONTROL;
	// }
	// else if(huart == tbd_uarts[TARGET_UART_INS].pHandle)
	// {
	// 	target_uart_no = TARGET_UART_INS;
	// }
	// else if(huart == tbd_uarts[TARGET_UART_BAM].pHandle)
	// {
	// 	target_uart_no = TARGET_UART_BAM;
	// }
	// else if(huart == tbd_uarts[TARGET_UART_DEBUG].pHandle)
	// {
	// 	target_uart_no = TARGET_UART_DEBUG;
	// }

	// if(tbd_uarts[target_uart_no].pHandle != NULL)
	// {
	// 	tbd_uarts[target_uart_no].lock_rx = TBD_TRUE;
	// 	TBD_uart_enqueue(&tbd_uarts[target_uart_no].rx, tbd_uart_rx_data[target_uart_no]);
	// 	tbd_uarts[target_uart_no].lock_rx = TBD_FALSE;
		
	// 	HAL_UART_Receive_IT(tbd_uarts[target_uart_no].pHandle,(uint8_t *)&tbd_uart_rx_data[target_uart_no],1);
	// }
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
	int data = 0;
	int size = 0;
	uint32_t stop_time = 0;
	target_uart_no_t target_uart_no;

	if(huart == tbd_uarts[TARGET_UART_NAVTEX_CONTROL].pHandle)
	{
		target_uart_no = TARGET_UART_NAVTEX_CONTROL;
	}
	else if(huart == tbd_uarts[TARGET_UART_INS].pHandle)
	{
		target_uart_no = TARGET_UART_INS;
	}
	else if(huart == tbd_uarts[TARGET_UART_BAM].pHandle)
	{
		target_uart_no = TARGET_UART_BAM;
	}
	else if(huart == tbd_uarts[TARGET_UART_DEBUG].pHandle)
	{
		target_uart_no = TARGET_UART_DEBUG;
	}

	if(TBD_uart_is_buf_empty(&tbd_uarts[target_uart_no].tx) == TBD_TRUE)
	{
		tbd_uarts[target_uart_no].lock_tx = TBD_FALSE;
	}
	else
	{
		while(TBD_uart_is_buf_empty(&tbd_uarts[target_uart_no].tx) != TBD_TRUE)
		{
			data = TBD_uart_dequeue(&tbd_uarts[target_uart_no].tx);
			if(data != TARGET_UART_NULL_DATA)
			{
				tx_isr_buf[target_uart_no][size++] = data;
			}
		}

		if(size > 0)
		{
			tbd_uarts[target_uart_no].lock_tx = TBD_TRUE;

			// for isr
			// HAL_UART_Transmit_IT(tbd_uarts[target_uart_no].pHandle, (uint8_t *)tx_isr_buf[target_uart_no], size);

			// for dma
			while(tbd_uarts[target_uart_no].pHandle->gState != HAL_UART_STATE_READY)
			{
				stop_time += 1;
				if(stop_time >= 0xFFFF)
				{
					break;
				}
			}
			SCB_CleanDCache_by_Addr((uint32_t *)tx_isr_buf[target_uart_no], size);
			HAL_UART_Transmit_DMA(tbd_uarts[target_uart_no].pHandle,(uint8_t *)tx_isr_buf[target_uart_no],size);
			SCB_InvalidateDCache_by_Addr((uint32_t *)tx_isr_buf[target_uart_no], size);
		}
		else
		{
			tbd_uarts[target_uart_no].lock_tx = TBD_FALSE;
		}
	}
}

/**
  * @brief  UART error callback management
  * @param  
  * @retval None
  * 
  */
 void TBD_UART_ErrorCallback_Management(void)
 {
	int i = 0;
	// 1sec task routine

	for(i=0; i<MAX_TARGET_UART; i++)
	{
		if(tbd_uarts[i].isr_error_cnt >= 1)
		{
			tbd_uarts[i].isr_error_cnt = 0;

			if(i == TARGET_UART_NAVTEX_CONTROL)
			{
				TBD_uart_clear_all(&tbd_uarts[i]);
				TBD_config_uart(TARGET_UART_NAVTEX_CONTROL, TARGET_BAUD_UART_NAVTEX_CONTROL, UART_WORDLENGTH_8B, UART_STOPBITS_1, UART_PARITY_NONE, 0);
				DEBUG_MSG("[ERROR][%s] TARGET_UART_NAVTEX_CONTROL\r\n", __FUNCTION__);
			}
			else if(i == TARGET_UART_INS)
			{
				TBD_init_setting_uarts(TARGET_UART_INS, g_hSysCfg.m_pCfg->ins.bps, g_hSysCfg.m_pCfg->ins.mode);
				DEBUG_MSG("[ERROR][%s] TARGET_UART_INS\r\n", __FUNCTION__);
			}
			else if(i == TARGET_UART_BAM)
			{
				TBD_init_setting_uarts(TARGET_UART_BAM, g_hSysCfg.m_pCfg->bam.bps, g_hSysCfg.m_pCfg->bam.mode);
				DEBUG_MSG("[ERROR][%s] TARGET_UART_BAM\r\n", __FUNCTION__);
			}
			else if(i == TARGET_UART_DEBUG)
			{
				TBD_uart_clear_all(&tbd_uarts[i]);
				TBD_config_uart(TARGET_UART_DEBUG, TARGET_BAUD_UART_NAVTEX_DEBUG, UART_WORDLENGTH_8B, UART_STOPBITS_1, UART_PARITY_NONE, 0);
				DEBUG_MSG("[ERROR][%s] TARGET_UART_DEBUG\r\n", __FUNCTION__);
			}
		}
	}
 }

/**
  * @brief  UART error callback.
  * @param  huart UART handle.
  * @retval None
  * 
  */
/*************************************************************************
 * #define  HAL_UART_ERROR_NONE  (0x00000000U)    // !< No error              
 * #define  HAL_UART_ERROR_PE    (0x00000001U)    // !< Parity error          
 * #define  HAL_UART_ERROR_NE    (0x00000002U)    // !< Noise error           
 * #define  HAL_UART_ERROR_FE    (0x00000004U)    // !< Frame error           
 * #define  HAL_UART_ERROR_ORE   (0x00000008U)    // !< Overrun error         
 * #define  HAL_UART_ERROR_DMA   (0x00000010U)    // !< DMA transfer error    
 * #define  HAL_UART_ERROR_RTO   (0x00000020U)    // !< Receiver Timeout error
************************************************************************/
void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
	u8 sysMode = 0;

	sysMode = g_hSysStatus.m_pStat->mode.system;
	if(sysMode <= SYS_BOOT_INIT)
	{
		return;
	}

	// recovery
	if(huart == tbd_uarts[TARGET_UART_NAVTEX_CONTROL].pHandle)
	{
		tbd_uarts[TARGET_UART_NAVTEX_CONTROL].isr_error_cnt += 1;
	}
	else if(huart == tbd_uarts[TARGET_UART_INS].pHandle)
	{
		if(g_hSysStatus.m_pStat->system_all_para_received_flag == 1)
		{
			tbd_uarts[TARGET_UART_INS].isr_error_cnt += 1;
		}
	}
	else if(huart == tbd_uarts[TARGET_UART_BAM].pHandle)
	{
		if(g_hSysStatus.m_pStat->system_all_para_received_flag == 1)
		{
			tbd_uarts[TARGET_UART_BAM].isr_error_cnt += 1;
		}
	}
	else if(huart == tbd_uarts[TARGET_UART_DEBUG].pHandle)
	{
		tbd_uarts[TARGET_UART_DEBUG].isr_error_cnt += 1;
	}
}
