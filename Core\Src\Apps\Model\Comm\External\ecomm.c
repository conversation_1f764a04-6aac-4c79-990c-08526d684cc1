/**
******************************************************************************
* @file      ecomm.c
* <AUTHOR>
* @date      2024-07-07
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetBoard.h"
#include "External.h"
#include "Internal.h"
#include "Nmea0183.h"

NMEA_61162_1_2_Tx_Buf_s nmea_bufs[NMEA_TYPE_MAX] __attribute__((section(".UartDmaSection")));


void External_Comm_Init(void)
{
    External_Comm_Reset(&gExtBamCom);
    External_Comm_Reset(&gExtRemCom);
    memset(nmea_bufs, 0x00, sizeof(NMEA_61162_1_2_Tx_Buf_s)*NMEA_TYPE_MAX);
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
uint8_t nmea_uart_is_buf_full(NMEA_61162_1_2_Tx_Buf_s *pBuf)
{
	uint8_t ret = TBD_FALSE;

	if(pBuf != NULL)
	{
		if(((pBuf->rear+1) % MAX_NMEA_61162_1_2_TX_BUF_SIZE) == pBuf->front)
		{
			ret = TBD_TRUE;
		}
	}
	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
uint8_t nmea_uart_is_buf_empty(NMEA_61162_1_2_Tx_Buf_s *pBuf)
{
	uint8_t ret = TBD_FALSE;

	if(pBuf != NULL)
	{
		if(pBuf->front == pBuf->rear)
		{
			ret = TBD_TRUE;
		}
	}
	
	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
int nmea_uart_enqueue(NMEA_61162_1_2_Tx_Buf_s *pBuf, uint8_t *p_data, int size)
{
	int ret = TBD_FAIL;
	int i = 0;
	uint32_t stop_time = 0;

	if((pBuf != NULL) && (p_data != NULL))
	{
		for(i=0; i<size; i++)
		{
			while(nmea_uart_is_buf_full(pBuf) == TBD_TRUE)
			{
				stop_time = 0;
				if(stop_time >= 0xFFFFFF)
				{
					return TBD_FAIL;
				}
			}				

			pBuf->buf[pBuf->rear] = p_data[i];
			pBuf->rear = (pBuf->rear + 1)%MAX_NMEA_61162_1_2_TX_BUF_SIZE;
		}
		ret = TBD_SUCCESS;
	}
	
	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
 */
int nmea_uart_dequeue(NMEA_61162_1_2_Tx_Buf_s *pBuf)
{
	int ret = TARGET_UART_NULL_DATA;
	if(pBuf != NULL)
	{
		if(nmea_uart_is_buf_empty(pBuf) == TBD_FALSE)
		{
			ret = pBuf->buf[pBuf->front];
			pBuf->front = ((pBuf->front + 1)%MAX_NMEA_61162_1_2_TX_BUF_SIZE);
		}
	}
	return ret;
}

void nmea_uart_tx_ins(void)
{
    int data = 0;

    do
    {
        data = nmea_uart_dequeue(&nmea_bufs[NMEA_TYPE_INS]);
        if(data == TARGET_UART_NULL_DATA)
        {
            return;
        }

        TBD_uart_send_data(TARGET_UART_INS, (uint8_t *)&data, 1);
    } while (data != TARGET_UART_NULL_DATA);
}

void nmea_uart_tx_bam(void)
{
    int data = 0;

    do
    {
        data = nmea_uart_dequeue(&nmea_bufs[NMEA_TYPE_BAM]);
        if(data == TARGET_UART_NULL_DATA)
        {
            return;
        }

        TBD_uart_send_data(TARGET_UART_BAM, (uint8_t *)&data, 1);
    } while (data != TARGET_UART_NULL_DATA);
}

void External_Nmea_Uart_Tx_Task(void)
{
    nmea_uart_tx_ins();
    nmea_uart_tx_bam();
}

void External_Comm_Reset(eCom_data_s *pCom)
{
    if(pCom == NULL)
    {
        return;
    }   

    pCom->bReceiving = TBD_FALSE;
    pCom->nRCnt = 0;

    memset((void *)pCom->rx, 0x00, sizeof(u8) * EX_COM_MAX_LEN);
}

void External_Comm_Bam_Task(void)
{
    eCom_data_s *pRx = &gExtBamCom;

    int data = TARGET_UART_NULL_DATA;
    do
    {
        data = TBD_uart_get_data(TARGET_UART_BAM);
        if(data == TARGET_UART_NULL_DATA)
        {
            return;
        }

        if(pRx->bReceiving == TBD_FALSE)
        {
            if(data == NMEA0183_GENERAL_S_DEL || data == NMEA0183_ENCAPSULATED_S_DEL)
            {
                External_Comm_Reset(pRx);
                pRx->rx[pRx->nRCnt ++] = data;
                pRx->bReceiving = TBD_TRUE;
            }
        }
        else
        {
            pRx->rx[pRx->nRCnt ++] = data;
            if(pRx->nRCnt > NMEA0183_MAX_SIZE)
            {
                External_Comm_Reset(pRx);
            }
            else
            {
                if(data == NMEA0183_LF)
                {
                    if(pRx->nRCnt >= NMEA0183_MIN_SIZE && pRx->rx[pRx->nRCnt - 2] == NMEA0183_CR)
                    {
                        if(nmea0183_parsing((char *)pRx->rx, pRx->nRCnt) == NMEA0183_OK)
						{
							//DEBUG_MSG("[Recv - NMEA0183] %s\r\n",pRx->rx);
                            Internal_Tx_Nmea0183_Data(pRx->rx, pRx->nRCnt);
						}
						else
						{
							DEBUG_MSG("[ERROR - NMEA0183] %s\r\n",pRx->rx);
						}
                    }
                    External_Comm_Reset(pRx);
                }
            }
        }
    } while (data != TARGET_UART_NULL_DATA);
}

void External_Comm_Ins_Task(void)
{
    eCom_data_s *pRx = &gExtRemCom;

    int data = TARGET_UART_NULL_DATA;
    do
    {
        data = TBD_uart_get_data(TARGET_UART_INS);
        if(data == TARGET_UART_NULL_DATA)
        {
            return;
        }

        if(pRx->bReceiving == TBD_FALSE)
        {
            if(data == NMEA0183_GENERAL_S_DEL || data == NMEA0183_ENCAPSULATED_S_DEL)
            {
                External_Comm_Reset(pRx);
                pRx->rx[pRx->nRCnt ++] = data;
                pRx->bReceiving = TBD_TRUE;
            }
        }
        else
        {
            pRx->rx[pRx->nRCnt ++] = data;
            if(pRx->nRCnt > NMEA0183_MAX_SIZE)
            {
                External_Comm_Reset(pRx);
            }
            else
            {
                if(data == NMEA0183_LF)
                {
                    if(pRx->nRCnt >= NMEA0183_MIN_SIZE && pRx->rx[pRx->nRCnt - 2] == NMEA0183_CR)
                    {
                        if(nmea0183_parsing((char *)pRx->rx, pRx->nRCnt) == NMEA0183_OK)
						{
							//DEBUG_MSG("[Recv - NMEA0183] %s\r\n",pRx->rx);
                            Internal_Tx_Nmea0183_Data(pRx->rx, pRx->nRCnt);
						}
						else
						{
							DEBUG_MSG("[ERROR - NMEA0183] %s\r\n",pRx->rx);
						}
                    }
                    External_Comm_Reset(pRx);
                }
            }
        }
    } while (data != TARGET_UART_NULL_DATA);
}
