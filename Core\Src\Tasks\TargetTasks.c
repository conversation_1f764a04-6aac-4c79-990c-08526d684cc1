/**
******************************************************************************
* @file      TargetTasks.c
* <AUTHOR>
* @date      2023-1-11
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/
#include "Common.h"
#include "TargetBoard.h"
#include "model.h"
#include "Comm.h"

#include "lwip.h"
#include "firmware_update.h"

extern IWDG_HandleTypeDef hiwdg1;

void StartEthernetTask(void const * argument)
{
	// /* Create tcp_ip stack thread */
	tcpip_init(NULL, NULL);

	/* Initialize the LwIP stack */
	Netif_Config();
	
	DEBUG_MSG("[%s] Thread Init...\r\n", __FUNCTION__);
	/* Infinite loop */
	for(;;)
	{
		if(Get_address_assinged() == 1)
		{
			Std450_Recv_Task();
			osDelay(10);
		}
		else
		{
			osDelay(500);
		}
	}
}

void StartSystemTask(void const * argument)
{
	static u16 tick = 0;
	static u8 tog = 0;

	DEBUG_MSG("[%s] Thread Init...\r\n", __FUNCTION__);
	for(;;)
	{
		tick += 1;
		if(tick >= 20)
		{
			tog ^= 0x01;
			//TargetCan_Test_Tx();
			//TBD_Dac_Test_Task();

			// TBD_uart_send_str(TARGET_UART_INS, "IEC61162-1 INS Port Transmit message\r\n");
			// TBD_uart_send_str(TARGET_UART_BAM, "IEC61162-1 BAM Port Transmit message\r\n");

			HAL_GPIO_WritePin(TP_LED_1_GPIO_Port, TP_LED_1_Pin, tog);
			tick = 0;
		}

		System_Booting_Run();
		System_Diagnosis_Run();
		NavtexModem_Task();
		System_Rtc_Task();
		system_VersionMgr_Task();
		TargetGpio_Task();
		
		TBD_UART_ErrorCallback_Management();

		osDelay(50);
	}
}

void StartSystemRestTask(void const * argument)
{
	DEBUG_MSG("[%s] Thread Init...\r\n", __FUNCTION__);
	for(;;)
	{
		osThreadTerminate(NULL);
	}
}

void StartOsMsgProcessingTask(void const * argument)
{
	//init_osTaskMsgManager();
	DEBUG_MSG("[%s] Thread Init...\r\n", __FUNCTION__);
	for(;;)
	{
		//osTaskMsgHandle();

		//osDelay(50);
		osThreadTerminate(NULL);
	}
}

void StartCommTask(void const * argument)
{
	DEBUG_MSG("[%s] Thread Init...\r\n", __FUNCTION__);
	for(;;)
	{
		Internal_Comm_Task();
		External_Comm_Bam_Task();
		External_Comm_Ins_Task();
		TestingComm_Task();
		Handshake_Backend_Task(TARGET_UART_NAVTEX_CONTROL);
		Handshake_Backend_Response_Recv_Timeout();
		
		osDelay(1);
	}
}

void StartNmeaTxTask(void const * argument)
{
	DEBUG_MSG("[%s] Thread Init...\r\n", __FUNCTION__);
	for(;;)
	{
		External_Nmea_Uart_Tx_Task();
		osDelay(200);
	}
}

void StartRestTask(void const * argument)
{
	DEBUG_MSG("[%s] Thread Init...\r\n", __FUNCTION__);
	for(;;)
	{
		//G729_Encode_Run();
		//G729_Decode_Run();
		//TargetCan_Audio_Tx_Task();

		HAL_IWDG_Refresh(&hiwdg1);
		system_CommMgr_Task();
		osDelay(500);
	}
}

