/**
******************************************************************************
* @file      TargetAdc.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "targetBoard.h"

extern ADC_HandleTypeDef hadc1;
extern ADC_HandleTypeDef hadc3;
extern HRTIM_HandleTypeDef hhrtim;

sDrvAdcComponent gDrvAdc = {0};

u32 Adc1_DataBuffer[ADC1_MAX] __attribute__((section(".AdcDmaSection")));
u32 Adc3_DataBuffer[ADC3_MAX] __attribute__((section(".AdcDmaSection")));

/**
  * @brief  
  * @param  
  * @retval 
  */
void TBD_init_adc(void)
{
	memset(&gDrvAdc, 0, sizeof(sDrvAdcComponent));
	memset(&Adc1_DataBuffer[0], 0, sizeof(u32)*ADC1_MAX);
	memset(&Adc3_DataBuffer[0], 0, sizeof(u32)*ADC3_MAX);

	if (HAL_ADCEx_Calibration_Start(&hadc1, ADC_CALIB_OFFSET_LINEARITY, ADC_SINGLE_ENDED) != HAL_OK)
  	{
  	  DEBUG_MSG("[%s] Adc1 Calibration Start Error \r\n", __FUNCTION__);
  	}

	if (HAL_ADCEx_Calibration_Start(&hadc3, ADC_CALIB_OFFSET_LINEARITY, ADC_SINGLE_ENDED) != HAL_OK)
  	{
  	  DEBUG_MSG("[%s] Adc3 Calibration Start Error \r\n", __FUNCTION__);
  	}
#if NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BASE_1_785KHZ
	HAL_ADC_Start_DMA(&hadc1, (uint32_t *)&Adc1_DataBuffer[0], ADC1_MAX-1);
	HAL_ADC_Start_DMA(&hadc3, (uint32_t *)&Adc3_DataBuffer[0], ADC3_MAX);
#elif NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BAND_PASS_10KHZ
	HAL_ADC_Start_DMA(&hadc1, (uint32_t *)&Adc1_DataBuffer[0], ADC1_MAX);
	HAL_ADC_Start_DMA(&hadc3, (uint32_t *)&Adc3_DataBuffer[0], ADC3_MAX);
#endif

	HAL_HRTIM_SimpleBaseStart(&hhrtim, HRTIM_TIMERINDEX_TIMER_A);
	//HAL_HRTIM_SimpleBaseStart_IT(&hhrtim, HRTIM_TIMERINDEX_TIMER_A);

	HAL_HRTIM_SimpleBaseStart(&hhrtim, HRTIM_TIMERINDEX_TIMER_B);
	//HAL_HRTIM_SimpleBaseStart_IT(&hhrtim, HRTIM_TIMERINDEX_TIMER_B);
}

/**
  * @brief  
  * @param 
  * @retval 
  */
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef *hadc)
{
	// speed check
	if(hadc == &hadc1)
	{
		gDrvAdc.Adc1_IsrCnt += 1;

		FSK_Msg_Tx_Run();
		FSK_Msg_Rx_Run();
	}
	else if(hadc == &hadc3)
	{
		gDrvAdc.Adc3_IsrCnt += 1;

		TBD_Adc_Antenna_Sense_Filter();
		TBD_Adc_Receive_PCB_Ver_Filter();
	}
}

void HAL_ADC_ErrorCallback(ADC_HandleTypeDef *hadc)
{
	if(hadc == &hadc1)
	{
		gDrvAdc.Adc1_ErrorCnt += 1;
	}
	else if(hadc == &hadc3)
	{
		gDrvAdc.Adc3_ErrorCnt += 1;
	}
}

/**
  * @brief  
  * @param  
  * @retval 
  */
void TBD_Adc_Antenna_Sense_Filter(void)
{
	f32 Adc_Ant_Sense = 0;
	static f32 pre_Ant_Sense = 0.0f;
	static f32 cur_Ant_Sense = 0.0f;

	Adc_Ant_Sense = (f32)Adc1_DataBuffer[ADC1_TYPE_ANT_SENSE];
	cur_Ant_Sense = (0.9f * pre_Ant_Sense) + (0.1f * Adc_Ant_Sense);
	pre_Ant_Sense = cur_Ant_Sense;

	gDrvAdc.Adc_ANT_SENSE = Adc_Ant_Sense;
	gDrvAdc.filtered_Adc_ANT_SENSE = cur_Ant_Sense;
}

/**
  * @brief  
  * @param  
  * @retval 
*/
void TBD_Adc_Receive_PCB_Ver_Filter(void)
{
	f32 Adc_ver_1 = 0;
	static f32 pre_ver_1 = 0.0f;
	static f32 cur_ver_1 = 0.0f;

	f32 Adc_ver_2 = 0;
	static f32 pre_ver_2 = 0.0f;
	static f32 cur_ver_2 = 0.0f;

	Adc_ver_1 = (f32)Adc3_DataBuffer[ADC3_TYPE_PCB_VER_1];
	cur_ver_1 = (0.9f * pre_ver_1) + (0.1f * Adc_ver_1);
	pre_ver_1 = cur_ver_1;

	gDrvAdc.Adc_Ver_1 = Adc_ver_1;
	gDrvAdc.filtered_Adc_Ver_1 = cur_ver_1;

	/////////////////////////////////////////////////////////////////
	Adc_ver_2 = (f32)Adc3_DataBuffer[ADC3_TYPE_PCB_VER_2];
	cur_ver_2 = (0.9f * pre_ver_2) + (0.1f * Adc_ver_2);
	pre_ver_2 = cur_ver_2;

	gDrvAdc.Adc_Ver_2 = Adc_ver_2;
	gDrvAdc.filtered_Adc_Ver_2 = cur_ver_2;
}

/**
  * @brief  
  * @param  
  * @retval 
  */
void TBD_adc_read(void)
{
	// Vrefint_cal = the Vrefint calibration value
	// Vrefint_data = the actual Vrefint output value convered by ADC

	// Vref+ = 3.3V * Vrefint_cal / Vrefint_data
	// Vref_adc = 65535 * Vrefint_cal / Vrefint_data

	// V(channel) = Vref+ * Adc_data / Full_scale
	// V(channel) = 3.3V * Vrefint_cal * Adc_data / Vrefint_data * Full_scale
	// V(channel_adc) = Vref_adc * Vrefint_cal * Adc_data / Vrefint_data * Full_scale

#if 0
	gDrvAdc.Full_scale = 4095;

	// from datasheet
	gDrvAdc.Vrefint_cal = *((uint32_t *)0x1FF1E860);
	gDrvAdc.Vrefint_adc = gDrvAdc.Samplling_Adc3_Val[ADC3_TYPE_VREFINT];

	// Vrefint calibration
	if(gDrvAdc.Vrefint_adc > 0)
	{
		gDrvAdc.Vref_adc = ((gDrvAdc.Full_scale * gDrvAdc.Vrefint_cal) / gDrvAdc.Vrefint_adc);
		gDrvAdc.Adc_RF_518KHz = (gDrvAdc.Vref_adc *  gDrvAdc.Samplling_Adc1_Val[ADC1_TYPE_RF_518KHZ]) / gDrvAdc.Full_scale;
		gDrvAdc.Adc_RF_490KHz = (gDrvAdc.Vref_adc *  gDrvAdc.Samplling_Adc1_Val[ADC1_TYPE_RF_490KHZ]) / gDrvAdc.Full_scale;
	}
	else
	{
		gDrvAdc.Adc_RF_518KHz = gDrvAdc.Samplling_Adc1_Val[ADC1_TYPE_RF_518KHZ];
		gDrvAdc.Adc_RF_490KHz = gDrvAdc.Samplling_Adc1_Val[ADC1_TYPE_RF_490KHZ];
	}
#else
	gDrvAdc.Adc_RF_518KHz = Adc1_DataBuffer[ADC1_TYPE_RF_518KHZ];
	gDrvAdc.Adc_RF_490KHz = Adc1_DataBuffer[ADC1_TYPE_RF_490KHZ];
	gDrvAdc.Adc_RF_42095KHz = Adc1_DataBuffer[ADC1_TYPE_RF_42095KHZ];
#endif

}