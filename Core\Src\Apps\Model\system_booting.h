/**
******************************************************************************
* @file      system_booting.h
* <AUTHOR>
* @date      2023-11-07
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_SYSTEM_BOOTING_H_
#define SRC_APPS_MODEL_SYSTEM_BOOTING_H_

#define BOOT_NONE 0
#define BOOT_OK 1
#define BOOT_FAIL -1

#define BOOT_TICK_6_SEC (6000/50)
#define BOOT_TICK_2_SEC (2000/50)

typedef enum
{
    SYS_BOOT_INIT = 0,
    SYS_BOOT_PARAMETER_GET,
    SYS_BOOT_PRE_RUN,
    SYS_BOOT_RUN,
    SYS_BOOT_FAIL,
    SYS_BOOT_SLEEP,

    MAX_SYS_BOOT,
}eSystemBootType;

void System_Booting_Run(void);



#endif /* SRC_APPS_MODEL_SYSTEM_BOOTING_H_ */
