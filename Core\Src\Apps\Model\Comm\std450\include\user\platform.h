/*
 * platform.h
 *
 *  Created on: Aug 7, 2024
 *      Author: intellian
 */

#ifndef STD450_INCLUDE_USER_PLATFORM_H_
#define STD450_INCLUDE_USER_PLATFORM_H_

#include "Common_Config.h"

#define USE_LINUX   	0
#define USE_RTOS_LWIP   1

#if (USE_LINUX == 1)
#define MEM_MALLOC  malloc
#define MEM_REALLOC realloc
#define MEM_FREE    free
#define SLEEP(n)    uSleep(*1000)

#elif (USE_RTOS_LWIP == 1)
#include <memp.h>
#define MEM_MALLOC  mem_malloc
#define MEM_FREE    mem_free
#define SLEEP(n)    vTaskDelay(n)
#endif

#ifdef EN_DBG_MSG
#define DEBUG_LOG(fmt, args...)         printf("[STD450]" fmt, ##args)
#define DEBUG_LOG_NF(fmt, args...)      printf("[STD450-NF]" fmt, ##args)
#define DEBUG_LOG_SF(fmt, args...)      printf("[STD450-SF]" fmt, ##args)
#define DEBUG_LOG_NET(fmt, args...)     printf("[STD450-NET]" fmt, ##args)
#define DEBUG_LOG_DATA(fmt, args...)    printf("[STD450-DATA]" fmt, ##args)
#define DEBUG_LOG_THREAD(fmt, args...)  printf("[STD450-THREAD]" fmt, ##args)
#define DEBUG_LOG_NET_USER(fmt, args...) printf("[STD450-NET-USER]" fmt, ##args)
#else
#define DEBUG_LOG(fmt, args...)
#define DEBUG_LOG_NF(fmt, args...)
#define DEBUG_LOG_SF(fmt, args...)
#define DEBUG_LOG_NET(fmt, args...)
#define DEBUG_LOG_DATA(fmt, args...)
#define DEBUG_LOG_THREAD(fmt, args...)
#define DEBUG_LOG_NET_USER(fmt, args...)
#endif
#endif /* STD450_INCLUDE_USER_PLATFORM_H_ */
