// RFC3551 RTP Profile for Audio and Video Conferences with Minimal Control

#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include <errno.h>
#include "rtp-packet.h"
#include "g729a.h"

static encoder_state en_state;

void* rtp_pack_create(uint8_t pt, uint16_t seq, uint32_t ssrc)
{
	struct rtp_packet_t *pkt;

	pkt = (struct rtp_packet_t *)calloc(1, sizeof(struct rtp_packet_t));
	if (!pkt) return NULL;

	pkt->rtp.v = RTP_VERSION;
	pkt->rtp.p = 0;
	pkt->rtp.x = 0;
	pkt->rtp.cc = 0;

	// The RTP header Marker bit MUST be set equal to 0 if the packet is not the last 
	// packet of the temporal unit, it SHOULD be set equal to 1 otherwise.
	// Note: It is possible for a receiver to receive the last packet of a temporal unit 
	// without the marker bit being set equal to 1, and a receiver should be able to handle 
	// this case. The last packet of a temporal unit is also indicated by the next packet, 
	// in RTP sequence number order, having an incremented timestamp.
	pkt->rtp.m = 0;
	pkt->rtp.pt = pt;
	pkt->rtp.seq = seq;
	pkt->rtp.timestamp = 0;
	pkt->rtp.ssrc = ssrc;

	if (pt == RTP_PAYLOAD_G729)
	{
		g729a_encoder_init(&en_state, 1);
	}

	return pkt;
}

void rtp_pack_destroy(void* p)
{
	struct rtp_packet_t *pkt;
	assert(p);

	pkt = (struct rtp_packet_t *)p;
	free(pkt);
}

void rtp_pack_get_info(void* p, uint16_t* seq, uint32_t* timestamp)
{
	struct rtp_packet_t *pkt;
	pkt = (struct rtp_packet_t *)p;
	*seq = (uint16_t)pkt->rtp.seq;
	*timestamp = pkt->rtp.timestamp;
}

int rtp_pack_input(void* p, const void* data, int bytes)
{
	int r, n;
	struct rtp_packet_t *pkt;

	assert(p && data);

	r = 0;
	pkt = (struct rtp_packet_t *)p;
	
	if (pkt)
	{
		pkt->rtp.m = 0; // marker bit alway 0

		if (pkt->rtp.pt == RTP_PAYLOAD_G729)
		{
			g729a_encoder(&en_state, (short *)data, (uint8_t*)pkt->payload, &pkt->payloadlen);
			if (pkt->payloadlen > 0 && pkt->payloadlen <= RTP_FIXED_PAYLOAD)
			{
				n = RTP_FIXED_HEADER + (pkt->rtp.cc * RTP_CSRC_LEN) + (pkt->rtp.x ? 4 : 0) + pkt->payloadlen;
				r = rtp_packet_serialize(pkt, pkt->serialbuf, n);
				if (r > 0)
				{
					pkt->serialbuflen = r;
					pkt->rtp.seq++;
					pkt->rtp.timestamp += n;
				}
				else
				{
					r = 0;
				}
			}
		}
	}

	return r;
}
