/**
******************************************************************************
* @file      icomm_tx_datalink.h
* <AUTHOR>
* @date      2024-06-14
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_INTERNAL_ICOMM_TX_DATALINK_H_
#define SRC_APPS_MODEL_COMM_INTERNAL_ICOMM_TX_DATALINK_H_

#include "Common.h"
#include "NavModem.h"
#include "system_status.h"

void Internal_Tx_CRC_Response(u16 crc, u16 cmd);

void Internal_Tx_System_Parameter_Getting_Request(void);
void Internal_Tx_Diag_Response(int type, int state);
void Internal_Tx_Nav_Msg(sNavtexMsg *pNavMsg, u16 Param, u8 FreqType, u16 ArrayNum, u8 *pData);
void Internal_Tx_Nmea0183_Data(u8 *pData, u16 len);
void Internal_Tx_Parameter_State_Update(void);
void Internal_Tx_Can_Received_Data(u32 ID, u8 *pData, u8 len);
void Internal_Tx_System_Parameter_Sending_Success(void);
void Internal_Tx_System_All_Parameter_Send(void);
void Internal_Tx_Network_Setting_Info(int eth_link, int mode, ip_addr_t ip, ip_addr_t netmask, ip_addr_t gateway);

void Internal_Tx_Fsk_Resp_DeMod_Test(FSK_DeMod_Test_s *pTest, int ch);
void Internal_Tx_System_Setting_Response(int type, int n, ...);
void Internal_Tx_Rf_Self_BER_Test_Response(int n, ...);

void Internal_Tx_FWU_Request_Device_Fw_Info(void);
void Internal_Tx_FWU_Cmd_ex(uint16_t cmd, uint16_t sub_cmd, uint8_t* p_data, uint16_t len);
void Internal_Tx_FWU_Cmd(uint8_t *frame_buf, uint32_t frame_len);

#endif /* SRC_APPS_MODEL_COMM_INTERNAL_ICOMM_TX_DATALINK_H_ */





