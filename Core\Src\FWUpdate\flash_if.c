/*
 * flash_if.c
 *
 *  Created on: Apr 1, 2025
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */
#include "flash_if.h"
#include "stm32h7xx_hal.h"
#include "stdio.h"
#include "string.h"
#include "cmsis_os.h" // RTOS 임계 영역 사용
#include "userdef.h"

#define FLASH_BANK1_START_ADDR  FLASH_BANK1_BASE //((uint32_t)0x08000000)
#define FLASH_BANK2_START_ADDR  FLASH_BANK2_BASE //((uint32_t)0x08100000) // 2MB 모델 (1MB x 2)
#define FLASH_TOTAL_SIZE        FLASH_SIZE //((uint32_t)0x00200000) // 2MB
#define FLASH_WRITE_UNIT_BYTES  32 // H743 플래시 쓰기 단위 (256비트 = 32바이트)
#define NB_SECTORS_PER_BANK     (FLASH_BANK_SIZE / FLASH_SECTOR_SIZE) // 뱅크당 섹터 수 (8개)

// --- *** 비활성 뱅크 CPU 시작 주소 정의 (firmware_update.h 와 일치) *** ---
#define FWU_INACTIVE_BANK_START_ADDR FLASH_BANK2_START_ADDR

// --- 전압 범위 설정 ---
#define FLASH_VOLTAGE_RANGE     FLASH_VOLTAGE_RANGE_3

/**
 * @brief 현재 사용중인 뱅크 확인
 * @retval uint8_t 현재 사용중인 뱅크 (FLASH_BANK_1 or FLASH_BANK_2)
 */
uint8_t FLASH_IF_GetCurrentBank(void)
{
	FLASH_OBProgramInitTypeDef OBInit = { 0 };
	// HAL_FLASH_OB_Unlock(); // 필요시
	HAL_FLASHEx_OBGetConfig(&OBInit);
	// HAL_FLASH_OB_Lock();
	if ((OBInit.USERConfig & OB_SWAP_BANK_ENABLE) != 0)
		return FLASH_BANK_2;
	else
		return FLASH_BANK_1;
}

/**
 * @brief 지정된 주소/길이가 비활성 뱅크의 유효 범위 내에 있고 정렬되었는지 확인
 * (비활성 뱅크는 항상 CPU 주소 0x08100000 부터 시작)
 * @param write_addr 검사할 시작 주소 (CPU 주소, 예: 0x081xxxxx)
 * @param len 검사할 길이
 * @retval 1: 유효, 0: 유효하지 않음
 */
uint8_t FLASH_IF_IsAddressValid(uint32_t write_addr, uint32_t len)
{
	const uint32_t inactive_bank_start = FWU_INACTIVE_BANK_START_ADDR; // 비활성 뱅크 시작 주소 상수 사용

	// 시작 주소 범위 확인
	if (write_addr < inactive_bank_start || write_addr >= (inactive_bank_start + FLASH_BANK_SIZE))
	{
		DM("[FWU] Error: Write address 0x%lX is outside inactive bank (0x%lX - 0x%lX).\r\n", write_addr, inactive_bank_start, inactive_bank_start + FLASH_BANK_SIZE - 1);
		return 0;
	}
	// 길이 0 처리
	if (len == 0)
	{
		if (write_addr % FLASH_WRITE_UNIT_BYTES != 0) // 시작 주소 정렬 확인
		{
			DM("[FWU] Warning: Write address 0x%lX is not aligned to %d bytes.\r\n", write_addr, FLASH_WRITE_UNIT_BYTES);
			return 0;
		}
		return 1;
	}
	// 마지막 주소 범위 확인 (오버플로우 주의)
	if (len > FLASH_BANK_SIZE || (write_addr - inactive_bank_start + len) > FLASH_BANK_SIZE)
	{
		DM("[FWU] Error: Write operation exceeds inactive bank boundary (Addr=0x%lX, Len=%lu).\r\n", write_addr, len);
		return 0;
	}
	// 시작 주소 정렬 확인
	if (write_addr % FLASH_WRITE_UNIT_BYTES != 0)
	{
		DM("[FWU] Warning: Write address 0x%lX is not aligned to %d bytes.\r\n", write_addr, FLASH_WRITE_UNIT_BYTES);
		return 0;
	}
	return 1; // 유효
}

/**
 * @brief 지정된 시작 주소의 플래시 섹터(128KB) 지우기
 * @note 입력 주소는 CPU 주소 (0x08xxxxxx)
 */
FLASH_StatusTypeDef FLASH_IF_EraseSector(uint32_t sector_start_addr)
{
	HAL_StatusTypeDef status = HAL_ERROR;
	FLASH_EraseInitTypeDef EraseInitStruct = { 0 };
	uint32_t SectorError = 0;
	uint32_t sector_index_in_bank = 0;

	// 주소 유효성 검사 (섹터 시작 주소 & 정렬)
	if (sector_start_addr % FLASH_SECTOR_SIZE != 0)
	{
		DM("[FWU] Error: Erase address 0x%lX is not a sector start address.\r\n", sector_start_addr);
		return FLASH_IF_PARAM_ERROR;
	}

	// --- 지울 물리적 뱅크 및 섹터 인덱스 계산 ---
	if (sector_start_addr >= FLASH_BANK2_START_ADDR && sector_start_addr < (FLASH_BANK2_START_ADDR + FLASH_BANK_SIZE))
	{
		sector_index_in_bank = (sector_start_addr - FLASH_BANK2_START_ADDR) / FLASH_SECTOR_SIZE;
	}
	else
	{
		DM("[FWU] Error: Erase address 0x%lX invalid for inactive Bank 2.\r\n", sector_start_addr);
		return FLASH_IF_PARAM_ERROR;
	}


	// --- 임계 영역 시작 ---
	taskENTER_CRITICAL();

	status = HAL_FLASH_Unlock();
	if (status != HAL_OK)
	{
		DM("[FWU] Error: Failed to unlock Flash! Status: %d\r\n", status);
		taskEXIT_CRITICAL();
		return FLASH_IF_ERASE_ERROR;
	}

	// 캐시 관리 (필요시)
    SCB_InvalidateICache();

	EraseInitStruct.TypeErase = FLASH_TYPEERASE_SECTORS;
	EraseInitStruct.Banks = FLASH_BANK_2;
	EraseInitStruct.Sector = sector_index_in_bank;
	EraseInitStruct.NbSectors = 1;
	EraseInitStruct.VoltageRange = FLASH_VOLTAGE_RANGE;

	DM("[FWU] Erasing Sector %lu (CPU Addr: 0x%lX)...\r\n", sector_index_in_bank, sector_start_addr);
	status = HAL_FLASHEx_Erase(&EraseInitStruct, &SectorError);

	HAL_FLASH_Lock();

	// 캐시 관리 (필요시)
    SCB_InvalidateICache();

	taskEXIT_CRITICAL();// --- 임계 영역 종료 ---

	if (status == HAL_OK)
	{
		DM("[FWU] Sector erase completed.\r\n");
		return FLASH_IF_OK;
	}
	else
	{
		DM("[FWU] Error: Sector erase failed! Status: %d, Error Sector Index: %lu\r\n", status, SectorError);
		return FLASH_IF_ERASE_ERROR;
	}
}

/**
 * @brief 플래시 메모리에 데이터 쓰기 (32바이트 단위)
 */
FLASH_StatusTypeDef FLASH_IF_WriteData(uint32_t dest_addr, uint8_t *data, uint32_t data_size)
{
	if (data == NULL || data_size == 0)
	{
		DM("[FWU] Error: Invalid parameters for WriteData.\r\n");
		return FLASH_IF_PARAM_ERROR;
	}
	if (dest_addr % FLASH_WRITE_UNIT_BYTES != 0)
	{
		DM("[FWU] Error: WriteData destination address 0x%lX is not aligned.\r\n", dest_addr);
		return FLASH_IF_PARAM_ERROR;
	}
	HAL_StatusTypeDef status = HAL_OK;
	uint32_t current_write_addr = dest_addr;
	uint32_t bytes_processed = 0;
	uint8_t write_buffer[FLASH_WRITE_UNIT_BYTES] __attribute__((aligned(4)));
	DM("[FWU] Writing %lu bytes to Flash starting at 0x%lX...\r\n", data_size, dest_addr);
	taskENTER_CRITICAL();
	if (HAL_FLASH_Unlock() != HAL_OK)
	{
		DM("[FWU] Error: Failed to unlock Flash for writing!\r\n");
		taskEXIT_CRITICAL();
		return FLASH_IF_WRITE_ERROR;
	}
	// Cache Maintenance (Pre)
    SCB_InvalidateICache();
	while (bytes_processed < data_size)
	{
		uint32_t bytes_to_copy = MIN(FLASH_WRITE_UNIT_BYTES, data_size - bytes_processed);
		memset(write_buffer, 0xFF, FLASH_WRITE_UNIT_BYTES);
		memcpy(write_buffer, data + bytes_processed, bytes_to_copy);
		uint64_t buffer_addr_64 = (uint64_t)write_buffer;
		status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_FLASHWORD, current_write_addr, buffer_addr_64);
		if (status != HAL_OK)
		{
			DM("[FWU] Error: Flash program failed at 0x%lX! Status: %d\r\n", current_write_addr, status);
			HAL_FLASH_Lock();
			taskEXIT_CRITICAL();
			return FLASH_IF_WRITE_ERROR;
		}
		current_write_addr += FLASH_WRITE_UNIT_BYTES;
		bytes_processed += bytes_to_copy; /* Or += FLASH_WRITE_UNIT_BYTES if loop uses aligned size */
	}
	HAL_FLASH_Lock();
	// Cache Maintenance (Post)
    SCB_InvalidateICache();
	taskEXIT_CRITICAL();
	DM("[FWU] Verifying written data...\r\n");
	if (memcmp((void*) dest_addr, data, data_size) != 0)
	{
		DM("[FWU] Error: Flash verification failed!\r\n");
		return FLASH_IF_WRITE_ERROR;
	}
	DM("[FWU] Flash verification successful.\r\n");
	DM("[FWU] Flash write successful for %lu bytes at 0x%lX.\r\n", data_size, dest_addr);
	return FLASH_IF_OK;
}

/**
 * @brief 옵션 바이트를 수정하여 뱅크 스왑을 설정하고 시스템 리셋
 */
void FLASH_IF_TriggerBankSwapAndReboot(void)
{
	HAL_StatusTypeDef status;
	FLASH_OBProgramInitTypeDef OBInit;
	DM("[FWU] !!! Attempting to configure bank swap and reboot NOW !!!\r\n");
	DM("[FWU] !!! This operation is critical and may brick the device if incorrect !!!\r\n");
	osDelay(50);
	taskENTER_CRITICAL();
	status = HAL_FLASH_Unlock();
	if (status != HAL_OK)
	{
		DM("[FWU] Error: Failed to unlock Flash for OB! Aborting swap.\r\n");
		taskEXIT_CRITICAL();
		return;
	}
	status = HAL_FLASH_OB_Unlock();
	if (status != HAL_OK)
	{
		DM("[FWU] Error: Failed to unlock Option Bytes! Aborting swap.\r\n");
		HAL_FLASH_Lock();
		taskEXIT_CRITICAL();
		return;
	}

    /* Get the configuration of Dual Bank status in FLASH_BANK_1 */
    OBInit.Banks = FLASH_BANK_1;
	DM("[FWU] Flash and Option Bytes Unlocked.\r\n");
	HAL_FLASHEx_OBGetConfig(&OBInit);
	DM("[FWU] Current USER Option Bytes: 0x%08lX\r\n", OBInit.USERConfig);
	OBInit.OptionType = OPTIONBYTE_USER;
	OBInit.USERType = OB_USER_SWAP_BANK;
    if ((OBInit.USERConfig & OB_SWAP_BANK_ENABLE) == OB_SWAP_BANK_DISABLE)
	{
		OBInit.USERConfig |= OB_SWAP_BANK_ENABLE;
		DM("[FWU] Setting Boot Bank to Bank 1 (Disabling SWAP_BANK).\r\n");
	}
	else
	{
		OBInit.USERConfig &= ~OB_SWAP_BANK_ENABLE;
		DM("[FWU] Setting Boot Bank to Bank 2 (Enabling SWAP_BANK).\r\n");
	}
	DM("[FWU] Programming Option Bytes...\r\n");
	status = HAL_FLASHEx_OBProgram(&OBInit);
	if (status != HAL_OK)
	{
		uint32_t ec = HAL_FLASH_GetError();
		DM("[FWU] Error: Failed to program Option Bytes! Status: %d, Flash Error: 0x%lX\r\n", status, ec);
		HAL_FLASH_OB_Lock();
		HAL_FLASH_Lock();
		taskEXIT_CRITICAL();
		return;
	}
	else
	{
		DM("[FWU] Option Bytes programmed successfully. Launching Option Byte loading...\r\n");
		DM("[FWU] System should reset shortly...\r\n");
		osDelay(500);
		status = HAL_FLASH_OB_Launch();
		
		// HAL_FLASH_OB_Lock();
		// HAL_FLASH_Lock();

		SCB_InvalidateICache();

		DM("[FWU] Warning/Info: HAL_FLASH_OB_Launch status: %d. Triggering System Reset NOW.\r\n", status);
		osDelay(500);
		NVIC_SystemReset();
		DM("[FWU] Error: System did not reset!\r\n");
		while (1);
	}
}
