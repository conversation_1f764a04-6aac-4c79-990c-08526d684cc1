/**
******************************************************************************
* @file      NavModem.c
* <AUTHOR>
* @date      2024-01-05
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "model.h"
#include "TargetBoard.h"
#include "Comm.h"

char *pg_NavModem_Test_Id = "AA00";
char *pg_NavModem_Test_Msg = "0123456789 ABCDEFGHIJKLMNOPQRSTUVWXYZ ?:., -()'=/+";

sNavtexMsg gNavtexMsg_518KHz = {0};
sNavtexMsg gNavtexMsg_490KHz = {0};
sNavtexMsg gNavtexMsg_42095KHz = {0};
sNavtexRx gNavtexRx_518KHz = {0};
sNavtexRx gNavtexRx_490KHz = {0};
sNavtexRx gNavtexRx_42095KHz = {0};

sNavtexTx gNavtexTx = {0};

u8 gNavtexDebugMsgBuffer[10240] __attribute__((section(".UartDmaSection")));

void NavModem_Init(void)
{
    memset(&gNavtexMsg_518KHz, 0x00, sizeof(sNavtexMsg));
    memset(&gNavtexMsg_490KHz, 0x00, sizeof(sNavtexMsg));
    memset(&gNavtexMsg_42095KHz, 0x00, sizeof(sNavtexMsg));
    memset(&gNavtexRx_518KHz, 0x00, sizeof(sNavtexRx));
    memset(&gNavtexRx_490KHz, 0x00, sizeof(sNavtexRx));
    memset(&gNavtexRx_42095KHz, 0x00, sizeof(sNavtexRx));
    memset(&gNavtexTx, 0x00, sizeof(sNavtexTx));
    memset(&gNavtexDebugMsgBuffer, 0x00, sizeof(u8)*10240);
}

u8 CCIR476_Decode(u8 str, sNavtexRx *pNavRx)
{
    u8 ret = 0xFF;

    switch(str)
    {
        case 0x07:   ret = '\r';                return ret; break;
        case 0x13:   ret = '\n';                return ret; break;
        case 0x23:   ret = ' ';                 return ret; break;
        case 0x25:   ret = LATTERS_MODE;        return ret; break;  // Autoset Mode = Letters
        case 0x49:   ret = FIGURES_MODE;        return ret; break;  // Autoset Mode = Figures
        case 0x4c:   ret = IDLE_SIGNAL_B;       return ret; break;  // idle signal 
        case 0x70:   ret = PHASING_SIGNAL_1;    return ret; break;  // Phasing Signal 1, idle signal A
        case 0x19:   ret = PHASING_SIGNAL_2;    return ret; break;  // Phasing Signal 2
    }

    switch (pNavRx->CCIR_Mode)
    {
        case LATTERS_MODE: // Autoset Mode = Letters
            switch(str)
            {
                case 0x38:   ret = 'A'; break;
                case 0x0d:   ret = 'B'; break;
                case 0x62:   ret = 'C'; break;
                case 0x2c:   ret = 'D'; break;
                case 0x29:   ret = 'E'; break;
                case 0x64:   ret = 'F'; break;
                case 0x4a:   ret = 'G'; break;
                case 0x16:   ret = 'H'; break;
                case 0x32:   ret = 'I'; break;
                case 0x68:   ret = 'J'; break;
                case 0x61:   ret = 'K'; break;
                case 0x1a:   ret = 'L'; break;
                case 0x46:   ret = 'M'; break;
                case 0x26:   ret = 'N'; break;
                case 0x0e:   ret = 'O'; break;
                case 0x52:   ret = 'P'; break;
                case 0x51:   ret = 'Q'; break;
                case 0x2a:   ret = 'R'; break;
                case 0x34:   ret = 'S'; break;
                case 0x0b:   ret = 'T'; break;
                case 0x31:   ret = 'U'; break;
                case 0x43:   ret = 'V'; break;
                case 0x58:   ret = 'W'; break;
                case 0x45:   ret = 'X'; break;
                case 0x54:   ret = 'Y'; break;
                case 0x1c:   ret = 'Z'; break;
            }
        break;

        case FIGURES_MODE: // Autoset Mode = Figures
            switch(str)
            {
                case 0x52:    ret = '0'; break;
                case 0x51:    ret = '1'; break;
                case 0x58:    ret = '2'; break;
                case 0x29:    ret = '3'; break;
                case 0x2a:    ret = '4'; break;
                case 0x0b:    ret = '5'; break;
                case 0x54:    ret = '6'; break;
                case 0x31:    ret = '7'; break;
                case 0x32:    ret = '8'; break;
                case 0x0e:    ret = '9'; break;
                case 0x34:    ret = '\''; break;
                case 0x64:    ret = '!'; break;
                case 0x62:    ret = ':'; break;
                case 0x61:    ret = '('; break;
                case 0x4a:    ret = '&'; break;
                case 0x46:    ret = '.'; break;
                case 0x45:    ret = '/'; break;
                case 0x43:    ret = '='; break;
                case 0x38:    ret = '-'; break;
                case 0x2c:    ret = '$'; break;
                case 0x26:    ret = ','; break;
                case 0x1c:    ret = '+'; break;
                case 0x1a:    ret = ')'; break;
                case 0x16:    ret = '#'; break;
                case 0x0d:    ret = '?'; break;
            }
        break;
    }
    return ret;
}

u8 CCIR476_Encode(u8 str, sNavtexTx *pNavTx)
{
    u8 ret = 0xFF;

    switch(str)
    {
        case '\r'            :   ret = 0x07;    return ret; break;
        case '\n'            :   ret = 0x13;    return ret; break;
        case ' '             :   ret = 0x23;    return ret; break;
        case LATTERS_MODE    :   ret = 0x25;    return ret; break;  // Autoset Mode = Letters
        case FIGURES_MODE    :   ret = 0x49;    return ret; break;  // Autoset Mode = Figures
        case IDLE_SIGNAL_B   :   ret = 0x4c;    return ret; break;  // idle signal 
        case PHASING_SIGNAL_1:   ret = 0x70;    return ret; break;  // Phasing Signal 1, idle signal A
        case PHASING_SIGNAL_2:   ret = 0x19;    return ret; break;  // Phasing Signal 2
    }

    switch (pNavTx->CCIR_Mode)
    {
        case LATTERS_MODE: // Autoset Mode = Letters
            switch(str)
            {
                case 'A':   ret = 0x38; break;
                case 'B':   ret = 0x0d; break;
                case 'C':   ret = 0x62; break;
                case 'D':   ret = 0x2c; break;
                case 'E':   ret = 0x29; break;
                case 'F':   ret = 0x64; break;
                case 'G':   ret = 0x4a; break;
                case 'H':   ret = 0x16; break;
                case 'I':   ret = 0x32; break;
                case 'J':   ret = 0x68; break;
                case 'K':   ret = 0x61; break;
                case 'L':   ret = 0x1a; break;
                case 'M':   ret = 0x46; break;
                case 'N':   ret = 0x26; break;
                case 'O':   ret = 0x0e; break;
                case 'P':   ret = 0x52; break;
                case 'Q':   ret = 0x51; break;
                case 'R':   ret = 0x2a; break;
                case 'S':   ret = 0x34; break;
                case 'T':   ret = 0x0b; break;
                case 'U':   ret = 0x31; break;
                case 'V':   ret = 0x43; break;
                case 'W':   ret = 0x58; break;
                case 'X':   ret = 0x45; break;
                case 'Y':   ret = 0x54; break;
                case 'Z':   ret = 0x1c; break;
            }
        break;

        case FIGURES_MODE: // Autoset Mode = Figures
            switch(str)
            {
                case '0':    ret = 0x52; break;
                case '1':    ret = 0x51; break;
                case '2':    ret = 0x58; break;
                case '3':    ret = 0x29; break;
                case '4':    ret = 0x2a; break;
                case '5':    ret = 0x0b; break;
                case '6':    ret = 0x54; break;
                case '7':    ret = 0x31; break;
                case '8':    ret = 0x32; break;
                case '9':    ret = 0x0e; break;
                case '\'':   ret = 0x34; break;
                case '!':    ret = 0x64; break;
                case ':':    ret = 0x62; break;
                case '(':    ret = 0x61; break;
                case '&':    ret = 0x4a; break;
                case '.':    ret = 0x46; break;
                case '/':    ret = 0x45; break;
                case '=':    ret = 0x43; break;
                case '-':    ret = 0x38; break;
                case '$':    ret = 0x2c; break;
                case ',':    ret = 0x26; break;
                case '+':    ret = 0x1c; break;
                case ')':    ret = 0x1a; break;
                case '#':    ret = 0x16; break;
                case '?':    ret = 0x0d; break;
            }
        break;
    }
    return ret;
}

u8 CCIR476_Mode_Check(u8 str, sNavtexTx *pNavTx)
{
    u8 ret = pNavTx->CCIR_Mode;

    switch (str)
    {
        case 'A': ret = LATTERS_MODE; break;
        case 'B': ret = LATTERS_MODE; break;
        case 'C': ret = LATTERS_MODE; break;
        case 'D': ret = LATTERS_MODE; break;
        case 'E': ret = LATTERS_MODE; break;
        case 'F': ret = LATTERS_MODE; break;
        case 'G': ret = LATTERS_MODE; break;
        case 'H': ret = LATTERS_MODE; break;
        case 'I': ret = LATTERS_MODE; break;
        case 'J': ret = LATTERS_MODE; break;
        case 'K': ret = LATTERS_MODE; break;
        case 'L': ret = LATTERS_MODE; break;
        case 'M': ret = LATTERS_MODE; break;
        case 'N': ret = LATTERS_MODE; break;
        case 'O': ret = LATTERS_MODE; break;
        case 'P': ret = LATTERS_MODE; break;
        case 'Q': ret = LATTERS_MODE; break;
        case 'R': ret = LATTERS_MODE; break;
        case 'S': ret = LATTERS_MODE; break;
        case 'T': ret = LATTERS_MODE; break;
        case 'U': ret = LATTERS_MODE; break;
        case 'V': ret = LATTERS_MODE; break;
        case 'W': ret = LATTERS_MODE; break;
        case 'X': ret = LATTERS_MODE; break;
        case 'Y': ret = LATTERS_MODE; break;
        case 'Z': ret = LATTERS_MODE; break;

        case '0':  ret = FIGURES_MODE; break;
        case '1':  ret = FIGURES_MODE; break;
        case '2':  ret = FIGURES_MODE; break;
        case '3':  ret = FIGURES_MODE; break;
        case '4':  ret = FIGURES_MODE; break;
        case '5':  ret = FIGURES_MODE; break;
        case '6':  ret = FIGURES_MODE; break;
        case '7':  ret = FIGURES_MODE; break;
        case '8':  ret = FIGURES_MODE; break;
        case '9':  ret = FIGURES_MODE; break;
        case '\'': ret = FIGURES_MODE; break;
        case '!':  ret = FIGURES_MODE; break;
        case ':':  ret = FIGURES_MODE; break;
        case '(':  ret = FIGURES_MODE; break;
        case '&':  ret = FIGURES_MODE; break;
        case '.':  ret = FIGURES_MODE; break;
        case '/':  ret = FIGURES_MODE; break;
        case '=':  ret = FIGURES_MODE; break;
        case '-':  ret = FIGURES_MODE; break;
        case '$':  ret = FIGURES_MODE; break;
        case ',':  ret = FIGURES_MODE; break;
        case '+':  ret = FIGURES_MODE; break;
        case ')':  ret = FIGURES_MODE; break;
        case '#':  ret = FIGURES_MODE; break;
        case '?':  ret = FIGURES_MODE; break;
    }
    return ret;
}

u8 NavMessage_Id_A_to_Z_Check(u8 character)
{
    u8 ret = 0xFF;

    switch(character)
    {
        case 'A': ret = 0x01; break;
        case 'B': ret = 0x01; break;
        case 'C': ret = 0x01; break;
        case 'D': ret = 0x01; break;
        case 'E': ret = 0x01; break;
        case 'F': ret = 0x01; break;
        case 'G': ret = 0x01; break;
        case 'H': ret = 0x01; break;
        case 'I': ret = 0x01; break;
        case 'J': ret = 0x01; break;
        case 'K': ret = 0x01; break;
        case 'L': ret = 0x01; break;
        case 'M': ret = 0x01; break;
        case 'N': ret = 0x01; break;
        case 'O': ret = 0x01; break;
        case 'P': ret = 0x01; break;
        case 'Q': ret = 0x01; break;
        case 'R': ret = 0x01; break;
        case 'S': ret = 0x01; break;
        case 'T': ret = 0x01; break;
        case 'U': ret = 0x01; break;
        case 'V': ret = 0x01; break;
        case 'W': ret = 0x01; break;
        case 'X': ret = 0x01; break;
        case 'Y': ret = 0x01; break;
        case 'Z': ret = 0x01; break;
    }

    return ret;
}

u8 NavMessage_Id_0_to_9_Check(u8 character)
{
    u8 ret = 0xFF;

    switch(character)
    {
        case '0': ret = 0x01; break;
        case '1': ret = 0x01; break;
        case '2': ret = 0x01; break;
        case '3': ret = 0x01; break;
        case '4': ret = 0x01; break;
        case '5': ret = 0x01; break;
        case '6': ret = 0x01; break;
        case '7': ret = 0x01; break;
        case '8': ret = 0x01; break;
        case '9': ret = 0x01; break;
    }

    return ret;
}

u8 NavMessage_StartPhasingDetect(sNavtexRx *pNavRx, tFskRxMDM *pFSK)
{
    int tmp = 0;
    u8 ret = 0xFF;

    do
    {
        pNavRx->rcvData &= 0x7F;
        tmp = ReadFskRxBitData(pFSK);
        if(tmp == -1)
        {
            return ret;
        }
        else
        {
            pNavRx->rcvData |= ((tmp << 6) & 0x40);
            if(pNavRx->rcvData == RAW_PHASING_SIGNAL_2)  // PHASING_SIGNAL_2
            {
                ret = 1;
                pNavRx->rcvData = 0;

                return ret;
            }
            pNavRx->rcvData >>= 1;
        }
    } while (tmp != -1);

    return ret;
}

void NavMessage_RxDataAdd(sNavtexRx *pNavRx, tFskRxMDM *pFSK)
{
    do
    {
        pNavRx->rcvData &= 0x7F;
        pNavRx->tmp_data = ReadFskRxBitData(pFSK);
        if(pNavRx->tmp_data == -1)
        {
            return;
        }
        else
        {
            pNavRx->rcvData |= ((pNavRx->tmp_data << 6) & 0x40);
            pNavRx->Bit_Cnt += 1;
            if(pNavRx->Bit_Cnt >= 7)
            {
                pNavRx->ByteBuffer[pNavRx->ByteHead_Cnt] = pNavRx->rcvData;
                pNavRx->ByteHead_Cnt += 1;
                if(pNavRx->ByteHead_Cnt >= NAVTEX_BYTE_BUFFER_SIZE)
                {
                    pNavRx->ByteHead_Cnt = 0;
                }
                pNavRx->Bit_Cnt = 0;
                pNavRx->rcvData = 0x00;
            }
            pNavRx->rcvData >>= 1;
        }
    } while(pNavRx->tmp_data != -1);
}

u8 NavMessage_RxDataRead(sNavtexRx *pNavRx, tFskRxMDM *pFSK)
{
    u8 ret = 0xFF;
    
    if(pNavRx->ByteTail_Cnt == pNavRx->ByteHead_Cnt)
    {
        return ret;
    }

    ret = pNavRx->ByteBuffer[pNavRx->ByteTail_Cnt];
    pNavRx->ByteTail_Cnt += 1;
    if(pNavRx->ByteTail_Cnt >= NAVTEX_BYTE_BUFFER_SIZE)
    {
        pNavRx->ByteTail_Cnt = 0;
    }

    return ret;
}

void NavMessage_RecvBufferAdd(sNavtexRx *pNavRx, u8 data)
{
    pNavRx->RecvBuffer[pNavRx->Recv_Cnt] = data;
    pNavRx->Recv_Cnt += 1;

    if(pNavRx->Recv_Cnt >= NAVTEX_MESSAGE_RECV_BUFFER_SIZE)
    {
        pNavRx->Recv_Cnt = 0;
    }
}

void NavMessage_RecvBufferClear(sNavtexRx *pNavRx)
{
    pNavRx->Recv_Cnt = 0;
}

void NavMessage_FecComplitedBufferAdd(sNavtexRx *pNavRx, u8 data)
{
    if(data == LATTERS_MODE || data == FIGURES_MODE)
    {
        pNavRx->CCIR_Mode = data;
        return;
    }
    pNavRx->FecComplitedBuffer[pNavRx->FCHead_Cnt] = data;
    pNavRx->FCHead_Cnt += 1;

    if(pNavRx->FCHead_Cnt >= NAVTEX_MESSAGE_FEC_BUFFER_SIZE)
    {
        pNavRx->FCHead_Cnt = 0;
    }
}

u8 NavMessage_FecComlitedBufferRead(sNavtexRx *pNavRx)
{
    u8 ret = 0xFF;

    if(pNavRx->FCTail_Cnt == pNavRx->FCHead_Cnt)
    {
        return ret;
    }

    ret = pNavRx->FecComplitedBuffer[pNavRx->FCTail_Cnt];
    pNavRx->FCTail_Cnt += 1;

    if(pNavRx->FCTail_Cnt >= NAVTEX_MESSAGE_FEC_BUFFER_SIZE)
    {
        pNavRx->FCTail_Cnt = 0;
    }

    return ret;
}

void NavMessage_FecSort(sNavtexRx *pNavRx)
{
    u8 rx = 0;
    u8 dx = 0;
    u8 ccir476_rx = 0;
    u8 ccir476_dx = 0;
    u32 Check_Cnt = 0;

    if(pNavRx->Recv_Cnt >= 6)
    {
        if((pNavRx->Recv_Cnt % 2) == 0)
        {
            Check_Cnt = pNavRx->Recv_Cnt - 1;

            rx = pNavRx->RecvBuffer[Check_Cnt];       // station 2
            dx = pNavRx->RecvBuffer[(Check_Cnt - 5)]; // station 1

            pNavRx->Raw_DX[pNavRx->RawStation_Cnt] = dx;
            pNavRx->Raw_RX[pNavRx->RawStation_Cnt] = rx;
            pNavRx->RawStation_Cnt += 1;
            if(pNavRx->RawStation_Cnt >= NAVTEX_MESSAGE_FEC_BUFFER_SIZE)
            {
                pNavRx->RawStation_Cnt = 0;
            }

            ccir476_dx = CCIR476_Decode(dx, pNavRx);
            ccir476_rx = CCIR476_Decode(rx, pNavRx);

            pNavRx->DX[pNavRx->Station_Cnt] = ccir476_dx;
            pNavRx->RX[pNavRx->Station_Cnt] = ccir476_rx;
            pNavRx->Station_Cnt += 1;
            if(pNavRx->Station_Cnt >= NAVTEX_MESSAGE_FEC_BUFFER_SIZE)
            {
                pNavRx->Station_Cnt = 0;
            }

            if(ccir476_dx == 0xFF && ccir476_rx == 0xFF)
            {
                NavMessage_FecComplitedBufferAdd(pNavRx, CHARACTER_ERROR);
            }
            else
            {
                if(ccir476_dx == 0xFF || ccir476_rx == 0xFF)
                {
                    if(ccir476_dx != 0xFF)
                    {
                        if(ccir476_dx == PHASING_SIGNAL_2 && ccir476_rx == 0xFF)
                        {
                            //NavMessage_FecComplitedBufferAdd(pNavRx, CHARACTER_ERROR);
                            NavMessage_FecComplitedBufferAdd(pNavRx, START_PHASING);
                        }
                        else
                        {
                            NavMessage_FecComplitedBufferAdd(pNavRx, ccir476_dx);
                        }
                    }
                    else if(ccir476_rx != 0xFF)
                    {
                        if(ccir476_rx == PHASING_SIGNAL_1 && ccir476_dx == 0xFF)
                        {
                            //NavMessage_FecComplitedBufferAdd(pNavRx, CHARACTER_ERROR);
                            NavMessage_FecComplitedBufferAdd(pNavRx, START_PHASING);
                        }
                        else
                        {
                            NavMessage_FecComplitedBufferAdd(pNavRx, ccir476_rx);
                        }
                    }
                }
                else
                {
                    if(ccir476_dx == ccir476_rx)
                    {
                        NavMessage_FecComplitedBufferAdd(pNavRx, ccir476_dx);
                    }
                    else
                    {
                        if(ccir476_dx == PHASING_SIGNAL_2 && ccir476_rx == PHASING_SIGNAL_1)
                        {
                            NavMessage_FecComplitedBufferAdd(pNavRx, START_PHASING);
                        }
                        else if(ccir476_dx == PHASING_SIGNAL_1 && ccir476_rx == PHASING_SIGNAL_2)
                        {
                            NavMessage_FecComplitedBufferAdd(pNavRx, START_PHASING);
                        }
                        else if(ccir476_dx == IDLE_SIGNAL_A && ccir476_rx == IDLE_SIGNAL_B)
                        {
                            NavMessage_FecComplitedBufferAdd(pNavRx, END_PHASING);
                        }
                        else if(ccir476_dx == IDLE_SIGNAL_B && ccir476_rx == IDLE_SIGNAL_A)
                        {
                            NavMessage_FecComplitedBufferAdd(pNavRx, END_PHASING);
                        }
                        else
                        {
                            NavMessage_FecComplitedBufferAdd(pNavRx, CHARACTER_ERROR);
                        }
                    }
                }
            }
        }
    }
}

u8 NavMessage_ZCZC_Detect(u8 *pArr)
{
    u8 ret = 0xFF;
    
    if     (pArr[0] == 'Z' && pArr[1] == 'C' && pArr[2] == 'Z' && pArr[3] == 'C')                           { ret = 0x01; }
    else if(pArr[0] == 'Z' && pArr[1] == 'C' && pArr[2] == 'Z' && pArr[3] == CHARACTER_ERROR)               { ret = 0x01; }
    else if(pArr[0] == 'Z' && pArr[1] == 'C' && pArr[2] == CHARACTER_ERROR && pArr[3] == 'C')               { ret = 0x01; }
    else if(pArr[0] == 'Z' && pArr[1] == 'C' && pArr[2] == CHARACTER_ERROR && pArr[3] == CHARACTER_ERROR)   { ret = 0x01; }
    else if(pArr[0] == 'Z' && pArr[1] == CHARACTER_ERROR && pArr[2] == 'Z' && pArr[3] == 'C')               { ret = 0x01; }
    else if(pArr[0] == 'Z' && pArr[1] == CHARACTER_ERROR && pArr[2] == 'Z' && pArr[3] == CHARACTER_ERROR)   { ret = 0x01; }
    else if(pArr[0] == 'Z' && pArr[1] == CHARACTER_ERROR && pArr[2] == CHARACTER_ERROR && pArr[3] == 'C')   { ret = 0x01; }
    else if(pArr[0] == CHARACTER_ERROR && pArr[1] == 'C' && pArr[2] == 'Z' && pArr[3] == 'C')               { ret = 0x01; }
    else if(pArr[0] == CHARACTER_ERROR && pArr[1] == 'C' && pArr[2] == 'Z' && pArr[3] == CHARACTER_ERROR)   { ret = 0x01; }
    else if(pArr[0] == CHARACTER_ERROR && pArr[1] == 'C' && pArr[2] == CHARACTER_ERROR && pArr[3] == 'C')   { ret = 0x01; }
    //else if(pArr[0] == CHARACTER_ERROR && pArr[1] == CHARACTER_ERROR && pArr[2] == 'Z' && pArr[3] == 'C')   { ret = 0x01; }  //240815 bugfix dmj ex)f,f,f,f,f,f,f,*,*,f,f,f,f,f,f,f,*,f,f,f,*,f,f,f,f,f,f,f,*,f,f,*,*,Z,C,Z,C, ,H,A,5,0,
    
    return ret;
}

u8 NavMessage_NNNN_Detect(u8 *pArr)
{
    u8 ret = 0xFF;
    
    if     (pArr[0] == 'N' && pArr[1] == 'N' && pArr[2] == 'N' && pArr[3] == 'N')                           { ret = 0x01; }
    else if(pArr[0] == 'N' && pArr[1] == 'N' && pArr[2] == 'N' && pArr[3] == CHARACTER_ERROR)               { ret = 0x01; }
    else if(pArr[0] == 'N' && pArr[1] == 'N' && pArr[2] == CHARACTER_ERROR && pArr[3] == 'N')               { ret = 0x01; }
    else if(pArr[0] == 'N' && pArr[1] == CHARACTER_ERROR && pArr[2] == 'N' && pArr[3] == 'N')               { ret = 0x01; }
    //else if(pArr[0] == CHARACTER_ERROR && pArr[1] == 'N' && pArr[2] == 'N' && pArr[3] == 'N')               { ret = 0x01; }
    
    return ret;
}

u8 NavMessage_End_rnn_Detect(u8 *pArr)
{
    u8 ret = 0xFF;

    if(pArr[0] == '\r' && pArr[1] == '\n' && pArr[2] == '\n') { ret = 0x01; }

    return ret;
}

u8 NavMessage_End_Phasing_Detect(u8 *pBuffer, u32 *pCnt, u8 InData)
{
    int i = 0;
    int endphasing_cnt = 0;

    pBuffer[*pCnt] = InData;
    *pCnt += 1;
    if(*pCnt >= NAVTEX_MESSAGE_END_PHASING_BUFFER_SIZE)
    {
        *pCnt = 0;
    }

    for(i=0; i<NAVTEX_MESSAGE_END_PHASING_BUFFER_SIZE; i++)
    {
        if(pBuffer[i] == END_PHASING)
        {
            endphasing_cnt += 1;
        }
    }
    
    if(endphasing_cnt == NAVTEX_MESSAGE_END_PHASING_BUFFER_SIZE)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

u8 NavMessage_ErrorOver_Detect(u8 *pBuffer, u32 *pCnt, u8 InData)
{
    int i = 0;
    u32 error_rate = 0;
    u32 error_cnt = 0;
    u32 character_cnt = 0;

    pBuffer[*pCnt] = InData;
    *pCnt += 1;
    if(*pCnt >= NAVTEX_MESSAGE_33_PER_FOR_5_SEC_ERROR_BUFFER_SIZE)
    {
        *pCnt = 0;
    }

    for(i=0; i<NAVTEX_MESSAGE_33_PER_FOR_5_SEC_ERROR_BUFFER_SIZE; i++)
    {
        character_cnt += 1;

        if(pBuffer[i] == CHARACTER_ERROR)
        {
            error_cnt += 1;
        }
    }
    error_rate = (error_cnt * 1000) / character_cnt;
    
    if(error_rate > 330)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

u8 NavMessage_ReStartPhasing_Detect(u8 *pBuffer, u32 *pCnt, u8 InData)
{
    int i = 0;
    u32 startphasing_cnt = 0;

    pBuffer[*pCnt] = InData;
    *pCnt += 1;
    if(*pCnt >= NAVTEX_MESSAGE_RE_START_PHASING_DETECT_BUFFER_SIZE)
    {
        *pCnt = 0;
    }

    for(i=0; i<NAVTEX_MESSAGE_RE_START_PHASING_DETECT_BUFFER_SIZE; i++)
    {
        if(pBuffer[i] == START_PHASING)
        {
            startphasing_cnt += 1;
        }
    }

    if(startphasing_cnt == NAVTEX_MESSAGE_RE_START_PHASING_DETECT_BUFFER_SIZE)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

u8 NavMessage_SearchSequence(sNavtexRx *pNavRx, sNavtexMsg *pNavMsg, tFskRxMDM *pFSK)
{
    do
    {
        pNavRx->FecData = NavMessage_FecComlitedBufferRead(pNavRx);
        if(pNavRx->FecData == 0xFF)
        {
        	return RECV_NONE;
        }

        pNavRx->ErrorOver_Detect = NavMessage_ErrorOver_Detect(&pNavRx->ErrorOverDetectBuffer[0], &pNavRx->ErrorOver_Cnt, pNavRx->FecData);
        if(pNavRx->ErrorOver_Detect == 1)
        {
            return RECV_ERROR_CHARACTER_ERROR_OVER;
        }

        pNavRx->EndPhasing_Detect = NavMessage_End_Phasing_Detect(&pNavRx->EndPhasingDetectBuffer[0], &pNavRx->EndPhasing_Cnt, pNavRx->FecData);
        if(pNavRx->EndPhasing_Detect == 1)
        {
            return RECV_ERROR_END_PHASING_BEFORE_NNNN;
        }

        pNavRx->ReStartPhasing_Detect = NavMessage_ReStartPhasing_Detect(&pNavRx->ReStartPhasingDetectBuffer[0], &pNavRx->ReStartPhasing_Cnt, pNavRx->FecData);

        switch(pNavRx->SearchSeq)
        {
            case SEARCH_SEQ_ZCZC:
                pNavRx->Timeout_Cnt += 1;
                if(pNavRx->Timeout_Cnt >= 214)    // 140ms * 214 = 30sec
                {
                    return RECV_ERROR_ZCZC_TIMEOUT;
                }

                pNavRx->TempBuffer[pNavRx->Temp_Cnt] = pNavRx->FecData;
                pNavRx->Temp_Cnt += 1;
                if(pNavRx->Temp_Cnt >= NAVTEX_MESSAGE_FEC_BUFFER_SIZE)
                {
                    return RECV_ERROR_ZCZC_FEC_BUFFER_OVER;
                }
                    
                if(pNavRx->Temp_Cnt >= 4)
                {
                    pNavRx->ZCZC_Detect = NavMessage_ZCZC_Detect(&pNavRx->TempBuffer[pNavRx->Temp_Cnt - 4]);
                    pNavRx->End_Detect_rnn = NavMessage_End_rnn_Detect(&pNavRx->TempBuffer[pNavRx->Temp_Cnt - 4]);
                    pNavRx->End_Detect_NNNN = NavMessage_NNNN_Detect(&pNavRx->TempBuffer[pNavRx->Temp_Cnt - 4]);
                    
                    if(pNavRx->ZCZC_Detect == 1)
                    {
                        pNavRx->SearchSeq = SEARCH_SEQ_ONE_SPACE;
                        pNavRx->Timeout_Cnt = 0;
                        pNavRx->Temp_Cnt = 0;
                        pNavRx->ZCZC_Detect = 0;
                    }
                    else if(pNavRx->End_Detect_rnn == 1 || pNavRx->End_Detect_NNNN == 1)
                    {
                    	return 0xFF;
                    }
                }
            break;

            case SEARCH_SEQ_ONE_SPACE:
                if(pNavRx->ReStartPhasing_Detect == 1)
                {
                    return RECV_ERROR_RE_START_PHASING;
                }

                pNavRx->TempBuffer[pNavRx->Temp_Cnt] = pNavRx->FecData;
                pNavRx->Temp_Cnt += 1;
                if(pNavRx->Temp_Cnt >= NAVTEX_MESSAGE_FEC_BUFFER_SIZE)
                {
                    return RECV_ERROR_ZCZC_FEC_BUFFER_OVER;
                }

                if(pNavRx->Temp_Cnt >= 1)
                {
                    pNavRx->SearchSeq = SEARCH_SEQ_ID;
                    pNavRx->Temp_Cnt = 0;
                }
            break;

            case SEARCH_SEQ_ID:
                if(pNavRx->ReStartPhasing_Detect == 1)
                {
                    return RECV_ERROR_RE_START_PHASING;
                }

                pNavRx->TempBuffer[pNavRx->Temp_Cnt] = pNavRx->FecData;
                pNavRx->Temp_Cnt += 1;
                if(pNavRx->Temp_Cnt >= NAVTEX_MESSAGE_FEC_BUFFER_SIZE)
                {
                    return RECV_ERROR_ZCZC_FEC_BUFFER_OVER;
                }

                if(pNavRx->Temp_Cnt >= 4)
                {
                    pNavMsg->Info.ID[0] = pNavRx->TempBuffer[0];
                    pNavMsg->Info.ID[1] = pNavRx->TempBuffer[1];
                    pNavMsg->Info.ID[2] = pNavRx->TempBuffer[2];
                    pNavMsg->Info.ID[3] = pNavRx->TempBuffer[3];
                    pNavMsg->Info.ID[4] = '\0';

                    if( NavMessage_Id_A_to_Z_Check(pNavMsg->Info.ID[0]) == 1 && NavMessage_Id_A_to_Z_Check(pNavMsg->Info.ID[1]) == 1 &&
                        NavMessage_Id_0_to_9_Check(pNavMsg->Info.ID[2]) == 1 && NavMessage_Id_0_to_9_Check(pNavMsg->Info.ID[3]) == 1 )
                     {
                        pNavRx->SearchSeq = SEARCH_SEQ_CR_LF;
                        pNavRx->Temp_Cnt = 0;

						Internal_Tx_Nav_Msg(pNavMsg, I_COMM_NAV_CMD_PARAM_TYPE_ID, pFSK->dFreqCh, 0, &pNavRx->FecData);
                     }
                     else
                     {
                        return RECV_ERROR_ID_NOT_MATCH;
                     }
                }
            break;

            case SEARCH_SEQ_CR_LF:
                if(pNavRx->ReStartPhasing_Detect == 1)
                {
                    return RECV_ERROR_RE_START_PHASING;
                }

                pNavRx->TempBuffer[pNavRx->Temp_Cnt] = pNavRx->FecData;
                pNavRx->Temp_Cnt += 1;
                if(pNavRx->Temp_Cnt >= NAVTEX_MESSAGE_FEC_BUFFER_SIZE)
                {
                    return RECV_ERROR_ZCZC_FEC_BUFFER_OVER;
                }

                if(pNavRx->Temp_Cnt >= 2)
                {
                    pNavRx->SearchSeq = SERACH_SEQ_MSG;
                    pNavRx->Temp_Cnt = 0;
                }
            break;

            case SERACH_SEQ_MSG:
                if(pNavRx->ReStartPhasing_Detect == 1)
                {
                    return RECV_ERROR_RE_START_PHASING;
                }

                if(pNavRx->FecData != START_PHASING)
                {
                    if(pNavRx->FecData == CHARACTER_ERROR)
                    {
                        pNavRx->ErrorCharLen += 1;
                    }

                    pNavRx->TempBuffer[pNavRx->Temp_Cnt] = pNavRx->FecData;
                    pNavRx->Temp_Cnt += 1;
                    if(pNavRx->Temp_Cnt >= NAVTEX_MESSAGE_FEC_BUFFER_SIZE)
                    {
                        return RECV_ERROR_MSG_FEC_BUFFER_OVER;
                    }

					Internal_Tx_Nav_Msg(pNavMsg, I_COMM_NAV_CMD_PARAM_TYPE_MSG, pFSK->dFreqCh, pNavRx->Temp_Cnt-1, &pNavRx->FecData);

                    if(pNavRx->Temp_Cnt >= 4)
                    {
                        pNavRx->ZCZC_Detect = NavMessage_ZCZC_Detect(&pNavRx->TempBuffer[pNavRx->Temp_Cnt - 4]);
                        pNavRx->End_Detect_rnn = NavMessage_End_rnn_Detect(&pNavRx->TempBuffer[pNavRx->Temp_Cnt - 4]);
                        pNavRx->End_Detect_NNNN = NavMessage_NNNN_Detect(&pNavRx->TempBuffer[pNavRx->Temp_Cnt - 4]);

                        if(pNavRx->ZCZC_Detect == 1)
                        {
                            pNavRx->SearchSeq = SERACH_SEQ_MSG;
                            pNavRx->Temp_Cnt = 0;
                            pNavRx->ErrorCharLen = 0;
                        }

                        if(pNavRx->End_Detect_NNNN == 1)
                        {
                            // memcpy(pNavMsg->Info.ID, &pNavRx->TempBuffer[1], 4);
                            // pNavMsg->Info.ID[4] = ' ';
                            
                            if(pFSK->dFreqCh == FSK_FREQ_INT_518KHZ)        { pNavMsg->Info.Freq = 518;   }
                            else if(pFSK->dFreqCh == FSK_FREQ_LOC_490KHZ)   { pNavMsg->Info.Freq = 490;   }
                            else if(pFSK->dFreqCh == FSK_FREQ_LOC_42095KHZ) { pNavMsg->Info.Freq = 42095; }

                            // upper space 1ea, id 4ea, \r\n 2ea, 
                            // message ...
                            // lower NNNN 4ea, \r\n\r\n 4ea
                            // [7] - upper id 4ea, space 1ea, \r\n 2ea
                            // 11 - 
                            pNavMsg->Msg.Message_Len = (pNavRx->Temp_Cnt - 4);
                            pNavMsg->Info.Total_Character_Len = (pNavRx->Temp_Cnt - 4);
                            pNavMsg->Info.Error_Character_Len = pNavRx->ErrorCharLen;
                            pNavMsg->Info.ErrorRate = (((u32)pNavMsg->Info.Error_Character_Len * 1000) / (u32)pNavMsg->Info.Total_Character_Len); // 0.1% scale
                            memcpy(pNavMsg->Msg.Message, pNavRx->TempBuffer, (sizeof(u8)*(pNavRx->Temp_Cnt - 4)));

                            pNavRx->Temp_Cnt = 0;
                            return RECV_MSG_SUCCESS;
                        }
                    }
                }
            break;
            
            default:
            break;
        }
    } while(pNavRx->FecData != 0xFF);

    return RECV_NONE;
}

void NavtexMessageReceive(sNavtexRx *pNavRx, tFskRxMDM *pFSK, sNavtexMsg *pNavMsg)
{
    switch(pNavRx->Seq)
    {
        case RECV_SEQ_STOP:
            pNavRx->result = NavMessage_StartPhasingDetect(pNavRx, pFSK);
            if(pNavRx->result == 1)
            {
                memset(pNavRx, 0x00, sizeof(sNavtexRx));
                memset(pNavMsg, 0x00, sizeof(sNavtexMsg));

                pNavRx->Seq = RECV_SEQ_PHASING_START_CHECK;
                pNavRx->CCIR_Mode = LATTERS_MODE;

                NavMessage_RecvBufferAdd(pNavRx, RAW_PHASING_SIGNAL_2); // PHASING_SIGNAL_2
                pNavRx->StartPhasingTime_ms += 70;
            }
        break;

        case RECV_SEQ_PHASING_START_CHECK:
            NavMessage_RxDataAdd(pNavRx, pFSK);
            pNavRx->result = NavMessage_RxDataRead(pNavRx, pFSK);
            
            if(pNavRx->result != 0xFF)
            {
                NavMessage_RecvBufferAdd(pNavRx, pNavRx->result);
                pNavRx->StartPhasingTime_ms += 70;
                if(pNavRx->StartPhasingTime_ms == (70*1) && pNavRx->result == RAW_PHASING_SIGNAL_2) {}
                else if(pNavRx->StartPhasingTime_ms == (70*2) && pNavRx->result == RAW_PHASING_SIGNAL_1) {}
                else if(pNavRx->StartPhasingTime_ms == (70*3) && pNavRx->result == RAW_PHASING_SIGNAL_2) {}
                else if(pNavRx->StartPhasingTime_ms == (70*4) && pNavRx->result == RAW_PHASING_SIGNAL_1) {}
                else if(pNavRx->StartPhasingTime_ms == (70*5) && pNavRx->result == RAW_PHASING_SIGNAL_2) {}
                else if(pNavRx->StartPhasingTime_ms == (70*6) && pNavRx->result == RAW_PHASING_SIGNAL_1)
                {
                    pNavRx->Seq = RECV_SEQ_RECEIVE;
					Internal_Tx_Nav_Msg(pNavMsg, I_COMM_NAV_CMD_PARAM_TYPE_START, pFSK->dFreqCh, 0, NULL);
                }
                else
                {
                    pNavRx->Seq = RECV_SEQ_STOP;
                    NavMessage_RecvBufferClear(pNavRx);
                }
            }
        break;

        case RECV_SEQ_RECEIVE:
            NavMessage_RxDataAdd(pNavRx, pFSK);
            pNavRx->result = NavMessage_RxDataRead(pNavRx, pFSK);
            if(pNavRx->result != 0xFF)
            {
                NavMessage_RecvBufferAdd(pNavRx, pNavRx->result);
                NavMessage_FecSort(pNavRx);
            }

            pNavRx->result_2nd = NavMessage_SearchSequence(pNavRx, pNavMsg, pFSK);
            if(pNavRx->result_2nd == RECV_NONE)
            {

            }
            else
            {
                if(pNavRx->result_2nd == RECV_MSG_SUCCESS)
                {
                    Internal_Tx_Nav_Msg(pNavMsg, I_COMM_NAV_CMD_PARAM_TYPE_END, pFSK->dFreqCh, 0, NULL);

#ifdef NAVTEX_MESSAGE_DEBUG_SEND_MSG
                    // ok
                    System_Debug_Tx_Navtex_Message_Log_Data(1, pNavRx, &pNavMsg->Info, &pNavMsg->Msg);
#endif
                }
                else
                {
                    Internal_Tx_Nav_Msg(pNavMsg, I_COMM_NAV_CMD_PARAM_TYPE_ERROR, pFSK->dFreqCh, 0, &pNavRx->result_2nd);
                    
#ifdef NAVTEX_MESSAGE_DEBUG_SEND_MSG
                    // error
                    System_Debug_Tx_Navtex_Message_Log_Data(0, pNavRx, &pNavMsg->Info, &pNavMsg->Msg);
#endif
                }
                memset(pNavRx, 0x00, sizeof(sNavtexRx));
            }
        break;
    }
}

void System_Debug_Tx_Navtex_Message_Log_Data(int mode, sNavtexRx *pNavRx, sMessageComponent *pInfo, sMessageString *pMsg)
{
    pNavRx->debug_i = 0;
    pNavRx->debug_i_add = 0;
    pNavRx->debug_len = 0;
    pNavRx->debug_crc16 = 0;

    gNavtexDebugMsgBuffer[0] = '$';

    pNavRx->debug_i_add = 9;

    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '[';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'D';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'X';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ']';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
    for(pNavRx->debug_i=0; pNavRx->debug_i<pNavRx->Station_Cnt; pNavRx->debug_i++)
    {
        gNavtexDebugMsgBuffer[pNavRx->debug_i+pNavRx->debug_i_add] = pNavRx->DX[pNavRx->debug_i];
    }
    pNavRx->debug_i_add += pNavRx->Station_Cnt;

    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '[';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'R';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'X';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ']';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
    for(pNavRx->debug_i=0; pNavRx->debug_i<pNavRx->Station_Cnt; pNavRx->debug_i++)
    {
        gNavtexDebugMsgBuffer[pNavRx->debug_i+pNavRx->debug_i_add] = pNavRx->RX[pNavRx->debug_i];
    }
    pNavRx->debug_i_add += pNavRx->Station_Cnt;

    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '[';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'F';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'E';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'C';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ']';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
    for(pNavRx->debug_i=0; pNavRx->debug_i<pNavRx->FCHead_Cnt; pNavRx->debug_i++)
    {
        gNavtexDebugMsgBuffer[pNavRx->debug_i+pNavRx->debug_i_add] = pNavRx->FecComplitedBuffer[pNavRx->debug_i];
    }
    pNavRx->debug_i_add += pNavRx->FCHead_Cnt;

    if(mode == 1)
    {
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '[';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'I';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'D';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ']';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        for(pNavRx->debug_i=0; pNavRx->debug_i<4; pNavRx->debug_i++)
        {
            gNavtexDebugMsgBuffer[pNavRx->debug_i+pNavRx->debug_i_add] = pInfo->ID[pNavRx->debug_i];
        }
        pNavRx->debug_i_add += 4;

        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '[';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'D';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'A';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'T';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'A';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ']';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        for(pNavRx->debug_i=0; pNavRx->debug_i<pMsg->Message_Len; pNavRx->debug_i++)
        {
            gNavtexDebugMsgBuffer[pNavRx->debug_i+pNavRx->debug_i_add] = pMsg->Message[pNavRx->debug_i];
        }
        pNavRx->debug_i_add += pMsg->Message_Len;

        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '[';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'F';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'R';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'E';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'Q';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ']';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ((pInfo->Freq) & 0xFF);
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ((pInfo->Freq >> 8) & 0xFF);

        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '[';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'T';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'O';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'T';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'A';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'L';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ']';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ((pInfo->Total_Character_Len) & 0xFF);
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ((pInfo->Total_Character_Len >> 8) & 0xFF);

        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '[';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'E';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'R';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'R';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'O';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'R';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ']';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ((pInfo->Error_Character_Len) & 0xFF);
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ((pInfo->Error_Character_Len >> 8) & 0xFF);

        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '[';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'E';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'R';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'A';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'T';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'E';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ']';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ((pInfo->ErrorRate) & 0xFF);
        gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ((pInfo->ErrorRate >> 8) & 0xFF);
    }

    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\r';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '\n';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = '[';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'E';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'N';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = 'D';
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = ']';
        
    // frame length
    pNavRx->debug_len = pNavRx->debug_i_add - 1;
    gNavtexDebugMsgBuffer[1] = (u8)(pNavRx->debug_len & 0xFF);
    gNavtexDebugMsgBuffer[2] = (u8)((pNavRx->debug_len >> 8) & 0xFF);

    // sequence number
    gNavtexDebugMsgBuffer[3] = 0;
    gNavtexDebugMsgBuffer[4] = 0;

    // command
    gNavtexDebugMsgBuffer[5] = 0;
    gNavtexDebugMsgBuffer[6] = 0;

    // command param
    gNavtexDebugMsgBuffer[7] = 0;
    gNavtexDebugMsgBuffer[8] = 0;

    pNavRx->debug_crc16 = crc16_calc(0xFFFF, &gNavtexDebugMsgBuffer[1], pNavRx->debug_len);
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = (u8)((pNavRx->debug_crc16)      & 0x00FF);
    gNavtexDebugMsgBuffer[pNavRx->debug_i_add++] = (u8)((pNavRx->debug_crc16 >> 8) & 0x00FF);

    TBD_uart_send_data(TARGET_UART_DEBUG, (uint8_t *)gNavtexDebugMsgBuffer, pNavRx->debug_i_add);
}

void NavtexMessageReceive_Task(void)
{ 
#if NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BASE_1_785KHZ
    // 50ms task run
    NavtexMessageReceive(&gNavtexRx_490KHz, &G_xMsgFskRxMDM_490KHz, &gNavtexMsg_490KHz);
    NavtexMessageReceive(&gNavtexRx_518KHz, &G_xMsgFskRxMDM_518KHz, &gNavtexMsg_518KHz);
#elif NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BAND_PASS_10KHZ
    // 50ms task run
    NavtexMessageReceive(&gNavtexRx_490KHz, &G_xMsgFskRxMDM_490KHz, &gNavtexMsg_490KHz);
    NavtexMessageReceive(&gNavtexRx_518KHz, &G_xMsgFskRxMDM_518KHz, &gNavtexMsg_518KHz);
    NavtexMessageReceive(&gNavtexRx_42095KHz, &G_xMsgFskRxMDM_42095KHz, &gNavtexMsg_42095KHz);
#endif
}

void NavtexMessageTransmit(sNavtexTx *pNavTx, tFskTxMDM *pFSK, u8 *pId, u8 *pStr)
{
    u32 Dx_Cnt = 0;
    u32 Rx_Cnt = 0;
    u32 i = 0;
    u16 msg_len = 0;
    u8 str_CCIR_Mode = 0xFF;
    u8 arr_ZCZC[4] = {'Z', 'C', 'Z', 'C'};

    pNavTx->CCIR_Mode = 0xFF;
    msg_len = strlen((char *)pStr);

    ClearAllFskTxBitData(pFSK);

    // // start phasing 5sec
    // // 1 character = 70ms
    // // 150 count = 10.5sec
    // for(i=0; i<150; i++) 
    // {
    //     pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_2, pNavTx);
    //     pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_1, pNavTx);
    // }
    // pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_1, pNavTx);
    // pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_1, pNavTx);

    // 29 count = 4sec
    for(i=0; i<29; i++) 
    {
        pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_2, pNavTx);
        pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_1, pNavTx);
    }
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_1, pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_1, pNavTx);

    // ZCZC
    for(i=0; i<4; i++)
    {
        str_CCIR_Mode = CCIR476_Mode_Check(arr_ZCZC[i], pNavTx);
        if(pNavTx->CCIR_Mode != str_CCIR_Mode)
        {
            pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(str_CCIR_Mode, pNavTx);
            pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(str_CCIR_Mode, pNavTx);
            pNavTx->CCIR_Mode = str_CCIR_Mode;
        }
        pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(arr_ZCZC[i], pNavTx);
        pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(arr_ZCZC[i], pNavTx);
    }

    // one space
    pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(' ', pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(' ', pNavTx);

    // id transmit
    for(i=0; i<4; i++)
    {
        str_CCIR_Mode = CCIR476_Mode_Check(pId[i], pNavTx);
        if(pNavTx->CCIR_Mode != str_CCIR_Mode)
        {
            pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(str_CCIR_Mode, pNavTx);
            pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(str_CCIR_Mode, pNavTx);
            pNavTx->CCIR_Mode = str_CCIR_Mode;
        }
        pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(pId[i], pNavTx);
        pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(pId[i], pNavTx);
    }

    // cariage return, line feed
    pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode('\r', pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode('\r', pNavTx);
    pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode('\n', pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode('\n', pNavTx);

    // message transmit
    for(i=0; i<msg_len; i++)
    {
        str_CCIR_Mode = CCIR476_Mode_Check(pStr[i], pNavTx);
        if(pNavTx->CCIR_Mode != str_CCIR_Mode)
        {
            pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(str_CCIR_Mode, pNavTx);
            pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(str_CCIR_Mode, pNavTx);
            pNavTx->CCIR_Mode = str_CCIR_Mode;
        }
        pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(pStr[i], pNavTx);
        pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(pStr[i], pNavTx);
    }

    // NNNN transmit
    pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode('\r', pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode('\r', pNavTx);
    pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode('\n', pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode('\n', pNavTx);
    pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode('\r', pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode('\r', pNavTx);
    pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode('\n', pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode('\n', pNavTx);
    for(i=0; i<4; i++)
    {
        str_CCIR_Mode = CCIR476_Mode_Check('N', pNavTx);
        if(pNavTx->CCIR_Mode != str_CCIR_Mode)
        {
            pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(str_CCIR_Mode, pNavTx);
            pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(str_CCIR_Mode, pNavTx);
            pNavTx->CCIR_Mode = str_CCIR_Mode;
        }
        pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode('N', pNavTx);
        pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode('N', pNavTx);
    }

    // Carriage return
    pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode('\r', pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode('\r', pNavTx);
    pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode('\n', pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode('\n', pNavTx);
    pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode('\n', pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode('\n', pNavTx);

    // end phasing
    // 1 character = 70ms
    // 20 count = 2.8sec
    for(i=0; i<20; i++)
    {
        pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(IDLE_SIGNAL_A, pNavTx);
        pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(IDLE_SIGNAL_B, pNavTx);
    }

    // fsk modem real transmit
    for(i=0; i<Rx_Cnt; i++)
    {
        if(i<=(Rx_Cnt-3))
        {
            ApndFskTx7BitData(pFSK, pNavTx->DX[i]);
        }
        ApndFskTx7BitData(pFSK, pNavTx->RX[i]);
    }

    memset(&gNavtexMsg_518KHz, 0x00, sizeof(sNavtexMsg));
    memset(&gNavtexRx_518KHz, 0x00, sizeof(sNavtexRx));
    memset(&gNavtexMsg_490KHz, 0x00, sizeof(sNavtexMsg));
    memset(&gNavtexRx_490KHz, 0x00, sizeof(sNavtexRx));
    SetFskTxSendMode(pFSK, TX_STATE_MSG_OUT);
}

void NavtexMessageTransmit_StartPhasing(sNavtexTx *pNavTx, tFskTxMDM *pFSK)
{
    u32 Dx_Cnt = 0;
    u32 Rx_Cnt = 0;
    u32 i = 0;

    ClearAllFskTxBitData(pFSK);

    // 100 count = 14sec
    for(i=0; i<100; i++) 
    {
        pNavTx->DX[Dx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_2, pNavTx);
        pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_1, pNavTx);
    }
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_1, pNavTx);
    pNavTx->RX[Rx_Cnt ++] = CCIR476_Encode(PHASING_SIGNAL_1, pNavTx);

    // fsk modem real transmit
    for(i=0; i<Rx_Cnt; i++)
    {
        if(i<=(Rx_Cnt-3))
        {
            ApndFskTx7BitData(pFSK, pNavTx->DX[i]);
        }
        ApndFskTx7BitData(pFSK, pNavTx->RX[i]);
    }

    memset(&gNavtexMsg_518KHz, 0x00, sizeof(sNavtexMsg));
    memset(&gNavtexRx_518KHz, 0x00, sizeof(sNavtexRx));
    memset(&gNavtexMsg_490KHz, 0x00, sizeof(sNavtexMsg));
    memset(&gNavtexRx_490KHz, 0x00, sizeof(sNavtexRx));
    SetFskTxSendMode(pFSK, TX_STATE_MSG_OUT);
}

void Navtex_Fsk_Dot_Out(tFskTxMDM *pFSK)
{
    ClearAllFskTxBitData(pFSK);
    SetFskTxSendMode(pFSK, TX_STATE_DOT_OUT);
}

void Navtex_Fsk_Space_Out(tFskTxMDM *pFSK)
{
    ClearAllFskTxBitData(pFSK);
    SetFskTxSendMode(pFSK, TX_STATE_SPACE_OUT);
}

void Navtex_Fsk_Mark_Out(tFskTxMDM *pFSK)
{
    ClearAllFskTxBitData(pFSK);
    SetFskTxSendMode(pFSK, TX_STATE_MARK_OUT);
}

void Self_NavtexMessageTransmit(void)
{
    NavtexMessageTransmit(&gNavtexTx, &G_xMsgFskTxMDM, (u8 *)pg_NavModem_Test_Id, (u8 *)pg_NavModem_Test_Msg);
}

void Self_NavtexMessageTransmit_StartPhasing(void)
{
    NavtexMessageTransmit_StartPhasing(&gNavtexTx, &G_xMsgFskTxMDM);
}

void Self_Navtex_Fsk_Dot_Out(void)
{
    Navtex_Fsk_Dot_Out(&G_xMsgFskTxMDM);
}

void Self_Navtex_Fsk_Mark_Out(void)
{
    Navtex_Fsk_Mark_Out(&G_xMsgFskTxMDM);
}

void Self_Navtex_Fsk_Space_Out(void)
{
    Navtex_Fsk_Mark_Out(&G_xMsgFskTxMDM);
}

void Self_Navtex_Tx_Stop(void)
{
    ClearAllFskTxBitData(&G_xMsgFskTxMDM);
}

int Proc_Self_Diag_StartPhasing_Test(sNavtexRx *pNavRx, int flag)
{
    static int seq = 0;
    int ret = RECV_DIAG_TYPE_NONE;

    if(flag == 0)
    {
        seq = 0;
        memset(pNavRx, 0x00, sizeof(sNavtexRx));
        Self_Navtex_Tx_Stop();
        return RECV_DIAG_TYPE_NONE;
    }

    switch(seq)
    {
        case 0:
            Self_Navtex_Tx_Stop();
            Self_NavtexMessageTransmit_StartPhasing();

            seq = 1;
            ret = RECV_DIAG_TYPE_RUN;
        break;

        case 1:
            if(pNavRx->Seq >= RECV_SEQ_RECEIVE)
            {
                ret = RECV_DIAG_TYPE_OK;
                memset(pNavRx, 0x00, sizeof(sNavtexRx));
                Self_Navtex_Tx_Stop();
                seq = 0;
            }
            else
            {
                ret = RECV_DIAG_TYPE_RUN;
            }
        break;
    }
    
    return ret;
}

int Proc_Self_Diag_Message_Tx(int flag)
{
    static int seq = 0;
    int ret = RECV_DIAG_TYPE_NONE;

    if(flag == 0)
    {
        seq = 0;
        Self_Navtex_Tx_Stop();
        return RECV_DIAG_TYPE_NONE;
    }

    switch(seq)
    {
        case 0:
            Self_Navtex_Tx_Stop();
            Self_NavtexMessageTransmit();
            seq = 1;
            ret = RECV_DIAG_TYPE_RUN;
        break;

        case 1:
            if(GetFskTxSendMode(&G_xMsgFskTxMDM) == TX_STATE_MSG_OUT)
            {
                ret = RECV_DIAG_TYPE_RUN;
            }
            else if(GetFskTxSendMode(&G_xMsgFskTxMDM) == TX_STATE_STOP)
            {
                ret = RECV_DIAG_TYPE_OK;
                seq = 0;
            }
        break;
    }

    return ret;
}

void NavtexModem_Task(void)
{
    static u16 pre_local_freq_ch = 0xFFFF;
    static u16 pre_int_freq_ch = 0xFFFF;

    // u8 SysMode = g_hSysStatus.m_pStat->mode.system;
    // if(SysMode != SYS_BOOT_RUN)
    // {
    //     return;
    // }

    if(G_xMsgFskRxMDM_518KHz.dFreqCh != pre_int_freq_ch)
    {
        memset(&gNavtexMsg_518KHz, 0x00, sizeof(sNavtexMsg));
        memset(&gNavtexRx_518KHz, 0x00, sizeof(sNavtexRx));
        ClearAllFskRxBitData(&G_xMsgFskRxMDM_518KHz);
        Internal_Tx_Nav_Msg(&gNavtexMsg_518KHz, I_COMM_NAV_CMD_PARAM_TYPE_LOCAL_FREQ_CHANGED, G_xMsgFskRxMDM_518KHz.dFreqCh, 0, NULL);
    }
    pre_int_freq_ch = G_xMsgFskRxMDM_518KHz.dFreqCh;

    if(G_xMsgFskRxMDM_490KHz.dFreqCh != pre_local_freq_ch)
    {
        memset(&gNavtexMsg_490KHz, 0x00, sizeof(sNavtexMsg));
        memset(&gNavtexRx_490KHz, 0x00, sizeof(sNavtexRx));
        ClearAllFskRxBitData(&G_xMsgFskRxMDM_490KHz);
        Internal_Tx_Nav_Msg(&gNavtexMsg_490KHz, I_COMM_NAV_CMD_PARAM_TYPE_LOCAL_FREQ_CHANGED, G_xMsgFskRxMDM_490KHz.dFreqCh, 0, NULL);
    }
    pre_local_freq_ch = G_xMsgFskRxMDM_490KHz.dFreqCh;

    NavtexMessageReceive_Task();
}
