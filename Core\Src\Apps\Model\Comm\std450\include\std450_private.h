///@file     std450_private.h
///@brief    450 internal private code header.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#ifndef STD450_PRIVATE_H
#define STD450_PRIVATE_H

#include <std450.h>
#include <std450_nf.h>
#include <std450_sf.h>

#include <std450_common.h>
#include <std450_nf_private.h>
#include <std450_sf_private.h>

STD450_BEGIN_DECLS

#define LEN_DEV_NAME    32

///@brief IEC 61162-450 device struct.
struct _std450 {
    int  idx;                   /// Internal 450 device Index. Not define in IEC 61162-450.
    char name[LEN_DEV_NAME];    /// Internal 450 device name. Not define in IEC 61162-450.
    nf_t *nf;                   /// Netwrok Function block.
    sf_t **sf;                  /// System Function block.
    sfi_info_t sfi_info;        /// System Function count and sfi info.
    // sf_t *sngf;              /// Serial to Network Gateway Function block. [IEC 61162-1,2 to IEC 61162-450]
    // sf_t *pngf;              /// PGN to Network Gateway Function block. [IEC 61162-3 to IEC 61162-450]        
    // onf_t *onf;              /// Other Network Function block. [NF compliant EQ]
};

STD450_END_DECLS

#endif  /* STD450_PRIVATE_H */