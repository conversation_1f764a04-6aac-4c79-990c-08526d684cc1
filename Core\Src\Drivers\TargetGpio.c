/**
******************************************************************************
* @file      TargetGpio.c
* <AUTHOR>
* @date      2024-05-10
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetBoard.h"
#include "model.h"

DrvGpioComponent_s gDrvGpio = {0};

void TargetGpio_Init(void)
{
    // HAL_GPIO_WritePin(DO_Selector_4M_GPIO_Port, DO_Selector_4M_Pin, 0); // H - 4.2MHz, L - 490KHz
    // HAL_GPIO_WritePin(DO_ANT_POW_ON_GPIO_Port, DO_ANT_POW_ON_Pin, 0);   // low active
    // HAL_GPIO_WritePin(DO_Alarm_relay_GPIO_Port, DO_Alarm_relay_Pin, 1);
    // HAL_GPIO_WritePin(DO_4MOSC_SEL_GPIO_Port, DO_4MOSC_SEL_Pin, 1);

    Set_Gpio_RF_Relay(RF_RELAY_CH_490KHZ);
    Set_Gpio_Ant_Power(0);
    Set_Gpio_Alarm_Relay(0);
    Set_Gpio_4M_Freq_Generator(1);

    HAL_GPIO_WritePin(DO_ETH_RESET_GPIO_Port, DO_ETH_RESET_Pin, 0);
    HAL_Delay(500);
    HAL_GPIO_WritePin(DO_ETH_RESET_GPIO_Port, DO_ETH_RESET_Pin, 1);
    HAL_Delay(500);
}

void Set_Gpio_RF_Relay(int onoff)
{
    // H - 4.2MHz
    // L - 490KHz
    // Mixer relay
    HAL_GPIO_WritePin(DO_Selector_4M_GPIO_Port, DO_Selector_4M_Pin, (GPIO_PinState)onoff);
    gDrvGpio.rf_relay = onoff;
}
int Get_Gpio_RF_Relay(void)
{
    return gDrvGpio.rf_relay;
}

void Set_Gpio_Ant_Power(int onoff)
{
    // low active

    if(onoff == 0)
    {
        HAL_GPIO_WritePin(DO_ANT_POW_ON_GPIO_Port, DO_ANT_POW_ON_Pin, (GPIO_PinState)1);
    }
    else
    {
        HAL_GPIO_WritePin(DO_ANT_POW_ON_GPIO_Port, DO_ANT_POW_ON_Pin, (GPIO_PinState)0);
    }
    gDrvGpio.ant_power = onoff;
}
int Get_Gpio_Ant_Power(void)
{
    return gDrvGpio.ant_power;
}

void Set_Gpio_Alarm_Relay(int onoff)
{
    HAL_GPIO_WritePin(DO_Alarm_relay_GPIO_Port, DO_Alarm_relay_Pin, (GPIO_PinState)onoff);
    gDrvGpio.alarm_relay = onoff;
}
int Get_Gpio_Alarm_Relay(void)
{
    return gDrvGpio.alarm_relay;
}

void Set_Gpio_4M_Freq_Generator(int onoff)
{
    HAL_GPIO_WritePin(DO_4MOSC_SEL_GPIO_Port, DO_4MOSC_SEL_Pin, (GPIO_PinState)onoff);
    gDrvGpio.freq_4m_gen = onoff;
}
int Get_Gpio_4M_Freq_Generator(void)
{
    return gDrvGpio.freq_4m_gen;
}


void TargetGpio_Task(void)
{
    if(g_hSysStatus.m_pStat->system_all_para_received_flag == 1)
    {
        if( g_hSysStatus.m_pStat->diag.Recv_Result_RF_518KHz == DIAG_STATE_NONE && 
            g_hSysStatus.m_pStat->diag.Recv_Result_RF_490KHz == DIAG_STATE_NONE && 
            g_hSysStatus.m_pStat->diag.Recv_Result_RF_4209_5KHz == DIAG_STATE_NONE &&
            g_hSysStatus.m_pStat->diag.Msg_Recv_Mon_State == DIAG_STATE_NONE)
        {
            if(g_hSysCfg.m_pCfg->nav.ch_set_local == MV_CH_LOC_490KHZ)
            {
                Set_Gpio_RF_Relay(RF_RELAY_CH_490KHZ);
            }
            else
            {
                Set_Gpio_RF_Relay(RF_RELAY_CH_4209_5KHZ);
            }

            if(g_hSysCfg.m_pCfg->antenna.type == MV_CH_ANT_TYPE_ACTIVE)
            {
                Set_Gpio_Ant_Power(1);
            }
            else
            {
                Set_Gpio_Ant_Power(0);
            }
        }

        if(g_hSysCfg.m_pCfg->alarm_relay.status == MV_VAL_ON)
        {
            Set_Gpio_Alarm_Relay(1);
        }
        else
        {
            Set_Gpio_Alarm_Relay(0);
        }
    }
}