/**
******************************************************************************
* @file      TargetFSK.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetBoard.h"
#include "AllConst.h"
#include "model.h"

extern DAC_HandleTypeDef hdac1;

void TargetFSK_Init(void)
{
#if NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BASE_1_785KHZ
    MakeInitFskTxModem(&G_xMsgFskTxMDM,
                        FSK_SAMPLE_FREQ,
                        FSK_BPS,
                        FSK_SPACE_FREQ,
                        FSK_MARK_FREQ,
                        6000);

    MakeInitFskRxModem(&G_xMsgFskRxMDM_518KHz, 
                        FSK_SAMPLE_FREQ,
                        FSK_BPS,
                        FSK_SPACE_FREQ,
                        FSK_MARK_FREQ,
                        FSK_BUFFER_SIZE,
                        FSK_ADC_SIZE);

    MakeInitFskRxModem(&G_xMsgFskRxMDM_490KHz, 
                        FSK_SAMPLE_FREQ,
                        FSK_BPS,
                        FSK_SPACE_FREQ,
                        FSK_MARK_FREQ,
                        FSK_BUFFER_SIZE,
                        FSK_ADC_SIZE);

    ConfigFskRxModemFreqCh(&G_xMsgFskRxMDM_518KHz, FSK_FREQ_INT_518KHZ);

    MakeInitBaseIIR(&G_xIir_Fs_24Khz_Lp_1700hz_IIR_Int_Ch, NULL, NULL, 0);
    MakeInitBaseIIR(&G_xIir_Fs_24Khz_Hp_1700hz_IIR_Int_Ch, NULL, NULL, 0);
    MakeInitBaseIIR(&G_xIir_Fs_24Khz_Lp_1700hz_IIR_Local_Ch, NULL, NULL, 0);
    MakeInitBaseIIR(&G_xIir_Fs_24Khz_Hp_1700hz_IIR_Local_Ch, NULL, NULL, 0);
    MakeInitBaseIIR(&G_xIir_Fs_24Khz_Bp_1600Hz_1800Hz_IIR_Int_Ch, NULL, NULL, 0);
    MakeInitBaseIIR(&G_xIir_Fs_24Khz_Bp_1600Hz_1800Hz_IIR_Local_Ch, NULL, NULL, 0);
    
#elif NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BAND_PASS_10KHZ
    MakeInitFskTxModem(&G_xMsgFskTxMDM,
                        FSK_SAMPLE_FREQ,
                        FSK_BPS,
                        FSK_SPACE_FREQ,
                        FSK_MARK_FREQ,
                        6000);
                        
    MakeInitFskRxModem(&G_xMsgFskRxMDM_518KHz, 
                        FSK_SAMPLE_FREQ,
                        FSK_BPS,
                        FSK_SPACE_FREQ,
                        FSK_MARK_FREQ,
                        FSK_BUFFER_SIZE,
                        FSK_ADC_SIZE);

    MakeInitFskRxModem(&G_xMsgFskRxMDM_490KHz, 
                        FSK_SAMPLE_FREQ,
                        FSK_BPS,
                        FSK_SPACE_FREQ,
                        FSK_MARK_FREQ,
                        FSK_BUFFER_SIZE,
                        FSK_ADC_SIZE);

    MakeInitFskRxModem(&G_xMsgFskRxMDM_42095KHz, 
                        FSK_SAMPLE_FREQ,
                        FSK_BPS,
                        FSK_SPACE_FREQ,
                        FSK_MARK_FREQ,
                        FSK_BUFFER_SIZE,
                        FSK_ADC_SIZE);

    ConfigFskRxModemFreqCh(&G_xMsgFskRxMDM_518KHz, FSK_FREQ_INT_518KHZ);
    ConfigFskRxModemFreqCh(&G_xMsgFskRxMDM_490KHz, FSK_FREQ_LOC_490KHZ);
    ConfigFskRxModemFreqCh(&G_xMsgFskRxMDM_42095KHz, FSK_FREQ_LOC_42095KHZ);

    MakeInitBaseIIR(&G_xIir_10Khz_BP_Of_518KHz, NULL, NULL, 0);
    MakeInitBaseIIR(&G_xIir_10Khz_BP_Of_490KHz, NULL, NULL, 0);
    MakeInitBaseIIR(&G_xIir_10Khz_BP_Of_42095KHz, NULL, NULL, 0);
#endif
}

float32_t fTemp_ADC_INT = 0;
float32_t fTemp_ADC_LOC = 0;
float32_t fTemp_IIR_Filtered_INT = 0;
float32_t fTemp_IIR_Filtered_LOC = 0;
void FSK_Msg_Rx_Run(void)
{
    static u8 tog = 0;
    u8 SysMode = g_hSysStatus.m_pStat->mode.system;
    SysMode = SYS_BOOT_RUN;
    if(SysMode != SYS_BOOT_RUN)
    {
        return; 
    }

#if NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BASE_1_785KHZ
    TBD_adc_read();

    if(Get_Gpio_RF_Relay() == RF_RELAY_CH_490KHZ)
    {
        ConfigFskRxModemFreqCh(&G_xMsgFskRxMDM_490KHz, FSK_FREQ_LOC_490KHZ);
    }
    else if(Get_Gpio_RF_Relay() == RF_RELAY_CH_4209_5KHZ)
    {
        ConfigFskRxModemFreqCh(&G_xMsgFskRxMDM_490KHz, FSK_FREQ_LOC_42095KHZ);
    }

    // *********** BPF applied fsk demod ***********
    fTemp_ADC_INT = (float32_t)gDrvAdc.Adc_RF_518KHz;
    fTemp_IIR_Filtered_INT = CalcNewDataIIR(&G_xIir_Fs_24Khz_Bp_1600Hz_1800Hz_IIR_Int_Ch, fTemp_ADC_INT);
    RunFskRxModemISR(&G_xMsgFskRxMDM_518KHz, (int)fTemp_IIR_Filtered_INT);

    // *********** BPF applied fsk demod ***********
    fTemp_ADC_LOC = (float32_t)gDrvAdc.Adc_RF_490KHz;
    fTemp_IIR_Filtered_LOC = CalcNewDataIIR(&G_xIir_Fs_24Khz_Bp_1600Hz_1800Hz_IIR_Local_Ch, fTemp_ADC_LOC);
    RunFskRxModemISR(&G_xMsgFskRxMDM_490KHz, (int)fTemp_IIR_Filtered_LOC);

    g_hSysStatus.m_pStat->dac_out_ch = DAC_OUT_CH_LOC;
    if(g_hSysStatus.m_pStat->dac_out_ch == DAC_OUT_CH_INT)
    {
        // *********** disable iir filtered ***********
        // HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_2, DAC_ALIGN_12B_R, ((u32)fTemp_ADC_INT * 4095)/65535);

        // *********** enable iir filtered ***********
        fTemp_IIR_Filtered_INT = fTemp_IIR_Filtered_INT + 21845; // offset +1.1V
        HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_2, DAC_ALIGN_12B_R, ((u32)fTemp_IIR_Filtered_INT * (4095/2))/65535);
    }
    else if(g_hSysStatus.m_pStat->dac_out_ch == DAC_OUT_CH_LOC)
    {
        // *********** disable iir filtered ***********
        // HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_2, DAC_ALIGN_12B_R, ((u32)fTemp_ADC_LOC * 4095)/65535);

        // *********** enable iir filtered ***********
        fTemp_IIR_Filtered_LOC = fTemp_IIR_Filtered_LOC + 21845; // offset +1.1V
        HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_2, DAC_ALIGN_12B_R, ((u32)fTemp_IIR_Filtered_LOC * (4095/2))/65535);
    }

    tog ^= 0x01;
    HAL_GPIO_WritePin(TP_PD3_GPIO_Port, TP_PD3_Pin, tog); //TP54

#elif NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BAND_PASS_10KHZ
    TBD_adc_read();

    fTemp_ADC = (f32)gDrvAdc.Adc_RF_518KHz;
    fTemp_IIR_Filtered = CalcNewDataIIR(&G_xIir_10Khz_BP_Of_518KHz, fTemp_ADC);
    RunFskRxModemISR(&G_xMsgFskRxMDM_518KHz, (int)fTemp_IIR_Filtered);

    fTemp_ADC = (f32)gDrvAdc.Adc_RF_490KHz;
    fTemp_IIR_Filtered = CalcNewDataIIR(&G_xIir_10Khz_BP_Of_490KHz, fTemp_ADC);
    RunFskRxModemISR(&G_xMsgFskRxMDM_490KHz, (int)fTemp_IIR_Filtered);

    fTemp_ADC = (f32)gDrvAdc.Adc_RF_42095KHz;
    fTemp_IIR_Filtered = CalcNewDataIIR(&G_xIir_10Khz_BP_Of_42095KHz, fTemp_ADC);
    RunFskRxModemISR(&G_xMsgFskRxMDM_42095KHz, (int)fTemp_IIR_Filtered);

//------_------_------_------_------_------_------_-------_-------_-------_------//
//                             Wave Check                             
//------_------_------_------_------_------_------_-------_-------_-------_------//
    int_data = (gDrvAdc.Adc_RF_518KHz * 4095) / 65535;
	HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_1, DAC_ALIGN_12B_R, (uint32_t)int_data);

    fTempX = (fTempX * 4095) / 65535;
    HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_2, DAC_ALIGN_12B_R, (uint32_t)fTempX);
#endif

//------_------_------_------_------_------_------_-------_-------_-------_------//
//                             G.729 test code                             
//------_------_------_------_------_------_------_-------_-------_-------_------//
    //TargetCan_Audio_Data_Tx((u16)gDrvAdc.Adc_RF_490KHz);

    // static int ecnt = 0;
    // static int tog = 0;
    // ecnt += 1;
    // if(ecnt >= 6)
    // {
    //     i32 offseted_adc = 0;
    //     offseted_adc = ((i32)gDrvAdc.Adc_RF_490KHz-32767);
    //     if(offseted_adc <= -32767)
    //     {
    //     	offseted_adc = -32767;
    //     }
    //     else if(offseted_adc >= 32767)
    //     {
    //     	offseted_adc = 32767;
    //     }
    //     G729_Encode_Apnd_Data_Buffer((i16)offseted_adc);

    //     tog ^= 0x01;
    //     HAL_GPIO_WritePin(DO_FSK_SAMPLE_FREQ_DETECT_GPIO_Port, DO_FSK_SAMPLE_FREQ_DETECT_Pin, tog);

    //     ecnt = 0;
    // }


    // static int dcnt = 0; 
    // dcnt += 1;
    // if(dcnt >= 6)
    // {
    //     i32 data = 0;
    //     i32 result_decode = 0xFFFF;
    //     result_decode = G729_Decode_Read_Out_Data_Buffer();
    //     if(result_decode != 0xFFFF)
    //     {
    //         //data = ((result_decode + 32767) * 4095) / 65535;
    //         data = ((result_decode + 32767) * vol_12bit) / 65535;
    //         HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_1, DAC_ALIGN_12B_R, (uint32_t)data);
    //         HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_2, DAC_ALIGN_12B_R, (uint32_t)data);
    //     }

    //     dcnt = 0;
    // }
}

void FSK_Msg_Tx_Run(void)
{
    u8 SysMode = g_hSysStatus.m_pStat->mode.system;
    SysMode = SYS_BOOT_RUN;
    if(SysMode != SYS_BOOT_RUN)
    {
        return; 
    }

    RunFskTxModemISR(&G_xMsgFskTxMDM);

    // test
    // x = dac (12bit)
    // y = target voltage (1 scale)
    // calculation x = (4095ad * y) /3.3v
    G_xMsgFskTxMDM.fVcoAmpVal = ((G_xMsgFskTxMDM.fVcoAmpVal + 1) * (20475*G_xMsgFskTxMDM.v_pp)/330) + (40950*G_xMsgFskTxMDM.v_offset)/330;
    //G_xMsgFskTxMDM.fVcoAmpVal = ((G_xMsgFskTxMDM.fVcoAmpVal + 1) * (float32_t)(1427)) + (float32_t)(869);
    HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_1, DAC_ALIGN_12B_R, (uint32_t)G_xMsgFskTxMDM.fVcoAmpVal);
}
