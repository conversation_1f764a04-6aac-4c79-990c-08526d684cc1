/**
******************************************************************************
* @file      Coef_FS_24KHZ_BP_1600HZ_1800HZ_IIR.c
* <AUTHOR>
* @date      2025-03-26
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2025 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "AllConst.h"
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
//------_------_------_------_------_------_------_------//
//      must invert the sign of the coefficients A       //
//------_------_------_------_------_------_------_------//
static const double vCoeffA[FLT_FS_24KHZ_BP_1600HZ_1800HZ_IIR_SIZE + 0] = 
{
    +10.581487716493100,
    -52.377940322646200,
    +160.184119930769000,
    -336.822526298479000,
    +512.746685573120000,
    -579.278591125809000,
    +489.320752431852000,
    -306.748719289112000,
    +139.216965000472000,
    -43.442284339217600,
    +8.375358580363030,
    -0.755354492956261,
};
static const double vCoeffB[FLT_FS_24KHZ_BP_1600HZ_1800HZ_IIR_SIZE + 1] = 
{ 
    0.000938621557888,
    -0.009915752162015,
    0.049208223442015,
    -0.151543149170659,
    0.322353952607951,
    -0.498753783283860,
    0.575423820652511,
    -0.498753783283860,
    0.322353952607951,
    -0.151543149170660,
    0.049208223442015,
    -0.009915752162015,
    0.000938621557888,
};

 //-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
tBaseIIR    G_xIir_Fs_24Khz_Bp_1600Hz_1800Hz_IIR_Int_Ch =
          {
            .dSizeB = FLT_FS_24KHZ_BP_1600HZ_1800HZ_IIR_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };

tBaseIIR    G_xIir_Fs_24Khz_Bp_1600Hz_1800Hz_IIR_Local_Ch =
          {
            .dSizeB = FLT_FS_24KHZ_BP_1600HZ_1800HZ_IIR_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
