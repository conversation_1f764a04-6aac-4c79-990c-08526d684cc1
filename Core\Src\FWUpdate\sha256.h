/*
 * sha256.h
 *
 *  Created on: Apr 15, 2025
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#ifndef FWUPDATE_SHA256_H_
#define FWUPDATE_SHA256_H_

#include <stddef.h> // size_t 사용
#include "stdint.h" // 표준 정수 타입 사용

// --- 상수 정의 ---
#define SHA256_BLOCK_SIZE 64   // SHA-256 처리 블록 크기 (바이트)
#define SHA256_DIGEST_SIZE 32  // SHA-256 해시 결과 크기 (바이트)

// --- SHA-256 컨텍스트 구조체 ---
// 내부 상태 저장을 위한 구조체
typedef struct
{
    uint8_t  data[SHA256_BLOCK_SIZE]; // 현재 처리 중인 데이터 블록 버퍼
    uint32_t datalen;                 // 현재 버퍼에 있는 데이터 길이
    uint64_t bitlen;                  // 전체 처리된 데이터 비트 수
    uint32_t state[8];                // 중간 해시 값 (H0 ~ H7)
} SHA256_CTX;

// --- 함수 프로토타입 ---

/**
 * @brief SHA-256 컨텍스트 초기화
 * @param ctx 초기화할 SHA-256 컨텍스트 포인터
 */
void sha256_init(SHA256_CTX *ctx);

/**
 * @brief SHA-256 해시 계산 데이터 업데이트
 * @param ctx 업데이트할 SHA-256 컨텍스트 포인터
 * @param data 추가할 데이터 배열 포인터
 * @param len 추가할 데이터 길이 (바이트 단위)
 */
void sha256_update(SHA256_CTX *ctx, const uint8_t *data, size_t len);

/**
 * @brief SHA-256 최종 해시 값 계산
 * @param ctx 최종 계산을 수행할 SHA-256 컨텍스트 포인터
 * @param hash 최종 해시 값이 저장될 32바이트 버퍼 포인터
 */
void sha256_final(SHA256_CTX *ctx, uint8_t *hash);

#endif /* FWUPDATE_SHA256_H_ */
