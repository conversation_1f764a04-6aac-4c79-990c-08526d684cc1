/**
******************************************************************************
* @file      TargetBoard.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "Common.h"
#include "TargetBoard.h"

extern SDRAM_HandleTypeDef* GetSdramHandler(void);
extern MDMA_HandleTypeDef* GetMdmaHandler(void);

/**
 * @brief  TargetBoard_Init
 * @param  NULL
 * @retval void
 * @see
 */

void TargetBoard_Init(void)
{
	//int32_t retVal = TBD_ERROR_NONE;

	//SDRAM_HandleTypeDef *pHSDRam = GetSdramHandler();
	//MDMA_HandleTypeDef *pHMdma = GetMdmaHandler();

	// Initialize uart port
	TBD_init_uarts();
	DEBUG_MSG("\r\n\r\n");

	DEBUG_MSG("***************************************\r\n");
	DEBUG_MSG("* Navtex Receiver Booting Start !!!!!!!!! \r\n");
	DEBUG_MSG("* Firmware ver\r\n");
	DEBUG_MSG("* Major %d\r\n", SYSTEM_SW_VER_MAJOR);
	DEBUG_MSG("* Minor %d\r\n", SYSTEM_SW_VER_MINOR);
	DEBUG_MSG("* Revision %d\r\n", SYSTEM_SW_VER_REV);
	DEBUG_MSG("* Release Candidate %d\r\n", SYSTEM_SW_VER_RELEASE_CANDIDATE);
	DEBUG_MSG("***************************************\r\n");

	// Initialize General Port
	TargetGpio_Init();
	DEBUG_MSG("[%s] General Port Init...\r\n", __FUNCTION__);

	// Initialize Dac
	TBD_init_dac();
	DEBUG_MSG("[%s] DAC Init...\r\n", __FUNCTION__);

	// Initialize FSK Module
	TargetFSK_Init();
	DEBUG_MSG("[%s] FSK Rx, Tx Init...\r\n", __FUNCTION__);

	// Initialize Eeprom for mac address
	TBD_Init_Eeprom();
	DEBUG_MSG("[%s] EEprom for Mac Addr Init...\r\n", __FUNCTION__);

	// // Initialize Nor Flash
	// TBD_init_nor_flash();
	// DEBUG_MSG("[%s] Nor Flash Init...\r\n", __FUNCTION__);

	// Initialize RTC
	TBD_Rtc_Init();
	DEBUG_MSG("[%s] RTC Init...\r\n", __FUNCTION__);

	// Initialize Can
	TargetCan_Init();
	DEBUG_MSG("[%s] CAN Init...\r\n", __FUNCTION__);

	// Initialize ADC
	TBD_init_adc();
	DEBUG_MSG("[%s] ADC Init...\r\n", __FUNCTION__);

	// // Initialize G729 compression
	// TargetG729_Init();
	// DEBUG_MSG("[%s] G729 compression Init...\r\n", __FUNCTION__);

	// // Initialize SampleFreq
	// TargetSampleFreq_Init();
	// DEBUG_MSG("[%s] Sample Freq Init...\r\n", __FUNCTION__);
}

