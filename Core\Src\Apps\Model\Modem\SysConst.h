﻿/**
 * @file      SysConst.h
 * <AUTHOR>
 * @brief     시스템에서 사용되는 일반 상수들 정의
 * @version   0.1
 * @date      2022-08-11
 * 
 * @copyright Copyright (c) 2022
 * 
 */


#if !defined(__SysConst_H__)
#define      __SysConst_H__

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "stm32h7xx_hal.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include <ctype.h>
#include <ieeefp.h>
#include <malloc.h>
#include <math.h>
#include <setjmp.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include <time.h>
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#define  SYS_CLK_OSC_FREQUENCY         (25000000UL)
#define  SYS_CLK_CPU_MAIN_FREQUENCY    (480000000)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  SYS_CLK_CPU_TICK_FREQUENCY    (SYS_CLK_CPU_MAIN_FREQUENCY / 8)
#define  SYS_CLK_AXI_PERI_FREQUENCY    (SYS_CLK_CPU_MAIN_FREQUENCY / 2)
#define  SYS_CLK_HCLK3_FREQUENCY       (SYS_CLK_CPU_MAIN_FREQUENCY / 2)
#define  SYS_CLK_APB3_PERI_FREQUENCY   (SYS_CLK_CPU_MAIN_FREQUENCY / 4)
#define  SYS_CLK_APB2_PERI_FREQUENCY   (SYS_CLK_CPU_MAIN_FREQUENCY / 4)
#define  SYS_CLK_APB2_TMR_FREQUENCY    (SYS_CLK_CPU_MAIN_FREQUENCY / 2)
#define  SYS_CLK_APB1_PERI_FREQUENCY   (SYS_CLK_CPU_MAIN_FREQUENCY / 4)
#define  SYS_CLK_APB1_TMR_FREQUENCY    (SYS_CLK_CPU_MAIN_FREQUENCY / 2)
#define  SYS_CLK_AHB1_FREQUENCY        (SYS_CLK_CPU_MAIN_FREQUENCY / 2)
#define  SYS_CLK_AHB2_FREQUENCY        (SYS_CLK_CPU_MAIN_FREQUENCY / 2)
#define  SYS_CLK_AHB4_FREQUENCY        (SYS_CLK_CPU_MAIN_FREQUENCY / 2)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  SYS_INT_PRIORITY_SPI2         (0)
#define  SYS_INT_PRIORITY_ADC          (TICK_INT_PRIORITY - 1)
#define  SYS_INT_PRIORITY_DAC          (TICK_INT_PRIORITY - 1)
#define  SYS_INT_PRIORITY_TIMER        (TICK_INT_PRIORITY - 1)
#define  SYS_INT_PRIORITY_UART         (TICK_INT_PRIORITY - 1)
#define  SYS_INT_PRIORITY_I2C          (TICK_INT_PRIORITY - 1)
#define  SYS_INT_PRIORITY_TICK         (TICK_INT_PRIORITY - 0)
#define  SYS_INT_PRIORITY_NULL         (TICK_INT_PRIORITY - 0)
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#define  SYS_TICK_CNT_PER_ONE_SCND     (1000)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  SYS_CONVERT_TICK_TO_MILI(X)   ((X) * (1000 / SYS_TICK_CNT_PER_ONE_SCND))
#define  SYS_CONVERT_MILI_TO_TICK(X)   (SYS_TICK_CNT_PER_ONE_SCND * (X) / 1000)
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#define  SYS_ENABLE_GLOBAL_IRQ()       {__enable_irq();}
#define  SYS_DISABLE_GLOBAL_IRQ()      {__disable_irq();}
#define  SYS_RESET_MAIN_MPU()          {NVIC_SystemReset();}
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

#endif   // __SysConst_H__
