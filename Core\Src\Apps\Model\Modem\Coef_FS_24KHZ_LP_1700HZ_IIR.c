/**
******************************************************************************
* @file      Coef_FS_24KHZ_LP_1700HZ_IIR.c
* <AUTHOR>
* @date      2025-03-26
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2025 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "AllConst.h"
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
//------_------_------_------_------_------_------_------//
//      must invert the sign of the coefficients A       //
//------_------_------_------_------_------_------_------//
static const double vCoeffA[FLT_FS_24KHZ_LP_1700HZ_IIR_SIZE + 0] = 
{
  +13.229011969956000,
  -84.853738534153100,
  +350.449861920166000,
  -1044.403739720150000,
  +2386.416239647740000,
  -4337.606759750280000,
  +6423.547194261080000,
  -7875.678617488900000,
  +8080.609497516430000,
  -6985.490323293130000,
  +5106.678493411980000,
  -3159.891546837240000,
  +1652.202373106870000,
  -726.745614049761000,
  +266.887097074033000,
  -80.886863212364400,
  +19.891959808759100,
  -3.871868390230120,
  +0.574384886391902,
  -0.061075160520874,
  +0.004155647670396,
  -0.000137073740947,
};
static const double vCoeffB[FLT_FS_24KHZ_LP_1700HZ_IIR_SIZE + 1] = 
{ 
  0.001675283801872,
  -0.018278265551096,
  0.098687596752287,
  -0.348185558070933,
  0.897005567991640,
  -1.790301041421930,
  2.872744585702470,
  -3.809039129084660,
  4.288031845123440,
  -4.261297123679180,
  3.992103823666410,
  -3.846280911069670,
  3.992103823666410,
  -4.261297123679190,
  4.288031845123440,
  -3.809039129084660,
  2.872744585702470,
  -1.790301041421930,
  0.897005567991641,
  -0.348185558070934,
  0.098687596752287,
  -0.018278265551096,
  0.001675283801872,
};

 //-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
tBaseIIR    G_xIir_Fs_24Khz_Lp_1700hz_IIR_Int_Ch =
          {
            .dSizeB = FLT_FS_24KHZ_LP_1700HZ_IIR_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };

tBaseIIR    G_xIir_Fs_24Khz_Lp_1700hz_IIR_Local_Ch =
          {
            .dSizeB = FLT_FS_24KHZ_LP_1700HZ_IIR_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
