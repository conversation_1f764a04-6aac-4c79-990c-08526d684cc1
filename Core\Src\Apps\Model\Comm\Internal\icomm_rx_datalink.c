/**
******************************************************************************
* @file      icomm_rx_datalink.c
* <AUTHOR>
* @date      2024-06-14
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "Internal.h"
#include "model.h"
#include "TargetBoard.h"
#include "FW_Update.h"

extern osThreadId EthernetTaskHandle;

void Internal_Rx_System_Parameter_Sending_Success(Icomm_Protocol_Rx_s *pRx)
{
    g_hSysStatus.m_pStat->mode.Ctrl_SystemState = pRx->st.dt.System.State;

    // DEBUG_COMM_MSG("[%s] Recv\r\n", __FUNCTION__);
}

void Internal_Rx_Diag_Req(Icomm_Protocol_Rx_s *pRx)
{
    if(pRx->st.dt.DiagReq.type == DIAG_TYPE_RECV_RF_518KHZ)
    {
        g_hSysStatus.m_pStat->diag.Recv_Result_RF_518KHz = (int)pRx->st.dt.DiagReq.state;
    }
    else if(pRx->st.dt.DiagReq.type == DIAG_TYPE_RECV_RF_490KHZ)
    {
        g_hSysStatus.m_pStat->diag.Recv_Result_RF_490KHz = (int)pRx->st.dt.DiagReq.state;
    }
    else if(pRx->st.dt.DiagReq.type == DIAG_TYPE_RECV_RF_4209_5KHZ)
    {
        g_hSysStatus.m_pStat->diag.Recv_Result_RF_4209_5KHz = (int)pRx->st.dt.DiagReq.state;
    }
    else if(pRx->st.dt.DiagReq.type == DIAG_TYPE_RECV_MSG_RF_ALL)
    {
        g_hSysStatus.m_pStat->diag.Msg_Recv_Mon_State = (int)pRx->st.dt.DiagReq.state;
    }

    // DEBUG_COMM_MSG("[%s] Recv\r\n", __FUNCTION__);
}

void Internal_Rx_Ins_Data(Icomm_Protocol_Rx_s *pRx)
{
    nmea_uart_enqueue(&nmea_bufs[NMEA_TYPE_INS], pRx->st.dt.Nmea0183.data, pRx->st.hd.Frm_Len-8);

    // DEBUG_COMM_MSG("[%s] Recv : %s\r\n", __FUNCTION__, pRx->st.dt.Nmea0183.data);
}

void Internal_Rx_Gpio_Set(Icomm_Protocol_Rx_s *pRx)
{
    int type = 0;
    int state = 0;

    type = pRx->st.dt.Gpio_Set.type;
    state = pRx->st.dt.Gpio_Set.state;
    if(type == GPIO_TYPE_RF_RELAY)
    {
        Set_Gpio_RF_Relay(state);
    }
    else if(type == GPIO_TYPE_ANT_POWER)
    {
        Set_Gpio_Ant_Power(state);
    }
    else if(type == GPIO_TYPE_ALARM_RELAY)
    {
        Set_Gpio_Alarm_Relay(state);
    }
    else if(type == GPIO_TYPE_4M_FREQ_GENERATOR)
    {
        Set_Gpio_4M_Freq_Generator(state);
    }

    // DEBUG_COMM_MSG("[%s] Recv\r\n", __FUNCTION__);
}

void Internal_Rx_Fsk_Req_DeMod_Test(Icomm_Protocol_Rx_s *pRx)
{
    FSK_DeMod_Test_s *pTest = NULL;

    switch(pRx->st.dt.Test_Fsk.ch)
    {
        case FSK_DEMOD_TEST_CH_518KHZ:
            pTest = &g_hSysStatus.m_pStat->test_fsk[FSK_DEMOD_TEST_CH_518KHZ];
            g_hSysStatus.m_pStat->dac_out_ch = DAC_OUT_CH_INT;
        break;
#if NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BASE_1_785KHZ
        case FSK_DEMOD_TEST_CH_4209_5KHZ:
        case FSK_DEMOD_TEST_CH_490KHZ:
            pTest = &g_hSysStatus.m_pStat->test_fsk[FSK_DEMOD_TEST_CH_490KHZ];
            g_hSysStatus.m_pStat->dac_out_ch = DAC_OUT_CH_LOC;
        break;
#elif NAVTEX_FREQ_SAMPLE_MODE == FREQ_SAMPLE_BAND_PASS_10KHZ
        case FSK_DEMOD_TEST_CH_490KHZ:
            pTest = &g_hSysStatus.m_pStat->test_fsk[FSK_DEMOD_TEST_CH_490KHZ];
        break;

        case FSK_DEMOD_TEST_CH_4209_5KHZ:
            pTest = &g_hSysStatus.m_pStat->test_fsk[FSK_DEMOD_TEST_CH_4209_5KHZ];
        break;
#endif
    }

    if(pTest->start_flag == 0)
    {
        memset(pTest, 0x00, sizeof(FSK_DeMod_Test_s));
        pTest->start_flag = 1;
        pTest->tot_cnt = pRx->st.dt.Test_Fsk.total_count;
        pTest->ch = pRx->st.dt.Test_Fsk.ch;
    }

    // DEBUG_COMM_MSG("[%s] Recv\r\n", __FUNCTION__);
}

void Internal_Rx_Can_Send(Icomm_Protocol_Rx_s *pRx)
{
    Can_Direct_Send(pRx->st.dt.Can_Send.id, pRx->st.dt.Can_Send.data);

    // DEBUG_COMM_MSG("[%s] Recv\r\n", __FUNCTION__);
}

void Internal_Rx_Bam_Data(Icomm_Protocol_Rx_s *pRx)
{
    nmea_uart_enqueue(&nmea_bufs[NMEA_TYPE_BAM], pRx->st.dt.Nmea0183.data, pRx->st.hd.Frm_Len-8);

    // DEBUG_COMM_MSG("[%s] Recv\r\n", __FUNCTION__);
}

void Internal_Rx_System_Parameter_Getting_Request(Icomm_Protocol_Rx_s *pRx)
{
    g_hSysStatus.m_pStat->mode.Ctrl_SystemState = pRx->st.dt.System.State;

    // DEBUG_COMM_MSG("[%s] Recv\r\n", __FUNCTION__);
}

void Internal_Rx_System_Setting_Request(Icomm_Protocol_Rx_s *pRx)
{
    int i = 0;
    u32 bps = 0;
    int type = 0;
    int size = 0;
    int data[10] = {0, };

    type = (int)pRx->st.dt.System_Setting_Req.type;
    size = (int)pRx->st.dt.System_Setting_Req.size;
    for(int i=0; i<size; i++)
    {
        data[i] = (int)pRx->st.dt.System_Setting_Req.data[i];
    }

    if(type == COMM_CMD_SYS_SET_NAVTEX_LOC_CH)
    {
        g_hSysCfg.m_pCfg->nav.ch_set_local = (u8)data[0];
        Internal_Tx_System_Setting_Response(type, size, data[0]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_NAVTEX_LOC_CH type:%d, size:%d, data:%d\r\n", type, size, data[0]);
    }
    else if(type == COMM_CMD_SYS_SET_ANT_TYPE)
    {
        g_hSysCfg.m_pCfg->antenna.type = data[0];
        Internal_Tx_System_Setting_Response(type, size, data[0]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_ANT_TYPE type:%d, size:%d, data:%d\r\n", type, size, data[0]);
    }
    else if(type == COMM_CMD_SYS_SET_INS_MODE_SELECT)
    {
        g_hSysCfg.m_pCfg->ins.mode = data[0];

        TBD_init_setting_uarts(TARGET_UART_INS, g_hSysCfg.m_pCfg->ins.bps, g_hSysCfg.m_pCfg->ins.mode);
        Internal_Tx_System_Setting_Response(type, size, data[0]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_INS_MODE_SELECT type:%d, size:%d, data:%d\r\n", type, size, data[0]);
    }
    else if(type == COMM_CMD_SYS_SET_INS_BPS_SELECT)
    {
        bps = (u32)data[0];
        bps += (u32)(data[1] << 8);
        bps += (u32)(data[2] << 16);
        bps += (u32)(data[3] << 24);
        g_hSysCfg.m_pCfg->ins.bps = (uint32_t)bps;

        TBD_init_setting_uarts(TARGET_UART_INS, g_hSysCfg.m_pCfg->ins.bps, g_hSysCfg.m_pCfg->ins.mode);
        Internal_Tx_System_Setting_Response(type, size, data[0], data[1], data[2], data[3]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_INS_BPS_SELECT type:%d, size:%d, data:%d %d %d %d\r\n", type, size, data[0], data[1], data[2], data[3]);
    }
    else if(type == COMM_CMD_SYS_SET_BAM_MODE_SELECT)
    {
        g_hSysCfg.m_pCfg->bam.mode = data[0];
        
        TBD_init_setting_uarts(TARGET_UART_BAM, g_hSysCfg.m_pCfg->bam.bps, g_hSysCfg.m_pCfg->bam.mode);
        Internal_Tx_System_Setting_Response(type, size, data[0]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_INS_BPS_SELECT type:%d, size:%d, data:%d\r\n", type, size, data[0]);
    }
    else if(type == COMM_CMD_SYS_SET_BAM_BPS_SELECT)
    {
        bps = (u32)data[0];
        bps += (u32)(data[1] << 8);
        bps += (u32)(data[2] << 16);
        bps += (u32)(data[3] << 24);
        g_hSysCfg.m_pCfg->bam.bps = (uint32_t)bps;

        TBD_init_setting_uarts(TARGET_UART_BAM, g_hSysCfg.m_pCfg->bam.bps, g_hSysCfg.m_pCfg->bam.mode);
        Internal_Tx_System_Setting_Response(type, size, data[0], data[1], data[2], data[3]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_BAM_BPS_SELECT type:%d, size:%d, data:%d %d %d %d\r\n", type, size, data[0], data[1], data[2], data[3]);
    }
    else if(type == COMM_CMD_SYS_SET_PRINT_IP_SEARCH)
    {
    }
    else if(type == COMM_CMD_SYS_SET_NETWORK_MODE)
    {
        g_hSysCfg.m_pCfg->network.mode = data[0];

        if(data[0] == MV_VAL_LAN_COMM_DHCP)
        {
            Set_DHCP_State(DHCP_START);
        }
        else
        {
            Set_Static_Network( g_hSysCfg.m_pCfg->network.static_ip,
                                g_hSysCfg.m_pCfg->network.static_netmask,
                                g_hSysCfg.m_pCfg->network.static_gateway);
        }
        
        Internal_Tx_System_Setting_Response(type, size, data[0]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_NETWORK_MODE type:%d, size:%d, data:%d\r\n", type, size, data[0]);
    }
    else if(type == COMM_CMD_SYS_SET_NETWORK_IP_ADDR)
    {
        if(g_hSysCfg.m_pCfg->network.mode == MV_VAL_LAN_COMM_STATIC)
        {
            memcpy(g_hSysCfg.m_pCfg->network.static_ip, data, sizeof(int)*4);
            Set_Static_Network( g_hSysCfg.m_pCfg->network.static_ip,
                                g_hSysCfg.m_pCfg->network.static_netmask,
                                g_hSysCfg.m_pCfg->network.static_gateway);
        }
        Internal_Tx_System_Setting_Response(type, size, data[0], data[1], data[2], data[3]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_NETWORK_IP_ADDR type:%d, size:%d, data:%d %d %d %d\r\n", type, size, data[0], data[1], data[2], data[3]);
    }
    else if(type == COMM_CMD_SYS_SET_NETWORK_NETMASK)
    {
        if(g_hSysCfg.m_pCfg->network.mode == MV_VAL_LAN_COMM_STATIC)
        {
            memcpy(g_hSysCfg.m_pCfg->network.static_netmask, data, sizeof(int)*4);
            Set_Static_Network( g_hSysCfg.m_pCfg->network.static_ip,
                                g_hSysCfg.m_pCfg->network.static_netmask,
                                g_hSysCfg.m_pCfg->network.static_gateway);
        }
        Internal_Tx_System_Setting_Response(type, size, data[0], data[1], data[2], data[3]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_NETWORK_NETMASK type:%d, size:%d, data:%d %d %d %d\r\n", type, size, data[0], data[1], data[2], data[3]);
    }
    else if(type == COMM_CMD_SYS_SET_NETWORK_GATEWAY)
    {
        if(g_hSysCfg.m_pCfg->network.mode == MV_VAL_LAN_COMM_STATIC)
        {
            memcpy(g_hSysCfg.m_pCfg->network.static_gateway, data, sizeof(int)*4);
            Set_Static_Network( g_hSysCfg.m_pCfg->network.static_ip,
                                g_hSysCfg.m_pCfg->network.static_netmask,
                                g_hSysCfg.m_pCfg->network.static_gateway);
        }
        Internal_Tx_System_Setting_Response(type, size, data[0], data[1], data[2], data[3]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_NETWORK_GATEWAY type:%d, size:%d, data:%d %d %d %d\r\n", type, size, data[0], data[1], data[2], data[3]);
    }
    else if(type == COMM_CMD_SYS_SET_NETWORK_SFI_ID)
    {
        for(i=0; i<6; i++)
        {
            g_hSysCfg.m_pCfg->network.sfi_id[i] = (char)data[i];
        }
        Internal_Tx_System_Setting_Response(type, size, data[0], data[1], data[2], data[3], data[4], data[5]);
        DEBUG_MSG("[Recv] COMM_CMD_SYS_SET_NETWORK_SFI_ID type:%d, size:%d, data:%d %d %d %d %d %d\r\n", type, size, data[0], data[1], data[2], data[3], data[4], data[5]);
    }
    else if( type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_MISC  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_TGTD  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_SATD  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_NAVD  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_VDRD  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_RCOM  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_TIME  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_PROP  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_1 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_2 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_3 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_4 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_5 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_6 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_7 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_USR_8 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_BAM_1 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_BAM_2 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_CAM_1 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_CAM_2 )
    {
        g_hSysCfg.m_pCfg->network.tx_group[type - COMM_CMD_SYS_SET_NETWORK_TG_TX_SETUP_MISC] = data[0];
        Internal_Tx_System_Setting_Response(type, size, data[0]);
    }
    else if( type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_MISC  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_TGTD  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_SATD  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_NAVD  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_VDRD  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_RCOM  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_TIME  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_PROP  ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_1 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_2 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_3 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_4 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_5 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_6 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_7 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_USR_8 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_BAM_1 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_BAM_2 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_CAM_1 ||
             type == COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_CAM_2 )
    {
        g_hSysCfg.m_pCfg->network.rx_group[type - COMM_CMD_SYS_SET_NETWORK_TG_RX_SETUP_MISC] = data[0];
        Internal_Tx_System_Setting_Response(type, size, data[0]);
    }

    // DEBUG_COMM_MSG("[%s] Recv\r\n", __FUNCTION__);
}

void Internal_Rx_System_All_Parameter_Send(Icomm_Protocol_Rx_s *pRx)
{
    memcpy(&g_SysConfig, pRx->st.dt.byte.data, sizeof(SystemConfig));
    g_hSysStatus.m_pStat->system_all_para_received_flag = 1;

    TBD_init_setting_uarts(TARGET_UART_INS, g_hSysCfg.m_pCfg->ins.bps, g_hSysCfg.m_pCfg->ins.mode);
    TBD_init_setting_uarts(TARGET_UART_BAM, g_hSysCfg.m_pCfg->bam.bps, g_hSysCfg.m_pCfg->bam.mode);

    osThreadResume(EthernetTaskHandle);

    // DEBUG_COMM_MSG("[%s] Recv\r\n", __FUNCTION__);
}

void Internal_Rx_Rf_Self_BER_Test_Req(Icomm_Protocol_Rx_s *pRx)
{
    g_hSysStatus.m_pStat->test_rf_self.out_select = pRx->st.dt.Test_Self_Rf.out_select;
    g_hSysStatus.m_pStat->test_rf_self.ch_select = pRx->st.dt.Test_Self_Rf.ch_select;
    g_hSysStatus.m_pStat->test_rf_self.mode = pRx->st.dt.Test_Self_Rf.mode;
    g_hSysStatus.m_pStat->test_rf_self.vpp = pRx->st.dt.Test_Self_Rf.vpp;
    g_hSysStatus.m_pStat->test_rf_self.offset = pRx->st.dt.Test_Self_Rf.offset;

    if(g_hSysStatus.m_pStat->test_rf_self.ch_select == 0)
    {
        g_hSysStatus.m_pStat->dac_out_ch = DAC_OUT_CH_INT;
    }
    else if(g_hSysStatus.m_pStat->test_rf_self.ch_select == 1 || g_hSysStatus.m_pStat->test_rf_self.ch_select == 2)
    {
        g_hSysStatus.m_pStat->dac_out_ch = DAC_OUT_CH_LOC;
    }

    Set_Self_BER_Rf_Test(&G_xMsgFskTxMDM);
    // DEBUG_COMM_MSG("[%s] Recv\r\n", __FUNCTION__);
}

void Internal_Rx_FWU(Icomm_Protocol_Rx_s *pRx)
{
    uint8_t *rsp_frame = malloc(pRx->st.hd.Frm_Len + 3);
	rsp_frame[0] = 0x24; // SFD
	memcpy(rsp_frame + 1, &pRx->Buffer[4], pRx->st.hd.Frm_Len + 2);

    u16 cmd = pRx->st.hd.Cmd & 0xFF;
    if (cmd == I_COMM_RX_FIRMWARE_UPDATE_DEVICE_SEARCH_ACK)
    {
        FirmwareUpdate_proxy_device_search_response_udp(rsp_frame, pRx->st.hd.Frm_Len + 3);
    }
	else if (cmd == I_COMM_RX_FIRMWARE_UPDATE_SLAVE_BASE || cmd == I_COMM_RX_FIRMWARE_UPDATE_ENTRY_UPDATE)
    {
        FirmwareUpdate_proxy_fwu_to_tcp(rsp_frame, pRx->st.hd.Frm_Len + 3);
    }

    free(rsp_frame);
}

void Internal_Rx_System_Reset_Request(Icomm_Protocol_Rx_s *pRx)
{
    if(pRx->st.dt.Reset_Req.receiver_bank_swap_flag == 1)
    {
        FLASH_IF_TriggerBankSwapAndReboot();
    }
    else
    {
        NVIC_SystemReset();
    }
}

void Internal_Rx_Parsing(Icomm_Protocol_Rx_s *pRx, u16 crc)
{
    u16 cmd = pRx->st.hd.Cmd;

    switch(cmd & 0xFF)
    {
        case I_COMM_RX_TYPE_SYSTEM_PARAMETER_SENDING_SUCCESS:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_System_Parameter_Sending_Success(pRx);
        break;

        case I_COMM_RX_TYPE_DIAG_REQ:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_Diag_Req(pRx);
        break;

        case I_COMM_RX_TYPE_NMEA0183_RECV:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_Ins_Data(pRx);
        break;

        case I_COMM_RX_TYPE_GPIO_SET:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_Gpio_Set(pRx);
        break;

        case I_COMM_RX_TYPE_FSK_DEMOD_REQ_TEST:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_Fsk_Req_DeMod_Test(pRx);
        break;

        case I_COMM_RX_TYPE_CRC_RESP:
            Handshake_Backend_Response_Recv(pRx->st.dt.Response.cmd, pRx->st.dt.Response.crc);
        break;

        case I_COMM_RX_TYPE_CAN_SEND:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_Can_Send(pRx);
        break;

        case I_COMM_RX_COMM_CHECK:
            Internal_Tx_CRC_Response(crc, cmd);
        break;

        case I_COMM_RX_TYPE_BAM_DATA_SEND:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_Bam_Data(pRx);
        break;

        case I_COMM_RX_TYPE_SYSTEM_PARAMETER_GETTING_REQUEST:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_System_Parameter_Getting_Request(pRx);
        break;

        case I_COMM_RX_SYSTEM_SETTING_REQUEST:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_System_Setting_Request(pRx);
        break;

        case I_COMM_RX_SYSTEM_ALL_PARAMETER_SEND:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_System_All_Parameter_Send(pRx);
        break;

        case I_COMM_RX_RF_SELF_BER_TEST_REQUEST:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_Rf_Self_BER_Test_Req(pRx);
        break;

        case I_COMM_RX_FIRMWARE_UPDATE_ENTRY_UPDATE:
        case I_COMM_RX_FIRMWARE_UPDATE_DEVICE_SEARCH_ACK:
        case I_COMM_RX_FIRMWARE_UPDATE_MASTER_BASE:
        case I_COMM_RX_FIRMWARE_UPDATE_SLAVE_BASE:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_FWU(pRx);
        break;

        case I_COMM_RX_RESET_REQUEST:
            Internal_Tx_CRC_Response(crc, cmd);
            osDelay(I_COMM_RX_DELAY);

            Internal_Rx_System_Reset_Request(pRx);
        break;

        default:
        break;
    }
    Set_Rx_Icomm_Handshake_Status(cmd, COMM_STATE_SET);
}
