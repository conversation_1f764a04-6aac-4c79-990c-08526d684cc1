/**
******************************************************************************
* @file      TargetUart.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef _TARGET_UART_H_
#define _TARGET_UART_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "Common.h"

#define MAX_TARGET_UART_BUF_SIZE	5120
//#define MAX_TARGET_UART_BUF_SIZE	10
#define TARGET_UART_NULL_DATA		-1

// #define TARGET_BAUD_UART_NAVTEX_CONTROL			115200
#define TARGET_BAUD_UART_NAVTEX_CONTROL			1000000
#define TARGET_BAUD_UART_NAVTEX_INTERNAL_450 	115200
#define TARGET_BAUD_UART_NAVTEX_INS				4800
#define TARGET_BAUD_UART_NAVTEX_BAM				4800
#define TARGET_BAUD_UART_NAVTEX_DEBUG 			230400

typedef enum
{
//------_------_------_------_------_------_------_------//
//             USART7, Tx - PF7, Rx - PF6
//------_------_------_------_------_------_------_------//
	TARGET_UART_NAVTEX_CONTROL = 0x00,

//------_------_------_------_------_------_------_------//
//             USART2, Tx - PD5, Rx - PD6
//------_------_------_------_------_------_------_------//
	TARGET_UART_INS,

//------_------_------_------_------_------_------_------//
//             USART4, Tx - PA12, Rx - PA11
//------_------_------_------_------_------_------_------//
	TARGET_UART_BAM,

//------_------_------_------_------_------_------_------//
//             USART1, Tx - PB14, Rx - PB15
//------_------_------_------_------_------_------_------//
	TARGET_UART_DEBUG,

	MAX_TARGET_UART
}target_uart_no_t;

typedef struct uart_buf{
	uint8_t buf[MAX_TARGET_UART_BUF_SIZE];
	int front;
	int rear;
}target_uart_buf_s;

typedef struct _target_uart_s{
	UART_HandleTypeDef *pHandle;
	
	target_uart_buf_s tx;
	uint8_t lock_tx;

	target_uart_buf_s rx;
	uint8_t lock_rx;

	int isEnQeueuIng_tx;

	uint32_t isr_error_cnt;
}target_uart_s;

void TBD_uart_clear_all(target_uart_s *pUart);
uint8_t TBD_uart_init_buf(target_uart_buf_s *pBuf);
int TBD_uart_get_data(uint8_t uart_no);
int TBD_uart_send_data(uint8_t uart_no, uint8_t *pData, int nSize);
int TBD_uart_direct_send_data(uint8_t uart_no, uint8_t *pData, int nSize);
void TBD_uart_send_str(uint8_t uart_no, uint8_t *pBuffer);
uint8_t TBD_config_uart(uint8_t uart_no, uint32_t baud, uint32_t data_bits, uint32_t stop_bit, uint32_t parity, uint32_t auto_bps);

void TBD_init_uarts();
void TBD_init_setting_uarts(uint32_t uart_no, uint32_t buadrate, uint32_t auto_baud);

void TBD_UART_ErrorCallback_Management(void);

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart);
void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart);

extern target_uart_s tbd_uarts[MAX_TARGET_UART] __attribute__((section(".UartDmaSection")));
extern uint8_t tbd_uart_tx_data __attribute__((section(".UartDmaSection")));
extern uint8_t tx_isr_buf[MAX_TARGET_UART][MAX_TARGET_UART_BUF_SIZE] __attribute__((section(".UartDmaSection")));
extern uint8_t tbd_uart_rx_data[MAX_TARGET_UART] __attribute__((section(".UartDmaSection")));

#ifdef __cplusplus
}
#endif

#endif	/* _TARGET_UART_H_ */

