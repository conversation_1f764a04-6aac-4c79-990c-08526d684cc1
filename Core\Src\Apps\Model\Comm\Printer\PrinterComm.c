/**
******************************************************************************
* @file      PrinterComm.c
* <AUTHOR>
* @date      2024-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetBoard.h"
#include "Comm.h"
#include "model.h"

#include "lwip/pbuf.h"
#include "lwip/udp.h"
#include "lwip/tcp.h"

static void Proc_Printer_Eth_Cmd_Send(u16 cnt, ...)
{
    u8 cmd[10] = {0, };
    u16 i = 0;
    va_list ap;

    if(cnt > 10)
    {
        return;
    }

    va_start(ap, cnt);

    for(i=0; i<cnt; i++)
    {
        cmd[i] = va_arg(ap, int);
    }

    va_end(ap);

    Printer_Eth_Send_Data(cmd, cnt);
}

void Printer_Eth_Send_Data(u8 *pBuff, u16 len)
{
    Tcp_Client_Send_Data(TCP_CLIENT_PRINTER, (uint8_t *)pBuff, len);
}

void Proc_Printer_Eth_TextOut(sMessageComponent *pInfo, sMessageString *pMsg)
{
    u8 buf[2] = {0x0d, 0x0a};
    u8 _buf[6] = {'Z', 'C', 'Z', 'C', ' ', ' '};

    Proc_Printer_Eth_Cmd_Send(2, 0x1B, 0x40);           // Print Init
    Proc_Printer_Eth_Cmd_Send(3, 0x1B, 0x21, 0x00);     // Print Font set

    //Printer_Send_Data(buf, 2);

    Printer_Eth_Send_Data(&_buf[0], 6);
    Printer_Eth_Send_Data(&pInfo->ID[0], 4);
    Printer_Eth_Send_Data(buf, 2);
    Printer_Eth_Send_Data(buf, 2);
    Printer_Eth_Send_Data(buf, 2);
    Printer_Eth_Send_Data(buf, 2);
    Printer_Eth_Send_Data(&pMsg->Message[0], pMsg->Message_Len);
    Printer_Eth_Send_Data(buf, 2);
    Printer_Eth_Send_Data(buf, 2);
    Printer_Eth_Send_Data(buf, 2);
    Printer_Eth_Send_Data(buf, 2);

    Proc_Printer_Eth_Cmd_Send(5, 0x1B, 0x64, 0x04, 0x1B, 0x69);     // Print Cutting
}
