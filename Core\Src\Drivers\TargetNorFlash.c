/**
******************************************************************************
* @file      TargetNorFlash.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include <string.h>

#include "TargetBoard.h"
#include "TargetNorFlash.h"

extern QSPI_HandleTypeDef hqspi;

MT25QL128A_Info_t nor_flash_info;

#if 1	// KPB TEST CODE
//uint8_t tmpWriteData[] = "HaHa HoHo KPB'S Nor Flash Example!!!!!\r\n";
uint8_t tmpWriteData[] = "PAPA BOBO KPB'S Nor Flash Example!!!!!\r\n";
uint8_t tmpReadData[sizeof(tmpWriteData)];
//uint8_t tmpWriteData1[] = "Overwrite Test!!!!!\r\n\r\n";
uint8_t sector_write_buf[MT25QL128A_SUBSECTOR_SIZE] = {0, };
uint8_t sector_read_buf[MT25QL128A_SUBSECTOR_SIZE] = {0, };
#endif

/**
 * @brief  
 * @param  
 * @retval 
 * @see
*/
void TBD_nor_flash_get_info(MT25QL128A_Info_t *pInfo)
{
	if(MT25QL128A_GetFlashInfo(pInfo) != MT25QL128A_OK)
	{
		DEBUG_MSG("UNKNOWN FLASH INFORMATION!\r\n");
	}
}

/**
 * @brief  reset nor flash memory
 * @param  pHandle(QSPI_HandleTypeDef *)
 * @retval uint8_t(Target board error number)
 * @see
*/
int32_t TBD_nor_reset_memory(QSPI_HandleTypeDef *pHandle)
{
	int32_t ret = TBD_ERROR_UNKNOWN_FAILURE;
	MT25QL128A_Interface_t mode = MT25QL128A_EXT_SPI_MODE;
#if (TBD_TYPE == BDT_MFHF_ES1)
	if(pHandle != NULL)
	{
		if(MT25QL128A_ResetEnable(pHandle,mode) == MT25QL128A_OK)
		{
			//DEBUG_MSG("TBD_nor_reset_memory -- PASS1\r\n");
			if(MT25QL128A_ResetMemory(pHandle,mode) == MT25QL128A_OK)
			{
				//DEBUG_MSG("TBD_nor_reset_memory -- PASS2\r\n");
				if(MT25QL128A_AutoPollingMemReady(pHandle,mode,HAL_QPSI_TIMEOUT_DEFAULT_VALUE) == MT25QL128A_OK)
				{
					//DEBUG_MSG("TBD_nor_reset_memory -- PASS3\r\n");
					//if(MT25QL128A_ExitQuadMode(pHandle,mode) == MT25QL128A_OK)
					{
						//DEBUG_MSG("TBD_nor_reset_memory -- PASS4\r\n");
						ret = TBD_ERROR_NONE;
					}
				}
			}
		}
	}
#endif
	//DEBUG_MSG("[%s] ret=%ld\r\n",__FUNCTION__, ret);

	return ret;
}

int32_t TBD_nor_erase_block(QSPI_HandleTypeDef *pHandle)
{
	return TBD_ERROR_UNKNOWN_FAILURE;
}

/**
 * @brief  erase entire nor flash memory
 * @param  pHandle(QSPI_HandleTypeDef *)
 * @retval int32_t(Target board error number)
 * @see
*/
int32_t TBD_nor_erase_chip(QSPI_HandleTypeDef *pHandle)
{
	int32_t ret = TBD_ERROR_UNKNOWN_FAILURE;
	MT25QL128A_Interface_t mode = MT25QL128A_EXT_SPI_MODE;
	
#if (TBD_TYPE == BDT_MFHF_ES1)
	if(pHandle != NULL)
	{
		if(MT25QL128A_WriteEnable(pHandle,mode) == MT25QL128A_OK)
		{
			//DEBUG_MSG("TBD_nor_erase_chip -- PASS1\r\n");
			if(MT25QL128A_ChipErase(pHandle,mode) == MT25QL128A_OK)
			{
				//DEBUG_MSG("TBD_nor_erase_chip -- PASS2\r\n");
				if(MT25QL128A_AutoPollingMemReady(pHandle, mode, MT25QL128A_MAX_BULK_ERASE_TIME) == MT25QL128A_OK)
				{
					//DEBUG_MSG("TBD_nor_erase_chip -- PASS3\r\n");
					ret = TBD_ERROR_NONE;
				}					
			}
		}
	}
#endif

	DEBUG_MSG("[TBD_nor_erase_chip]result=%ld\r\n",ret);
	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
*/
int TBD_nor_flash_nav_msg_write(uint32_t Block, uint8_t *pInfoData, uint32_t InfoSize, uint8_t *pMsgData, uint32_t MsgSize)
{
	int ret = 0;
	int ret_1nd = -1;
	int ret_2nd = -1;

	(void)MT25QL128A_Sub4KSectorErase(	&hqspi,
										MT25QL128A_EXT_SPI_MODE,
										(Block*0x1000) );

	ret_1nd = MT25QL128A_PageProgram(	&hqspi,
										MT25QL128A_EXT_SPI_MODE,
										pInfoData,
										(Block*0x1000),
										InfoSize );

	ret_2nd = MT25QL128A_PageProgram(	&hqspi,
										MT25QL128A_EXT_SPI_MODE,
										pMsgData,
										((Block*0x1000)+InfoSize),
										MsgSize );

	if(ret_1nd == -1 || ret_2nd == -1)
	{
		ret = -1;
	}
	else
	{
		ret = 1;
	}

	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
*/
int TBD_nor_flash_nav_msg_read(uint32_t Block, uint8_t *pInfoData, uint32_t InfoSize, uint8_t *pMsgData, uint32_t MsgSize)
{
	int ret = -1;
	int ret_1nd = -1;
	int ret_2nd = -1;

	ret_1nd = MT25QL128A_ReadSTR(	&hqspi,
									MT25QL128A_EXT_SPI_MODE,
									(Block*0x1000),
									pInfoData,
									InfoSize );
								
	ret_2nd = MT25QL128A_ReadSTR(	&hqspi,
									MT25QL128A_EXT_SPI_MODE,
									((Block*0x1000)+InfoSize),
									pMsgData,
									MsgSize );

	if(ret_1nd == -1 || ret_2nd == -1)
	{
		ret = -1;
	}
	else
	{
		ret = 1;
	}

	return ret;
}

/**
 * @brief  
 * @param  
 * @retval 
 * @see
*/
void TBD_init_nor_flash()
{
	TBD_nor_flash_get_info(&nor_flash_info);

	DEBUG_MSG("===========================================\r\n");
	DEBUG_MSG("FLASH SIZE          : %ldMByte\r\n", nor_flash_info.FlashSize/(1024*1024));
	DEBUG_MSG("ERASE SECTOR SIZE   : %ldByte\r\n",  nor_flash_info.EraseSectorSize);
	DEBUG_MSG("ERASE SECTOR NUMBER : %ld\r\n",      nor_flash_info.EraseSectorsNumber);
	DEBUG_MSG("PAGE  SIZE          : %ldByte\r\n",  nor_flash_info.ProgPageSize);
	DEBUG_MSG("PAGE NUMBER         : %ld\r\n",      nor_flash_info.ProgPagesNumber);
	DEBUG_MSG("===========================================\r\n");
	
	TBD_nor_reset_memory(&hqspi);

	TBD_nor_erase_chip(&hqspi);	

#if 0	// KPB TEST CODE
	tmpResult = MT25QL128A_DummyCyclesCfg(&hqspi,MT25QL128A_EXT_SPI_MODE);
	DEBUG_MSG("[Dummy Cycle Config]=%ld\r\n",tmpResult);

	// '24.01.27 dmj code test success
	for(int i=0; i<MT25QL128A_SUBSECTOR_SIZE; i++)
	{
		sector_write_buf[i] = 0x78;
		sector_read_buf[i] = 0x00;
	}

	TBD_nor_erase_chip(&hqspi);	
	// tmpResult = MT25QL128A_EnableMemoryMappedMode(&hqspi,MT25QL128A_EXT_SPI_MODE);
	// DEBUG_MSG("[ENABLE MEMORY MAPPED MODE RESULT]=%ld\r\n",tmpResult);
	//TBD_nor_erase_chip(&hqspi);

	tmpResult = MT25QL128A_PageProgram(&hqspi,MT25QL128A_EXT_SPI_MODE,sector_write_buf,0,10);
	if(MT25QL128A_ReadSTR(&hqspi,MT25QL128A_EXT_SPI_MODE,0,sector_read_buf,10) == MT25QL128A_OK)
	{
		DEBUG_MSG("[WRITE AND READ NOR OK]@@@@####\r\n");

		for(int i=0; i<(10); i++)
		{
			DEBUG_MSG("[%ld]%02x\r\n",i, sector_read_buf[i]);
		}
	}
	
	tmpResult = MT25QL128A_Sub4KSectorErase(&hqspi,MT25QL128A_EXT_SPI_MODE,0);
	//tmpResult = MT25QL128A_Sub32KSectorErase(&hqspi,MT25QL128A_EXT_SPI_MODE,0);
	//tmpResult = MT25QL128A_SectorErase(&hqspi,MT25QL128A_EXT_SPI_MODE,0);
	//TBD_nor_erase_chip(&hqspi);	
	DEBUG_MSG("ERASE RESULT=%d\r\n",tmpResult);
	//TBD_nor_erase_chip(&hqspi);
	{
		if(MT25QL128A_ReadSTR(&hqspi,MT25QL128A_EXT_SPI_MODE,0,sector_read_buf,10) == MT25QL128A_OK)
		{
			DEBUG_MSG("[ERASE SECTOR AND READ NOR OK]@@@@####\r\n");

			for(int i=0; i<(10); i++)
			{
				DEBUG_MSG("[%ld]%02x\r\n",i, sector_read_buf[i]);
			}
		}
	}

	tmpResult = MT25QL128A_Sub32KSectorErase(&hqspi,MT25QL128A_EXT_SPI_MODE,TBD_QSPI_NOR_FLASH_BASE_ADDR);
	DEBUG_MSG("[32K SUBSECTOR ERASE RESULT]=%ld\r\n",tmpResult);
	
	tmpResult = MT25QL128A_SectorErase(&hqspi,MT25QL128A_EXT_SPI_MODE,TBD_QSPI_NOR_FLASH_BASE_ADDR);
	DEBUG_MSG("[SECTOR ERASE RESULT]=%ld\r\n",tmpResult);
#endif	

#if 0	// KPB TEST CODE
	//TBD_nor_erase_chip(&hqspi);

	//tmpResult = MT25QL128A_PageProgram(&hqspi,MT25QL128A_EXT_SPI_MODE,tmpWriteData1,0,sizeof(tmpWriteData1));
	//tmpResult = MT25QL128A_PageProgram(&hqspi,MT25QL128A_EXT_SPI_MODE,tmpWriteData,MT25QL128A_PAGE_SIZE-10,sizeof(tmpWriteData));
	//tmpResult = MT25QL128A_PageProgram(&hqspi,MT25QL128A_EXT_SPI_MODE,tmpWriteData,0,sizeof(tmpWriteData));

	//DEBUG_MSG("[PAGE WRITE RESULT]=%ld",tmpResult);
	
	// if(MT25QL128A_ReadSTR(&hqspi,MT25QL128A_EXT_SPI_MODE,0,sector_read_buf,MT25QL128A_PAGE_SIZE*2) == MT25QL128A_OK)
	// {
	// 	DEBUG_MSG("[READ NOR OK]@@@@####");

	// 	for(int i=0; i<(MT25QL128A_PAGE_SIZE*2); i++)
	// 	{
	// 		DEBUG_MSG("[%ld]Value=%c",i, sector_read_buf[i]);
	// 	}
	// }

	if(MT25QL128A_ReadSTR(&hqspi,MT25QL128A_EXT_SPI_MODE,0,sector_read_buf,MT25QL128A_PAGE_SIZE*2) == MT25QL128A_OK)
	{
		DEBUG_MSG("[READ NOR OK]@@@@####");

		for(int i=0; i<(MT25QL128A_PAGE_SIZE*2); i++)
		{
			DEBUG_MSG("[%ld]Value=%c",i, sector_read_buf[i]);
		}
	}
#endif	

}
