/**
******************************************************************************
* @file      TargetCan.c
* <AUTHOR>
* @date      2024-05-10
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_DRIVERS_TARGETCAN_H_
#define SRC_DRIVERS_TARGETCAN_H_

#include "Common.h"

#define CAN_TX_ID_ALARM_UNIT_PERIODIC   0xA00
#define CAN_TX_ID_ALARM_UNIT_EVENT      0xA80

#define CAN_RX_ID_ALARM_UNIT_PERIODIC   0xA10
#define CAN_RX_ID_ALARM_UNIT_EVENT      0xA20

#define MAX_ALARM_BOX_CNT				(4)

#define MAX_CAN_DATA_SIZE			(8)
#define MAX_CAN_MSG_QUEUE_SIZE	(30)

typedef enum {
    ABOX,
    REMOTE
} can_device_type_t;

typedef struct{
	uint32_t id;
	uint32_t id_type;
	uint8_t data[MAX_CAN_DATA_SIZE];
}can_msg_s;

typedef struct{
	uint32_t front;
	uint32_t rear;
	uint8_t lock_rx;
	can_msg_s rx_msgs[MAX_CAN_MSG_QUEUE_SIZE];
}can_msg_gueue_s;

typedef struct
{
    u32 TxEmptyCnt;
    u32 TxEventFifoCnt;
    u32 TxCompleteCnt;

    u8 TxData[8];

    u32 busoff_cnt;

    u8 RxData[8];
} sDrvCanComponent;
extern sDrvCanComponent gDrvCan;

void TargetCan_Init(void);
void TargetCan_Test_Tx(void);
void TargetCan_Direct_Send_Message(FDCAN_HandleTypeDef *pHandle, uint32_t can_id, uint8_t *p_data);

void TargetCan_Audio_Data_Tx(u8 *pData, u8 len);
void TargetCan_Audio_Tx_Task(void);

void Can_Direct_Send(u32 id, u8 *pData);

#endif /* SRC_DRIVERS_TARGETCAN_H_ */
