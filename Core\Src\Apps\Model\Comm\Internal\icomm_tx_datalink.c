/**
******************************************************************************
* @file      icomm_tx_datalink.c
* <AUTHOR>
* @date      2024-06-14
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "Internal.h"
#include "model.h"
#include "lwip.h"
#include "FW_Update.h"
#include "TargetBoard.h"

void Internal_Tx_CRC_Response(u16 crc, u16 cmd)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(Icomm_Response_CRC_Tx_s);

    pComm->st.hd.Seq_Num  = 0;
    pComm->st.hd.Cmd  = I_COMM_TX_TYPE_CRC_RESP;
    pComm->st.hd.Cmd_Param = 0;

    pComm->st.dt.Response.crc = crc;
    pComm->st.dt.Response.cmd = cmd;

    Internal_Comm_Send((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] cmd:%d, crc:%d\r\n", __FUNCTION__, cmd, crc);
}

void Internal_Tx_System_Parameter_Getting_Request(void)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(Icomm_System_Parameter_Getting_Request_Tx_s);

    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_TYPE_SYSTEM_PARAMETER_GETTING_REQUEST;
    pComm->st.hd.Cmd_Param = 0;

    pComm->st.dt.System_Parameter_Getting_Request.State = g_hSysStatus.m_pStat->mode.system;

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_Diag_Response(int type, int state)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(Icomm_Diag_Resp_Tx_s);

    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_TYPE_DIAG_RESP;
    pComm->st.hd.Cmd_Param = 0;

    pComm->st.dt.DiagResp.type = type;
    pComm->st.dt.DiagResp.state = state;

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_Nav_Msg(sNavtexMsg *pNavMsg, u16 Param, u8 FreqType, u16 ArrayNum, u8 *pData)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    switch(Param)
    {
        case I_COMM_NAV_CMD_PARAM_TYPE_START:
            pComm->st.hd.Frm_Len = sizeof(Icomm_NavMsg_Tx_s);
            pComm->st.hd.Seq_Num = 0;
            pComm->st.hd.Cmd = I_COMM_TX_TYPE_NAVTEX_MESSAGE_SESSION;
            pComm->st.hd.Cmd_Param = I_COMM_NAV_CMD_PARAM_TYPE_START;

            memset(&pComm->st.dt.NavMsg, 0x00, sizeof(Icomm_NavMsg_Tx_s));
            pComm->st.dt.NavMsg.Data_0 = FreqType;
        break;

        case I_COMM_NAV_CMD_PARAM_TYPE_ID:
            pComm->st.hd.Frm_Len = sizeof(Icomm_NavMsg_Id_Tx_s);
            pComm->st.hd.Seq_Num = 0;
            pComm->st.hd.Cmd = I_COMM_TX_TYPE_NAVTEX_MESSAGE_SESSION;
            pComm->st.hd.Cmd_Param = I_COMM_NAV_CMD_PARAM_TYPE_ID;

            memset(&pComm->st.dt.NavMsgId, 0x00, sizeof(Icomm_NavMsg_Id_Tx_s));
            pComm->st.dt.NavMsgId.Data_0 = FreqType;
            pComm->st.dt.NavMsgId.Data_1 = pNavMsg->Info.ID[0];
            pComm->st.dt.NavMsgId.Data_2 = pNavMsg->Info.ID[1];
            pComm->st.dt.NavMsgId.Data_3 = pNavMsg->Info.ID[2];
            pComm->st.dt.NavMsgId.Data_4 = pNavMsg->Info.ID[3];
        break;

        case I_COMM_NAV_CMD_PARAM_TYPE_MSG:
            pComm->st.hd.Frm_Len = sizeof(Icomm_NavMsg_Tx_s);
            pComm->st.hd.Seq_Num = ArrayNum;
            pComm->st.hd.Cmd = I_COMM_TX_TYPE_NAVTEX_MESSAGE_SESSION;
            pComm->st.hd.Cmd_Param = I_COMM_NAV_CMD_PARAM_TYPE_MSG;
            
            memset(&pComm->st.dt.NavMsg, 0x00, sizeof(Icomm_NavMsg_Tx_s));
            pComm->st.dt.NavMsg.Data_0 = FreqType;
            pComm->st.dt.NavMsg.Data_1 = pData[0];
        break;

        case I_COMM_NAV_CMD_PARAM_TYPE_NNNN:
            pComm->st.hd.Frm_Len = sizeof(Icomm_NavMsg_Tx_s);
            pComm->st.hd.Seq_Num = 0;
            pComm->st.hd.Cmd = I_COMM_TX_TYPE_NAVTEX_MESSAGE_SESSION;
            pComm->st.hd.Cmd_Param = I_COMM_NAV_CMD_PARAM_TYPE_NNNN;
            
            memset(&pComm->st.dt.NavMsg, 0x00, sizeof(Icomm_NavMsg_Tx_s));
            pComm->st.dt.NavMsg.Data_0 = FreqType;
        break;

        case I_COMM_NAV_CMD_PARAM_TYPE_END:
            pComm->st.hd.Frm_Len = sizeof(Icomm_NavMsg_End_Tx_s);
            pComm->st.hd.Seq_Num = 0;
            pComm->st.hd.Cmd = I_COMM_TX_TYPE_NAVTEX_MESSAGE_SESSION;
            pComm->st.hd.Cmd_Param = I_COMM_NAV_CMD_PARAM_TYPE_END;
            
            memset(&pComm->st.dt.NavMsgEnd, 0x00, sizeof(Icomm_NavMsg_End_Tx_s));
            pComm->st.dt.NavMsgEnd.Data_0 = FreqType;
            pComm->st.dt.NavMsgEnd.Data_1 = (u8) (pNavMsg->Info.Total_Character_Len       & 0x00ff);
            pComm->st.dt.NavMsgEnd.Data_2 = (u8)((pNavMsg->Info.Total_Character_Len >> 8) & 0x00ff);
            pComm->st.dt.NavMsgEnd.Data_3 = (u8) (pNavMsg->Info.Error_Character_Len       & 0x00ff);
            pComm->st.dt.NavMsgEnd.Data_4 = (u8)((pNavMsg->Info.Error_Character_Len >> 8) & 0x00ff);
            pComm->st.dt.NavMsgEnd.Data_5 = (u8) (pNavMsg->Info.ErrorRate                 & 0x00ff);
            pComm->st.dt.NavMsgEnd.Data_6 = (u8)((pNavMsg->Info.ErrorRate           >> 8) & 0x00ff);
        break;

        case I_COMM_NAV_CMD_PARAM_TYPE_ERROR:
            pComm->st.hd.Frm_Len = sizeof(Icomm_NavMsg_Tx_s);
            pComm->st.hd.Seq_Num = 0;
            pComm->st.hd.Cmd = I_COMM_TX_TYPE_NAVTEX_MESSAGE_SESSION;
            pComm->st.hd.Cmd_Param = I_COMM_NAV_CMD_PARAM_TYPE_ERROR;

            memset(&pComm->st.dt.NavMsg, 0x00, sizeof(Icomm_NavMsg_Tx_s));
            pComm->st.dt.NavMsg.Data_0 = FreqType;
            pComm->st.dt.NavMsg.Data_1 = pData[0];
        break;

        case I_COMM_NAV_CMD_PARAM_TYPE_LOCAL_FREQ_CHANGED:
            pComm->st.hd.Frm_Len = sizeof(Icomm_NavMsg_Tx_s);
            pComm->st.hd.Seq_Num = 0;
            pComm->st.hd.Cmd = I_COMM_TX_TYPE_NAVTEX_MESSAGE_SESSION;
            pComm->st.hd.Cmd_Param = I_COMM_NAV_CMD_PARAM_TYPE_LOCAL_FREQ_CHANGED;

            memset(&pComm->st.dt.NavMsg, 0x00, sizeof(Icomm_NavMsg_Tx_s));
            pComm->st.dt.NavMsg.Data_0 = FreqType;
        break;

        default:
        break;
    }

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_Nmea0183_Data(u8 *pData, u16 len)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(u8)*len;
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_TYPE_MSG_NMEA0183_SEND;
    pComm->st.hd.Cmd_Param = 0;

    memcpy(pComm->st.dt.Nmea0183.data, pData, sizeof(u8)*len);

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_Fsk_Resp_DeMod_Test(FSK_DeMod_Test_s *pTest, int ch)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(Icomm_Fsk_DeMod_Test_Resp_Tx_s);
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_TYPE_FSK_DEMOD_RESP_TEST;
    pComm->st.hd.Cmd_Param = 0;

    pComm->st.dt.Test_Fsk.total_count = pTest->tot_cnt;
    pComm->st.dt.Test_Fsk.ch = ch;
    pComm->st.dt.Test_Fsk.mark_count = pTest->mark_cnt;
    pComm->st.dt.Test_Fsk.space_count = pTest->space_cnt;

    Internal_Comm_Send((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_Parameter_State_Update(void)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;
    int i = 0;

    pComm->st.hd.Frm_Len = sizeof(Icomm_Parameter_State_Tx_s);
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_TYPE_PARAMETER_STATE;
    pComm->st.hd.Cmd_Param = 0;

    pComm->st.dt.Parameter_State.timedate.bits.year     = g_hSysStatus.m_pStat->timedate.bits.year;
    pComm->st.dt.Parameter_State.timedate.bits.month    = g_hSysStatus.m_pStat->timedate.bits.month;
    pComm->st.dt.Parameter_State.timedate.bits.day      = g_hSysStatus.m_pStat->timedate.bits.day;
    pComm->st.dt.Parameter_State.timedate.bits.hour     = g_hSysStatus.m_pStat->timedate.bits.hour;
    pComm->st.dt.Parameter_State.timedate.bits.min      = g_hSysStatus.m_pStat->timedate.bits.min;
    pComm->st.dt.Parameter_State.timedate.bits.sec      = g_hSysStatus.m_pStat->timedate.bits.sec;

    for(i=0; i<4; i++)
    {
        pComm->st.dt.Parameter_State.Ver_Receive_Hw[i] = (u8)g_hSysStatus.m_pStat->ver.Receive_Hw[i];
        pComm->st.dt.Parameter_State.Ver_Receive_Sw[i] = (u8)g_hSysStatus.m_pStat->ver.Receive_Sw[i];
    }

    pComm->st.dt.Parameter_State.Adc_Ant = (u16)gDrvAdc.filtered_Adc_ANT_SENSE;
    pComm->st.dt.Parameter_State.Adc_Int_Ch = (u16)gDrvAdc.Adc_RF_518KHz;
    pComm->st.dt.Parameter_State.Adc_Loc_Ch = (u16)gDrvAdc.Adc_RF_490KHz;
    pComm->st.dt.Parameter_State.Adc_VREF = 0;
    pComm->st.dt.Parameter_State.Adc_PCB_Ver_1 = (u16)gDrvAdc.filtered_Adc_Ver_1;
    pComm->st.dt.Parameter_State.Adc_PCB_Ver_2 = (u16)gDrvAdc.filtered_Adc_Ver_2;
    
    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_Can_Received_Data(u32 ID, u8 *pData, u8 len)
{
    int i = 0;
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(Icomm_CAN_Received_Data_Pass_Tx_s);
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_TYPE_CAN_RECEIVED_DATA_PASS;
    pComm->st.hd.Cmd_Param = 0;

    pComm->st.dt.Can_Received.id = ID;
    for(i=0; i<len; i++)
    {
        pComm->st.dt.Can_Received.data[i] = pData[i];
    }
    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_System_Parameter_Sending_Success(void)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(Icomm_System_Parameter_Sending_Success_Tx_s);
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_TYPE_SYSTEM_PARAMETER_SENDING_SUCCESS;
    pComm->st.hd.Cmd_Param = 0;

    pComm->st.dt.Sys_Para_Sending_Success.State = g_hSysStatus.m_pStat->mode.system;

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_System_Setting_Response(int type, int n, ...)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(Icomm_System_Setting_Response_Tx_s);
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_SYSTEM_SETTING_RESPONSE;
    pComm->st.hd.Cmd_Param = 0;

    pComm->st.dt.System_Setting_Resp.type = (u16)type;
    pComm->st.dt.System_Setting_Resp.size = (u16)n;

    memset(pComm->st.dt.System_Setting_Resp.data, 0x00, sizeof(u16)*10);
    va_list ap;
    va_start(ap, n);
    for(int i=0; i<n; i++)
    {
        pComm->st.dt.System_Setting_Resp.data[i] = (u16)va_arg(ap, int);
    }
    va_end(ap);

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_System_All_Parameter_Send(void)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(Product_Version_s);
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_SYSTEM_ALL_PARAMETER_SEND;
    pComm->st.hd.Cmd_Param = 0;

    memcpy(pComm->st.dt.byte.data, &g_curSysStatus.ver, sizeof(Product_Version_s));

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_Network_Setting_Info(int eth_link, int mode, ip_addr_t ip, ip_addr_t netmask, ip_addr_t gateway)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(Icomm_Network_Setting_Info_Tx_s);
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_NETWORK_SETTING_INFO;
    pComm->st.hd.Cmd_Param = 0;

    pComm->st.dt.Network_Setting_Info.eth_link = (u8)eth_link;
    pComm->st.dt.Network_Setting_Info.mode = (u8)mode;
    pComm->st.dt.Network_Setting_Info.ip[0] = ((u8)ip4_addr1(&ip));
    pComm->st.dt.Network_Setting_Info.ip[1] = ((u8)ip4_addr2(&ip));
    pComm->st.dt.Network_Setting_Info.ip[2] = ((u8)ip4_addr3(&ip));
    pComm->st.dt.Network_Setting_Info.ip[3] = ((u8)ip4_addr4(&ip));
    pComm->st.dt.Network_Setting_Info.netmask[0] = ((u8)ip4_addr1(&netmask));
    pComm->st.dt.Network_Setting_Info.netmask[1] = ((u8)ip4_addr2(&netmask));
    pComm->st.dt.Network_Setting_Info.netmask[2] = ((u8)ip4_addr3(&netmask));
    pComm->st.dt.Network_Setting_Info.netmask[3] = ((u8)ip4_addr4(&netmask));
    pComm->st.dt.Network_Setting_Info.gateway[0] = ((u8)ip4_addr1(&gateway));
    pComm->st.dt.Network_Setting_Info.gateway[1] = ((u8)ip4_addr2(&gateway));
    pComm->st.dt.Network_Setting_Info.gateway[2] = ((u8)ip4_addr3(&gateway));
    pComm->st.dt.Network_Setting_Info.gateway[3] = ((u8)ip4_addr4(&gateway));

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_Rf_Self_BER_Test_Response(int n, ...)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = sizeof(Icomm_Rf_Self_Test_Res_Tx_s);
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_RF_SELF_BER_TEST_RESPONSE;
    pComm->st.hd.Cmd_Param = 0;

    va_list ap;
    va_start(ap, n);
    pComm->st.dt.Test_Self_Rf.ch_select     = (u8)va_arg(ap, int);
    pComm->st.dt.Test_Self_Rf.tot_cnt       = (u8)va_arg(ap, int);
    pComm->st.dt.Test_Self_Rf.mark_cnt      = (u8)va_arg(ap, int);
    pComm->st.dt.Test_Self_Rf.space_cnt     = (u8)va_arg(ap, int);
    va_end(ap);

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_FWU_Request_Device_Fw_Info(void)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = 0;
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = I_COMM_TX_FIRMWARE_UPDATE_DEVICE_SEARCH;
    pComm->st.hd.Cmd_Param = 0;

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // Internal_Comm_Send((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_FWU_Cmd_ex(uint16_t cmd, uint16_t sub_cmd, uint8_t *p_data, uint16_t len)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    pComm->st.hd.Frm_Len = len;
    pComm->st.hd.Seq_Num = 0;
    pComm->st.hd.Cmd = cmd;
    pComm->st.hd.Cmd_Param = sub_cmd;

    memcpy(pComm->st.dt.byte.data, p_data, len);

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // Internal_Comm_Send((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}

void Internal_Tx_FWU_Cmd(uint8_t *frame_buf, uint32_t frame_len)
{
    Icomm_Protocol_Tx_s *pComm = &gIcommData_Tx;

    memcpy(&pComm->Buffer[4], &frame_buf[1], frame_len);

    pComm->st.hd.Frm_Len -= sizeof(Icomm_HeaderFrame_s);

    Internal_Comm_Handshake_EnQueue((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // Internal_Comm_Send((Icomm_Protocol_Tx_s *)pComm->Buffer);
    // DEBUG_COMM_MSG("[%s] Send\r\n", __FUNCTION__);
}