/*
 * flash_if.h
 *
 *  Created on: Apr 1, 2025
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#ifndef FWUPDATE_FLASH_IF_H_
#define FWUPDATE_FLASH_IF_H_

#include "stdint.h"
#include "stm32h7xx_hal.h" // HAL 타입 사용

// 플래시 작업 결과 타입 정의
typedef enum
{
	FLASH_IF_OK = 0x00U,
	FLASH_IF_ERASE_ERROR = 0x01U,
	FLASH_IF_WRITE_ERROR = 0x02U,
	FLASH_IF_PARAM_ERROR = 0x03U,
	FLASH_IF_PROTECTION_ERR = 0x04U,
	FLASH_IF_ERROR = 0xFFU
} FLASH_StatusTypeDef;

// --- 함수 프로토타입 ---

/**
 * @brief 현재 사용중인 뱅크 확인
 * @retval uint8_t 현재 사용중인 뱅크 (FLASH_BANK_1 or FLASH_BANK_2)
 */
uint8_t FLASH_IF_GetCurrentBank(void);

/**
 * @brief 지정된 주소/길이가 비활성 뱅크의 유효 범위 내에 있고 정렬되었는지 확인
 * @param write_addr 검사할 시작 주소 (CPU 주소, 예: 0x081xxxxx)
 * @param len 검사할 길이
 * @retval 1: 유효, 0: 유효하지 않음
 */
uint8_t FLASH_IF_IsAddressValid(uint32_t write_addr, uint32_t len);

/**
 * @brief 지정된 시작 주소의 플래시 섹터(128KB) 지우기
 * @param sector_start_addr 지울 섹터의 시작 주소
 * @retval FLASH_IF_OK: 성공, FLASH_IF_ERASE_ERROR: 실패
 */
FLASH_StatusTypeDef FLASH_IF_EraseSector(uint32_t sector_start_addr);

/**
 * @brief 플래시 메모리에 데이터 페이지(RAM 버퍼 크기만큼) 쓰기
 * @note 이 함수는 지정된 주소에 데이터를 씁니다. 섹터 지우기는 별도로 호출해야 할 수 있습니다.
 * (또는 이 함수 내부에서 필요시 섹터 지우기 수행)
 * @param page_start_addr 데이터를 쓸 시작 주소
 * @param data 기록할 데이터 포인터 (RAM 버퍼)
 * @param data_size 기록할 실제 데이터 크기 (바이트 단위)
 * @retval FLASH_IF_OK: 성공, FLASH_IF_WRITE_ERROR: 실패
 */
FLASH_StatusTypeDef FLASH_IF_WritePage(uint32_t page_start_addr, uint8_t *data, uint32_t data_size);

/**
 * @brief 플래시 메모리에 데이터 쓰기 (32바이트 단위)
 */
FLASH_StatusTypeDef FLASH_IF_WriteData(uint32_t dest_addr, uint8_t *data, uint32_t data_size);

/**
 * @brief 옵션 바이트를 수정하여 뱅크 스왑을 설정하고 시스템 리셋
 * @note 이 함수는 실행 후 돌아오지 않습니다 (리셋됨). 매우 주의해서 사용해야 합니다.
 */
void FLASH_IF_TriggerBankSwapAndReboot(void);

#endif /* FWUPDATE_FLASH_IF_H_ */
