/**
******************************************************************************
* @file      TargetTCP.h
* <AUTHOR>
* @date      2024-6-10
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_DRIVERS_ETHERNET_TARGETTCP_H_
#define SRC_DRIVERS_ETHERNET_TARGETTCP_H_

#include "Common.h"
#include "lwip/opt.h"
#include "lwip/api.h"
#include "lwip/sys.h"
#include "netif.h"
#include "udp.h"
#include "tcp.h"
#include "igmp.h"

typedef enum
{
    TCP_CLIENT_STATE_START = 0,

    TCP_CLIENT_STATE_NOT_CONNECTED,
    TCP_CLIENT_STATE_CONNECTED,
    TCP_CLIENT_STATE_RECEIVED,
    TCP_CLIENT_STATE_CLOSING,

    TCP_CLIENT_STATE_MAX,
}tcp_client_states_t;

typedef enum
{
    TCP_CLIENT_ERROR_START = 0,
    TCP_CLIENT_ERROR_NONE = 0,

    TCP_CLIENT_ERROR_MAX,
}tcp_client_errors_t;

typedef enum
{
    TCP_TYPE_START = 0,

    TCP_CLIENT_PRINTER = TCP_TYPE_START,

    TCP_TYPE_MAX,
}tcp_ip_port_pcb_t;

typedef struct
{
    ip4_addr_t local_ip;
    u16_t local_port;
    ip4_addr_t remote_ip;
    u16_t remote_port;
    u8 cur_state;
    u8 tgt_state;
    u8 err_state;
    u8 err_cb_state;
    struct tcp_pcb *pcb;
}tcp_ip_port_pcb_s;


void Tcp_Ip_Client_Config(void const *argument);
void Tcp_Ip_Server_Config(void const *argument);
void Tcp_Client_Connect(tcp_ip_port_pcb_t type);
void Tcp_Client_Disconnect(tcp_ip_port_pcb_t type);
err_t Tcp_Client_Connected_Callback(void *arg, struct tcp_pcb *tpcb, err_t err);
err_t Tcp_Client_Recv_Callback(void *arg, struct tcp_pcb *tpcb, struct pbuf *p, err_t err);
err_t Tcp_Client_Sent_Callback(void *arg, struct tcp_pcb *tpcb, u16_t len);
err_t Tcp_Client_Poll_Callback(void *arg, struct tcp_pcb *tpcb);
void Tcp_Client_Error_Callback(void *arg, err_t err);
void Tcp_Client_Send_Str(tcp_ip_port_pcb_t type, char *pStr);
void Tcp_Client_Send_Data(tcp_ip_port_pcb_t type, u8 *pData, u16 len);
void TargetTCP_Init(void const *argument);


#endif /* SRC_DRIVERS_ETHERNET_TARGETTCP_H_ */
