#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_3
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_5
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_9
ADC1.ClockPrescaler=ADC_CLOCK_ASYNC_DIV6
ADC1.ClockPrescalerADC3=ADC_CLOCK_ASYNC_DIV256
ADC1.ContinuousConvMode=DISABLE
ADC1.ConversionDataManagement=ADC_CONVERSIONDATA_DMA_CIRCULAR
ADC1.ExternalTrigConv=ADC_EXTERNALTRIG_HR1_ADCTRG1
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,OffsetNumber-0\#ChannelRegularConversion,OffsetSignedSaturation-0\#ChannelRegularConversion,NbrOfConversionFlag,master,ClockPrescaler,ContinuousConvMode,NbrOfConversion,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,OffsetSignedSaturation-1\#ChannelRegularConversion,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,OffsetSignedSaturation-2\#ChannelRegularConversion,ClockPrescalerADC3,ConversionDataManagement,Resolution,ExternalTrigConv
ADC1.NbrOfConversion=3
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-0\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetSignedSaturation-0\#ChannelRegularConversion=DISABLE
ADC1.OffsetSignedSaturation-1\#ChannelRegularConversion=DISABLE
ADC1.OffsetSignedSaturation-2\#ChannelRegularConversion=DISABLE
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.Rank-1\#ChannelRegularConversion=2
ADC1.Rank-2\#ChannelRegularConversion=3
ADC1.Resolution=ADC_RESOLUTION_16B
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_8CYCLES_5
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_8CYCLES_5
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_8CYCLES_5
ADC1.master=1
ADC3.Channel-10\#ChannelRegularConversion=ADC_CHANNEL_6
ADC3.Channel-8\#ChannelRegularConversion=ADC_CHANNEL_VREFINT
ADC3.Channel-9\#ChannelRegularConversion=ADC_CHANNEL_1
ADC3.ClockPrescaler=ADC_CLOCK_ASYNC_DIV6
ADC3.ClockPrescalerADC3=ADC_CLOCK_ASYNC_DIV256
ADC3.ContinuousConvMode=DISABLE
ADC3.ConversionDataManagement=ADC_CONVERSIONDATA_DMA_CIRCULAR
ADC3.ExternalTrigConv=ADC_EXTERNALTRIG_HR1_ADCTRG3
ADC3.IPParameters=Rank-8\#ChannelRegularConversion,Channel-8\#ChannelRegularConversion,SamplingTime-8\#ChannelRegularConversion,OffsetNumber-8\#ChannelRegularConversion,OffsetSignedSaturation-8\#ChannelRegularConversion,NbrOfConversionFlag,ClockPrescalerADC3,ContinuousConvMode,ClockPrescaler,ConversionDataManagement,Resolution,ExternalTrigConv,Rank-9\#ChannelRegularConversion,Channel-9\#ChannelRegularConversion,SamplingTime-9\#ChannelRegularConversion,OffsetNumber-9\#ChannelRegularConversion,OffsetSignedSaturation-9\#ChannelRegularConversion,NbrOfConversion,Rank-10\#ChannelRegularConversion,Channel-10\#ChannelRegularConversion,SamplingTime-10\#ChannelRegularConversion,OffsetNumber-10\#ChannelRegularConversion,OffsetSignedSaturation-10\#ChannelRegularConversion
ADC3.NbrOfConversion=3
ADC3.NbrOfConversionFlag=1
ADC3.OffsetNumber-10\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC3.OffsetNumber-8\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC3.OffsetNumber-9\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC3.OffsetSignedSaturation-10\#ChannelRegularConversion=DISABLE
ADC3.OffsetSignedSaturation-8\#ChannelRegularConversion=DISABLE
ADC3.OffsetSignedSaturation-9\#ChannelRegularConversion=DISABLE
ADC3.Rank-10\#ChannelRegularConversion=3
ADC3.Rank-8\#ChannelRegularConversion=1
ADC3.Rank-9\#ChannelRegularConversion=2
ADC3.Resolution=ADC_RESOLUTION_16B
ADC3.SamplingTime-10\#ChannelRegularConversion=ADC_SAMPLETIME_16CYCLES_5
ADC3.SamplingTime-8\#ChannelRegularConversion=ADC_SAMPLETIME_16CYCLES_5
ADC3.SamplingTime-9\#ChannelRegularConversion=ADC_SAMPLETIME_16CYCLES_5
CAD.formats=
CAD.pinconfig=
CAD.provider=
CORTEX_M7.AccessPermission-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission-Cortex_Memory_Protection_Unit_Region2_Settings=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission-Cortex_Memory_Protection_Unit_Region3_Settings=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission_S-Cortex_Memory_Protection_Unit_Region4_Settings_S=MPU_REGION_FULL_ACCESS
CORTEX_M7.AccessPermission_S-Cortex_Memory_Protection_Unit_Region5_Settings_S=MPU_REGION_FULL_ACCESS
CORTEX_M7.BaseAddress-Cortex_Memory_Protection_Unit_Region1_Settings=0xc0000000
CORTEX_M7.BaseAddress-Cortex_Memory_Protection_Unit_Region2_Settings=TBD_LWIP_HEAP_ADDR
CORTEX_M7.BaseAddress-Cortex_Memory_Protection_Unit_Region3_Settings=0x30000000
CORTEX_M7.BaseAddress_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=0xc0000000
CORTEX_M7.BaseAddress_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=0x30000000
CORTEX_M7.BaseAddress_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=0x30000400
CORTEX_M7.BaseAddress_S-Cortex_Memory_Protection_Unit_Region4_Settings_S=0x30004d84
CORTEX_M7.BaseAddress_S-Cortex_Memory_Protection_Unit_Region5_Settings_S=0x30040000
CORTEX_M7.CPU_DCache=Enabled
CORTEX_M7.CPU_ICache=Enabled
CORTEX_M7.DisableExec-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_INSTRUCTION_ACCESS_DISABLE
CORTEX_M7.DisableExec-Cortex_Memory_Protection_Unit_Region2_Settings=MPU_INSTRUCTION_ACCESS_DISABLE
CORTEX_M7.DisableExec-Cortex_Memory_Protection_Unit_Region3_Settings=MPU_INSTRUCTION_ACCESS_DISABLE
CORTEX_M7.DisableExec_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_INSTRUCTION_ACCESS_DISABLE
CORTEX_M7.DisableExec_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_INSTRUCTION_ACCESS_ENABLE
CORTEX_M7.DisableExec_S-Cortex_Memory_Protection_Unit_Region4_Settings_S=MPU_INSTRUCTION_ACCESS_ENABLE
CORTEX_M7.Enable-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_REGION_ENABLE
CORTEX_M7.Enable-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_REGION_ENABLE
CORTEX_M7.Enable-Cortex_Memory_Protection_Unit_Region2_Settings=MPU_REGION_ENABLE
CORTEX_M7.Enable-Cortex_Memory_Protection_Unit_Region3_Settings=MPU_REGION_ENABLE
CORTEX_M7.Enable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_REGION_ENABLE
CORTEX_M7.Enable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_REGION_ENABLE
CORTEX_M7.Enable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_REGION_ENABLE
CORTEX_M7.Enable_S-Cortex_Memory_Protection_Unit_Region4_Settings_S=MPU_REGION_ENABLE
CORTEX_M7.Enable_S-Cortex_Memory_Protection_Unit_Region5_Settings_S=MPU_REGION_ENABLE
CORTEX_M7.IPParameters=CPU_ICache,CPU_DCache,Enable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,Size_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,BaseAddress_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,AccessPermission_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,IsShareable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,IsCacheable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,IsBufferable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S,Enable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,Size_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,TypeExtField_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,AccessPermission_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,DisableExec_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,BaseAddress_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,Enable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,Size_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,BaseAddress_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,AccessPermission_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,DisableExec_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,IsShareable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,IsBufferable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,MPU_Control,Enable-Cortex_Memory_Protection_Unit_Region0_Settings,Size-Cortex_Memory_Protection_Unit_Region0_Settings,SubRegionDisable-Cortex_Memory_Protection_Unit_Region0_Settings,AccessPermission-Cortex_Memory_Protection_Unit_Region0_Settings,DisableExec-Cortex_Memory_Protection_Unit_Region0_Settings,IsShareable-Cortex_Memory_Protection_Unit_Region0_Settings,Enable-Cortex_Memory_Protection_Unit_Region1_Settings,BaseAddress-Cortex_Memory_Protection_Unit_Region1_Settings,Size-Cortex_Memory_Protection_Unit_Region1_Settings,AccessPermission-Cortex_Memory_Protection_Unit_Region1_Settings,IsShareable-Cortex_Memory_Protection_Unit_Region1_Settings,IsCacheable-Cortex_Memory_Protection_Unit_Region1_Settings,IsBufferable-Cortex_Memory_Protection_Unit_Region1_Settings,Enable-Cortex_Memory_Protection_Unit_Region2_Settings,Size-Cortex_Memory_Protection_Unit_Region2_Settings,BaseAddress-Cortex_Memory_Protection_Unit_Region2_Settings,TypeExtField-Cortex_Memory_Protection_Unit_Region2_Settings,AccessPermission-Cortex_Memory_Protection_Unit_Region2_Settings,DisableExec-Cortex_Memory_Protection_Unit_Region2_Settings,Enable-Cortex_Memory_Protection_Unit_Region3_Settings,BaseAddress-Cortex_Memory_Protection_Unit_Region3_Settings,Size-Cortex_Memory_Protection_Unit_Region3_Settings,AccessPermission-Cortex_Memory_Protection_Unit_Region3_Settings,DisableExec-Cortex_Memory_Protection_Unit_Region3_Settings,IsShareable-Cortex_Memory_Protection_Unit_Region3_Settings,IsBufferable-Cortex_Memory_Protection_Unit_Region3_Settings,default_mode_Activation,Enable_S-Cortex_Memory_Protection_Unit_Region4_Settings_S,BaseAddress_S-Cortex_Memory_Protection_Unit_Region4_Settings_S,Size_S-Cortex_Memory_Protection_Unit_Region4_Settings_S,IsShareable_S-Cortex_Memory_Protection_Unit_Region4_Settings_S,IsCacheable_S-Cortex_Memory_Protection_Unit_Region4_Settings_S,IsBufferable_S-Cortex_Memory_Protection_Unit_Region4_Settings_S,AccessPermission_S-Cortex_Memory_Protection_Unit_Region4_Settings_S,DisableExec_S-Cortex_Memory_Protection_Unit_Region4_Settings_S,IsShareable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,IsBufferable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,Enable_S-Cortex_Memory_Protection_Unit_Region5_Settings_S,IsCacheable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S,IsCacheable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S,BaseAddress_S-Cortex_Memory_Protection_Unit_Region5_Settings_S,Size_S-Cortex_Memory_Protection_Unit_Region5_Settings_S,IsShareable_S-Cortex_Memory_Protection_Unit_Region5_Settings_S,IsCacheable_S-Cortex_Memory_Protection_Unit_Region5_Settings_S,IsBufferable_S-Cortex_Memory_Protection_Unit_Region5_Settings_S,AccessPermission_S-Cortex_Memory_Protection_Unit_Region5_Settings_S
CORTEX_M7.IPParametersWithoutCheck=BaseAddress-Cortex_Memory_Protection_Unit_Region2_Settings,BaseAddress_S-Cortex_Memory_Protection_Unit_Region2_Settings_S
CORTEX_M7.IsBufferable-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_ACCESS_BUFFERABLE
CORTEX_M7.IsBufferable-Cortex_Memory_Protection_Unit_Region3_Settings=MPU_ACCESS_BUFFERABLE
CORTEX_M7.IsBufferable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_ACCESS_BUFFERABLE
CORTEX_M7.IsBufferable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_ACCESS_BUFFERABLE
CORTEX_M7.IsBufferable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_ACCESS_BUFFERABLE
CORTEX_M7.IsBufferable_S-Cortex_Memory_Protection_Unit_Region4_Settings_S=MPU_ACCESS_BUFFERABLE
CORTEX_M7.IsBufferable_S-Cortex_Memory_Protection_Unit_Region5_Settings_S=MPU_ACCESS_BUFFERABLE
CORTEX_M7.IsCacheable-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_ACCESS_CACHEABLE
CORTEX_M7.IsCacheable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_ACCESS_NOT_CACHEABLE
CORTEX_M7.IsCacheable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_ACCESS_NOT_CACHEABLE
CORTEX_M7.IsCacheable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_ACCESS_CACHEABLE
CORTEX_M7.IsCacheable_S-Cortex_Memory_Protection_Unit_Region4_Settings_S=MPU_ACCESS_NOT_CACHEABLE
CORTEX_M7.IsCacheable_S-Cortex_Memory_Protection_Unit_Region5_Settings_S=MPU_ACCESS_CACHEABLE
CORTEX_M7.IsShareable-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_ACCESS_SHAREABLE
CORTEX_M7.IsShareable-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_ACCESS_SHAREABLE
CORTEX_M7.IsShareable-Cortex_Memory_Protection_Unit_Region3_Settings=MPU_ACCESS_SHAREABLE
CORTEX_M7.IsShareable_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_ACCESS_SHAREABLE
CORTEX_M7.IsShareable_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_ACCESS_SHAREABLE
CORTEX_M7.IsShareable_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_ACCESS_SHAREABLE
CORTEX_M7.IsShareable_S-Cortex_Memory_Protection_Unit_Region4_Settings_S=MPU_ACCESS_SHAREABLE
CORTEX_M7.IsShareable_S-Cortex_Memory_Protection_Unit_Region5_Settings_S=MPU_ACCESS_SHAREABLE
CORTEX_M7.MPU_Control=MPU_PRIVILEGED_DEFAULT
CORTEX_M7.Size-Cortex_Memory_Protection_Unit_Region0_Settings=MPU_REGION_SIZE_4GB
CORTEX_M7.Size-Cortex_Memory_Protection_Unit_Region1_Settings=MPU_REGION_SIZE_32MB
CORTEX_M7.Size-Cortex_Memory_Protection_Unit_Region2_Settings=MPU_REGION_SIZE_32KB
CORTEX_M7.Size-Cortex_Memory_Protection_Unit_Region3_Settings=MPU_REGION_SIZE_1KB
CORTEX_M7.Size_S-Cortex_Memory_Protection_Unit_Region1_Settings_S=MPU_REGION_SIZE_32MB
CORTEX_M7.Size_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_REGION_SIZE_2KB
CORTEX_M7.Size_S-Cortex_Memory_Protection_Unit_Region3_Settings_S=MPU_REGION_SIZE_32KB
CORTEX_M7.Size_S-Cortex_Memory_Protection_Unit_Region4_Settings_S=MPU_REGION_SIZE_256KB
CORTEX_M7.Size_S-Cortex_Memory_Protection_Unit_Region5_Settings_S=MPU_REGION_SIZE_32KB
CORTEX_M7.SubRegionDisable-Cortex_Memory_Protection_Unit_Region0_Settings=0x87
CORTEX_M7.TypeExtField-Cortex_Memory_Protection_Unit_Region2_Settings=MPU_TEX_LEVEL1
CORTEX_M7.TypeExtField_S-Cortex_Memory_Protection_Unit_Region2_Settings_S=MPU_TEX_LEVEL0
CORTEX_M7.default_mode_Activation=1
DAC1.DAC_Channel-DAC_OUT1=DAC_CHANNEL_1
DAC1.DAC_Channel-DAC_OUT2=DAC_CHANNEL_2
DAC1.IPParameters=DAC_Channel-DAC_OUT1,DAC_Channel-DAC_OUT2
Dma.ADC1.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.1.EventEnable=DISABLE
Dma.ADC1.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.1.Instance=DMA1_Stream0
Dma.ADC1.1.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.1.MemInc=DMA_MINC_ENABLE
Dma.ADC1.1.Mode=DMA_CIRCULAR
Dma.ADC1.1.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.1.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC1.1.Priority=DMA_PRIORITY_LOW
Dma.ADC1.1.RequestNumber=1
Dma.ADC1.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC1.1.SignalID=NONE
Dma.ADC1.1.SyncEnable=DISABLE
Dma.ADC1.1.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC1.1.SyncRequestNumber=1
Dma.ADC1.1.SyncSignalID=NONE
Dma.ADC3.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC3.0.EventEnable=DISABLE
Dma.ADC3.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC3.0.Instance=DMA1_Stream1
Dma.ADC3.0.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC3.0.MemInc=DMA_MINC_ENABLE
Dma.ADC3.0.Mode=DMA_CIRCULAR
Dma.ADC3.0.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC3.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC3.0.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.ADC3.0.Priority=DMA_PRIORITY_LOW
Dma.ADC3.0.RequestNumber=1
Dma.ADC3.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.ADC3.0.SignalID=NONE
Dma.ADC3.0.SyncEnable=DISABLE
Dma.ADC3.0.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.ADC3.0.SyncRequestNumber=1
Dma.ADC3.0.SyncSignalID=NONE
Dma.Request0=ADC3
Dma.Request1=ADC1
Dma.Request2=UART4_TX
Dma.Request3=UART4_RX
Dma.Request4=UART7_TX
Dma.Request5=UART7_RX
Dma.Request6=USART1_TX
Dma.Request7=USART1_RX
Dma.Request8=USART2_TX
Dma.Request9=USART2_RX
Dma.RequestsNb=10
Dma.UART4_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART4_RX.3.EventEnable=DISABLE
Dma.UART4_RX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_RX.3.Instance=DMA1_Stream3
Dma.UART4_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_RX.3.MemInc=DMA_MINC_ENABLE
Dma.UART4_RX.3.Mode=DMA_CIRCULAR
Dma.UART4_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_RX.3.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UART4_RX.3.Priority=DMA_PRIORITY_LOW
Dma.UART4_RX.3.RequestNumber=1
Dma.UART4_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UART4_RX.3.SignalID=NONE
Dma.UART4_RX.3.SyncEnable=DISABLE
Dma.UART4_RX.3.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UART4_RX.3.SyncRequestNumber=1
Dma.UART4_RX.3.SyncSignalID=NONE
Dma.UART4_TX.2.Direction=DMA_MEMORY_TO_PERIPH
Dma.UART4_TX.2.EventEnable=DISABLE
Dma.UART4_TX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_TX.2.Instance=DMA1_Stream2
Dma.UART4_TX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_TX.2.MemInc=DMA_MINC_ENABLE
Dma.UART4_TX.2.Mode=DMA_NORMAL
Dma.UART4_TX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_TX.2.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_TX.2.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UART4_TX.2.Priority=DMA_PRIORITY_LOW
Dma.UART4_TX.2.RequestNumber=1
Dma.UART4_TX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UART4_TX.2.SignalID=NONE
Dma.UART4_TX.2.SyncEnable=DISABLE
Dma.UART4_TX.2.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UART4_TX.2.SyncRequestNumber=1
Dma.UART4_TX.2.SyncSignalID=NONE
Dma.UART7_RX.5.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART7_RX.5.EventEnable=DISABLE
Dma.UART7_RX.5.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART7_RX.5.Instance=DMA1_Stream7
Dma.UART7_RX.5.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART7_RX.5.MemInc=DMA_MINC_ENABLE
Dma.UART7_RX.5.Mode=DMA_CIRCULAR
Dma.UART7_RX.5.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART7_RX.5.PeriphInc=DMA_PINC_DISABLE
Dma.UART7_RX.5.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UART7_RX.5.Priority=DMA_PRIORITY_LOW
Dma.UART7_RX.5.RequestNumber=1
Dma.UART7_RX.5.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UART7_RX.5.SignalID=NONE
Dma.UART7_RX.5.SyncEnable=DISABLE
Dma.UART7_RX.5.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UART7_RX.5.SyncRequestNumber=1
Dma.UART7_RX.5.SyncSignalID=NONE
Dma.UART7_TX.4.Direction=DMA_MEMORY_TO_PERIPH
Dma.UART7_TX.4.EventEnable=DISABLE
Dma.UART7_TX.4.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART7_TX.4.Instance=DMA1_Stream6
Dma.UART7_TX.4.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART7_TX.4.MemInc=DMA_MINC_ENABLE
Dma.UART7_TX.4.Mode=DMA_NORMAL
Dma.UART7_TX.4.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART7_TX.4.PeriphInc=DMA_PINC_DISABLE
Dma.UART7_TX.4.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UART7_TX.4.Priority=DMA_PRIORITY_LOW
Dma.UART7_TX.4.RequestNumber=1
Dma.UART7_TX.4.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UART7_TX.4.SignalID=NONE
Dma.UART7_TX.4.SyncEnable=DISABLE
Dma.UART7_TX.4.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UART7_TX.4.SyncRequestNumber=1
Dma.UART7_TX.4.SyncSignalID=NONE
Dma.USART1_RX.7.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.7.EventEnable=DISABLE
Dma.USART1_RX.7.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.7.Instance=DMA2_Stream1
Dma.USART1_RX.7.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.7.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.7.Mode=DMA_CIRCULAR
Dma.USART1_RX.7.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.7.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.7.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.USART1_RX.7.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.7.RequestNumber=1
Dma.USART1_RX.7.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.USART1_RX.7.SignalID=NONE
Dma.USART1_RX.7.SyncEnable=DISABLE
Dma.USART1_RX.7.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.USART1_RX.7.SyncRequestNumber=1
Dma.USART1_RX.7.SyncSignalID=NONE
Dma.USART1_TX.6.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART1_TX.6.EventEnable=DISABLE
Dma.USART1_TX.6.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_TX.6.Instance=DMA2_Stream0
Dma.USART1_TX.6.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_TX.6.MemInc=DMA_MINC_ENABLE
Dma.USART1_TX.6.Mode=DMA_NORMAL
Dma.USART1_TX.6.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_TX.6.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_TX.6.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.USART1_TX.6.Priority=DMA_PRIORITY_LOW
Dma.USART1_TX.6.RequestNumber=1
Dma.USART1_TX.6.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.USART1_TX.6.SignalID=NONE
Dma.USART1_TX.6.SyncEnable=DISABLE
Dma.USART1_TX.6.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.USART1_TX.6.SyncRequestNumber=1
Dma.USART1_TX.6.SyncSignalID=NONE
Dma.USART2_RX.9.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.9.EventEnable=DISABLE
Dma.USART2_RX.9.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.9.Instance=DMA2_Stream3
Dma.USART2_RX.9.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.9.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.9.Mode=DMA_CIRCULAR
Dma.USART2_RX.9.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.9.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.9.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.USART2_RX.9.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.9.RequestNumber=1
Dma.USART2_RX.9.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.USART2_RX.9.SignalID=NONE
Dma.USART2_RX.9.SyncEnable=DISABLE
Dma.USART2_RX.9.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.USART2_RX.9.SyncRequestNumber=1
Dma.USART2_RX.9.SyncSignalID=NONE
Dma.USART2_TX.8.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART2_TX.8.EventEnable=DISABLE
Dma.USART2_TX.8.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_TX.8.Instance=DMA2_Stream2
Dma.USART2_TX.8.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_TX.8.MemInc=DMA_MINC_ENABLE
Dma.USART2_TX.8.Mode=DMA_NORMAL
Dma.USART2_TX.8.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_TX.8.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_TX.8.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.USART2_TX.8.Priority=DMA_PRIORITY_LOW
Dma.USART2_TX.8.RequestNumber=1
Dma.USART2_TX.8.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.USART2_TX.8.SignalID=NONE
Dma.USART2_TX.8.SyncEnable=DISABLE
Dma.USART2_TX.8.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.USART2_TX.8.SyncRequestNumber=1
Dma.USART2_TX.8.SyncSignalID=NONE
ETH.IPParameters=MediaInterface,TxDescAddress,RxBufferAddress
ETH.MediaInterface=HAL_ETH_RMII_MODE
ETH.RxBufferAddress=0x30000400
ETH.TxDescAddress=0x30000200
FDCAN1.CalculateBaudRateNominal=264705
FDCAN1.CalculateTimeBitNominal=3777
FDCAN1.CalculateTimeQuantumNominal=222.22222222222223
FDCAN1.DataSyncJumpWidth=1
FDCAN1.DataTimeSeg1=1
FDCAN1.DataTimeSeg2=1
FDCAN1.ExtFiltersNbr=0
FDCAN1.IPParameters=CalculateTimeQuantumNominal,CalculateTimeBitNominal,CalculateBaudRateNominal,NominalTimeSeg1,NominalPrescaler,NominalSyncJumpWidth,DataTimeSeg1,DataTimeSeg2,DataSyncJumpWidth,ExtFiltersNbr,RxFifo0ElmtsNbr,TxFifoQueueElmtsNbr,RxBuffersNbr,TxBuffersNbr,TxEventsNbr
FDCAN1.NominalPrescaler=20
FDCAN1.NominalSyncJumpWidth=1
FDCAN1.NominalTimeSeg1=15
FDCAN1.RxBuffersNbr=0
FDCAN1.RxFifo0ElmtsNbr=40
FDCAN1.TxBuffersNbr=0
FDCAN1.TxEventsNbr=0
FDCAN1.TxFifoQueueElmtsNbr=2
FMC.CASLatency1=FMC_SDRAM_CAS_LATENCY_3
FMC.ColumnBitsNumber1=FMC_SDRAM_COLUMN_BITS_NUM_9
FMC.ExitSelfRefreshDelay1=9
FMC.IPParameters=ColumnBitsNumber1,CASLatency1,SDClockPeriod1,ReadBurst1,LoadToActiveDelay1,ExitSelfRefreshDelay1,SelfRefreshTime1,RowCycleDelay1,WriteRecoveryTime1,RPDelay1,RCDDelay1
FMC.LoadToActiveDelay1=2
FMC.RCDDelay1=3
FMC.RPDelay1=3
FMC.ReadBurst1=FMC_SDRAM_RBURST_ENABLE
FMC.RowCycleDelay1=8
FMC.SDClockPeriod1=FMC_SDRAM_CLOCK_PERIOD_2
FMC.SelfRefreshTime1=10
FMC.WriteRecoveryTime1=8
FREERTOS.FootprintOK=true
FREERTOS.INCLUDE_eTaskGetState=0
FREERTOS.INCLUDE_pcTaskGetTaskName=0
FREERTOS.INCLUDE_uxTaskGetStackHighWaterMark=0
FREERTOS.INCLUDE_uxTaskGetStackHighWaterMark2=0
FREERTOS.INCLUDE_vTaskCleanUpResources=1
FREERTOS.INCLUDE_vTaskDelayUntil=1
FREERTOS.INCLUDE_xQueueGetMutexHolder=1
FREERTOS.INCLUDE_xSemaphoreGetMutexHolder=1
FREERTOS.INCLUDE_xTaskAbortDelay=0
FREERTOS.INCLUDE_xTaskGetCurrentTaskHandle=0
FREERTOS.INCLUDE_xTaskGetHandle=0
FREERTOS.IPParameters=Tasks01,configTOTAL_HEAP_SIZE,configUSE_NEWLIB_REENTRANT,FootprintOK,configENABLE_FPU,configUSE_RECURSIVE_MUTEXES,configUSE_COUNTING_SEMAPHORES,INCLUDE_xQueueGetMutexHolder,INCLUDE_xSemaphoreGetMutexHolder,configMAX_TASK_NAME_LEN,INCLUDE_vTaskCleanUpResources,INCLUDE_vTaskDelayUntil,INCLUDE_pcTaskGetTaskName,INCLUDE_uxTaskGetStackHighWaterMark,INCLUDE_xTaskGetCurrentTaskHandle,INCLUDE_eTaskGetState,INCLUDE_xTaskAbortDelay,INCLUDE_xTaskGetHandle,INCLUDE_uxTaskGetStackHighWaterMark2,configUSE_TIMERS,configUSE_CO_ROUTINES
FREERTOS.Tasks01=MainTask,0,2048,StartMainTask,As weak,NULL,Dynamic,NULL,NULL;SystemTask,0,2048,StartSystemTask,As external,NULL,Dynamic,NULL,NULL;EthernetTask,0,2048,StartEthernetTask,As external,NULL,Dynamic,NULL,NULL;CommTask,0,2048,StartCommTask,As external,NULL,Dynamic,NULL,NULL;RestTask,0,2048,StartRestTask,As external,NULL,Dynamic,NULL,NULL;NmeaTxTask,-2,2048,StartNmeaTxTask,As external,NULL,Dynamic,NULL,NULL
FREERTOS.configENABLE_FPU=1
FREERTOS.configMAX_TASK_NAME_LEN=30
FREERTOS.configTOTAL_HEAP_SIZE=163840
FREERTOS.configUSE_COUNTING_SEMAPHORES=1
FREERTOS.configUSE_CO_ROUTINES=1
FREERTOS.configUSE_NEWLIB_REENTRANT=1
FREERTOS.configUSE_RECURSIVE_MUTEXES=1
FREERTOS.configUSE_TIMERS=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
HRTIM.ADCTrigger1_Source1=HRTIM_ADCTRIGGEREVENT13_TIMERA_PERIOD
HRTIM.ADCTrigger3_Source1=HRTIM_ADCTRIGGEREVENT13_TIMERB_PERIOD
HRTIM.ADCTrigger_Id1=HRTIM_ADCTRIGGER_1
HRTIM.ADCTrigger_Id3=HRTIM_ADCTRIGGER_3
HRTIM.Configuration_TA=Simple
HRTIM.Configuration_TB=Simple
HRTIM.IPParameters=Periode_TA,ADCTrigger_Id1,NumberADCTriggerSource1,ADCTrigger1_Source1,PrescalerRatio_TB,Configuration_TB,Configuration_TA,Periode_TB,ADCTrigger_Id3,ORingUpdateSource_ADCTrigger3,NumberADCTriggerSource3,ADCTrigger3_Source1
HRTIM.NumberADCTriggerSource1=1
HRTIM.NumberADCTriggerSource3=1
HRTIM.ORingUpdateSource_ADCTrigger3=HRTIM_ADCTRIGGERUPDATE_TIMER_B
HRTIM.Periode_TA=0x4e20
HRTIM.Periode_TB=60000
HRTIM.PrescalerRatio_TB=HRTIM_PRESCALERRATIO_DIV4
I2C2.IPParameters=Timing
I2C2.Timing=0x307075B1
IWDG1.IPParameters=Prescaler,Window,Reload
IWDG1.Prescaler=IWDG_PRESCALER_256
IWDG1.Reload=1250
IWDG1.Window=1250
KeepUserPlacement=false
LWIP.BSP.number=1
LWIP.GATEWAY_ADDRESS=***************
LWIP.IPParameters=MEM_SIZE,LWIP_ICMP,LWIP_DNS,LWIP_MULTICAST_TX_OPTIONS,LWIP_IGMP,MEMP_NUM_UDP_PCB,MEMP_NUM_TCP_PCB,SO_REUSE,LWIP_RAM_HEAP_POINTER,PBUF_POOL_BUFSIZE,MEMP_NUM_RAW_PCB,MEMP_NUM_NETBUF,MEMP_NUM_NETCONN,MEMP_NUM_SELECT_CB,MEMP_NUM_LOCALHOSTLIST,MEMP_NUM_NETDB,MEMP_MEM_INIT,MEMP_MEM_MALLOC,MEM_LIBC_MALLOC,MEMP_NUM_PBUF,MEMP_NUM_TCP_PCB_LISTEN,MEMP_NUM_SYS_TIMEOUT,LWIP_TCPIP_TIMEOUT,LWIP_SO_SNDTIMEO,LWIP_SO_RCVTIMEO,LWIP_SO_SNDRCVTIMEO_NONSTANDARD,LWIP_SOCKET_OFFSET,LWIP_DHCP,IP_ADDRESS,NETMASK_ADDRESS,GATEWAY_ADDRESS,LWIP_TIMERS,TCPIP_MBOX_SIZE,TCPIP_THREAD_STACKSIZE,TCPIP_THREAD_PRIO,LWIP_DBG_MIN_LEVEL,PBUF_POOL_SIZE
LWIP.IP_ADDRESS=***************
LWIP.LWIP_DBG_MIN_LEVEL=LWIP_DBG_MASK_LEVEL
LWIP.LWIP_DHCP=1
LWIP.LWIP_DNS=1
LWIP.LWIP_ICMP=1
LWIP.LWIP_IGMP=1
LWIP.LWIP_MULTICAST_TX_OPTIONS=1
LWIP.LWIP_RAM_HEAP_POINTER=0x30040000
LWIP.LWIP_SOCKET_OFFSET=1
LWIP.LWIP_SO_RCVTIMEO=1
LWIP.LWIP_SO_SNDRCVTIMEO_NONSTANDARD=1
LWIP.LWIP_SO_SNDTIMEO=1
LWIP.LWIP_TCPIP_TIMEOUT=1
LWIP.LWIP_TIMERS=1
LWIP.MEMP_MEM_INIT=0
LWIP.MEMP_MEM_MALLOC=0
LWIP.MEMP_NUM_LOCALHOSTLIST=8
LWIP.MEMP_NUM_NETBUF=8
LWIP.MEMP_NUM_NETCONN=8
LWIP.MEMP_NUM_NETDB=8
LWIP.MEMP_NUM_PBUF=20
LWIP.MEMP_NUM_RAW_PCB=4
LWIP.MEMP_NUM_SELECT_CB=8
LWIP.MEMP_NUM_SYS_TIMEOUT=128
LWIP.MEMP_NUM_TCP_PCB=10
LWIP.MEMP_NUM_TCP_PCB_LISTEN=4
LWIP.MEMP_NUM_UDP_PCB=10
LWIP.MEM_LIBC_MALLOC=0
LWIP.MEM_SIZE=31744
LWIP.NETMASK_ADDRESS=***************
LWIP.PBUF_POOL_BUFSIZE=1024
LWIP.PBUF_POOL_SIZE=16
LWIP.SO_REUSE=1
LWIP.TCPIP_MBOX_SIZE=6
LWIP.TCPIP_THREAD_PRIO=3
LWIP.TCPIP_THREAD_STACKSIZE=1024
LWIP.Version=v2.1.2_Cube
LWIP0.BSP.STBoard=false
LWIP0.BSP.api=BSP_COMPONENT_DRIVER
LWIP0.BSP.component=LAN8742
LWIP0.BSP.condition=
LWIP0.BSP.instance=LAN8742
LWIP0.BSP.ip=
LWIP0.BSP.mode=
LWIP0.BSP.name=Driver_PHY
LWIP0.BSP.semaphore=S_LAN8742
LWIP0.BSP.solution=LAN8742
MMTAppRegionsCount=0
MMTConfigApplied=false
Mcu.CPN=STM32H743BIT6
Mcu.Family=STM32H7
Mcu.IP0=ADC1
Mcu.IP1=ADC3
Mcu.IP10=HRTIM
Mcu.IP11=I2C2
Mcu.IP12=IWDG1
Mcu.IP13=LWIP
Mcu.IP14=MEMORYMAP
Mcu.IP15=NVIC
Mcu.IP16=QUADSPI
Mcu.IP17=RCC
Mcu.IP18=RTC
Mcu.IP19=SYS
Mcu.IP2=CORTEX_M7
Mcu.IP20=TIM3
Mcu.IP21=UART4
Mcu.IP22=UART7
Mcu.IP23=USART1
Mcu.IP24=USART2
Mcu.IP3=CRC
Mcu.IP4=DAC1
Mcu.IP5=DMA
Mcu.IP6=ETH
Mcu.IP7=FDCAN1
Mcu.IP8=FMC
Mcu.IP9=FREERTOS
Mcu.IPNb=25
Mcu.Name=STM32H743BITx
Mcu.Package=LQFP208
Mcu.Pin0=PE2
Mcu.Pin1=PC13
Mcu.Pin10=PF6
Mcu.Pin100=VP_CRC_VS_CRC
Mcu.Pin101=VP_FREERTOS_VS_CMSIS_V1
Mcu.Pin102=VP_HRTIM_VS_hrtimTimerANoOutput
Mcu.Pin103=VP_HRTIM_VS_hrtimTimerBNoOutput
Mcu.Pin104=VP_IWDG1_VS_IWDG
Mcu.Pin105=VP_LWIP_VS_Enabled
Mcu.Pin106=VP_RTC_VS_RTC_Activate
Mcu.Pin107=VP_RTC_VS_RTC_Calendar
Mcu.Pin108=VP_SYS_VS_tim1
Mcu.Pin109=VP_TIM3_VS_ClockSourceINT
Mcu.Pin11=PF7
Mcu.Pin110=VP_MEMORYMAP_VS_MEMORYMAP
Mcu.Pin12=PF10
Mcu.Pin13=PH0-OSC_IN (PH0)
Mcu.Pin14=PH1-OSC_OUT (PH1)
Mcu.Pin15=PC1
Mcu.Pin16=PC2_C
Mcu.Pin17=PC3_C
Mcu.Pin18=PA0
Mcu.Pin19=PA1
Mcu.Pin2=PC14-OSC32_IN (OSC32_IN)
Mcu.Pin20=PA2
Mcu.Pin21=PH2
Mcu.Pin22=PH5
Mcu.Pin23=PA4
Mcu.Pin24=PA5
Mcu.Pin25=PA6
Mcu.Pin26=PA7
Mcu.Pin27=PC4
Mcu.Pin28=PC5
Mcu.Pin29=PB0
Mcu.Pin3=PC15-OSC32_OUT (OSC32_OUT)
Mcu.Pin30=PB1
Mcu.Pin31=PB2
Mcu.Pin32=PF11
Mcu.Pin33=PF12
Mcu.Pin34=PF13
Mcu.Pin35=PF14
Mcu.Pin36=PF15
Mcu.Pin37=PG0
Mcu.Pin38=PG1
Mcu.Pin39=PE7
Mcu.Pin4=PF0
Mcu.Pin40=PE8
Mcu.Pin41=PE9
Mcu.Pin42=PE10
Mcu.Pin43=PE11
Mcu.Pin44=PE12
Mcu.Pin45=PE13
Mcu.Pin46=PE14
Mcu.Pin47=PE15
Mcu.Pin48=PB10
Mcu.Pin49=PB11
Mcu.Pin5=PF1
Mcu.Pin50=PH10
Mcu.Pin51=PH12
Mcu.Pin52=PB14
Mcu.Pin53=PB15
Mcu.Pin54=PD8
Mcu.Pin55=PD9
Mcu.Pin56=PD10
Mcu.Pin57=PD11
Mcu.Pin58=PD12
Mcu.Pin59=PD13
Mcu.Pin6=PF2
Mcu.Pin60=PD14
Mcu.Pin61=PD15
Mcu.Pin62=PG2
Mcu.Pin63=PG4
Mcu.Pin64=PG5
Mcu.Pin65=PG6
Mcu.Pin66=PG7
Mcu.Pin67=PG8
Mcu.Pin68=PC8
Mcu.Pin69=PA10
Mcu.Pin7=PF3
Mcu.Pin70=PA11
Mcu.Pin71=PA12
Mcu.Pin72=PH13
Mcu.Pin73=PH14
Mcu.Pin74=PI0
Mcu.Pin75=PI1
Mcu.Pin76=PI2
Mcu.Pin77=PA15 (JTDI)
Mcu.Pin78=PD0
Mcu.Pin79=PD1
Mcu.Pin8=PF4
Mcu.Pin80=PD2
Mcu.Pin81=PD3
Mcu.Pin82=PD4
Mcu.Pin83=PD5
Mcu.Pin84=PD6
Mcu.Pin85=PD7
Mcu.Pin86=PJ12
Mcu.Pin87=PJ13
Mcu.Pin88=PJ14
Mcu.Pin89=PJ15
Mcu.Pin9=PF5
Mcu.Pin90=PG9
Mcu.Pin91=PG10
Mcu.Pin92=PG11
Mcu.Pin93=PG12
Mcu.Pin94=PG13
Mcu.Pin95=PK3
Mcu.Pin96=PG15
Mcu.Pin97=PE0
Mcu.Pin98=PE1
Mcu.Pin99=VP_ADC3_Vref_Input
Mcu.PinsNb=111
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H743BITx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.ADC3_IRQn=true\:7\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.ADC_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DMA1_Stream0_IRQn=true\:6\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream1_IRQn=true\:6\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream2_IRQn=true\:6\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream3_IRQn=true\:6\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream6_IRQn=true\:6\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream7_IRQn=true\:6\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream0_IRQn=true\:6\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream1_IRQn=true\:6\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:6\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream3_IRQn=true\:6\:0\:true\:false\:true\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.ETH_IRQn=true\:15\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.FDCAN1_IT0_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM1_UP_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TIM3_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.TimeBase=TIM1_UP_IRQn
NVIC.TimeBaseIP=TIM1
NVIC.UART4_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.UART7_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:6\:0\:true\:false\:true\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PA0.GPIOParameters=GPIO_Label
PA0.GPIO_Label=DO_FSK_CHECK_INT
PA0.Locked=true
PA0.Signal=GPIO_Output
PA1.GPIOParameters=GPIO_Speed
PA1.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA1.Mode=RMII
PA1.Signal=ETH_REF_CLK
PA10.GPIOParameters=GPIO_Label
PA10.GPIO_Label=DO_RF_TEST_ENABLE
PA10.Locked=true
PA10.Signal=GPIO_Output
PA11.GPIOParameters=GPIO_Speed,PinAttribute
PA11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA11.Locked=true
PA11.Mode=Asynchronous
PA11.PinAttribute=Free
PA11.Signal=UART4_RX
PA12.GPIOParameters=GPIO_Speed,PinAttribute
PA12.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA12.Locked=true
PA12.Mode=Asynchronous
PA12.PinAttribute=Free
PA12.Signal=UART4_TX
PA15\ (JTDI).GPIOParameters=GPIO_Label
PA15\ (JTDI).GPIO_Label=TP_PA15
PA15\ (JTDI).Locked=true
PA15\ (JTDI).Signal=GPIO_Output
PA2.GPIOParameters=GPIO_Speed
PA2.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA2.Mode=RMII
PA2.Signal=ETH_MDIO
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=AO_FSK_OUT
PA4.Locked=true
PA4.Signal=COMP_DAC11_group
PA5.GPIOParameters=GPIO_Label
PA5.GPIO_Label=AO_FREQ_INT
PA5.Locked=true
PA5.Signal=COMP_DAC12_group
PA6.GPIOParameters=GPIO_Label
PA6.GPIO_Label=AI_ANT_POWER
PA6.Signal=ADCx_INP3
PA7.GPIOParameters=GPIO_Speed
PA7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA7.Mode=RMII
PA7.Signal=ETH_CRS_DV
PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=AI_FSK_LOC
PB0.Signal=ADCx_INP9
PB1.GPIOParameters=GPIO_Label
PB1.GPIO_Label=AI_FSK_INT
PB1.Signal=ADCx_INP5
PB10.Locked=true
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Locked=true
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB14.GPIOParameters=GPIO_Speed
PB14.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB14.Locked=true
PB14.Mode=Asynchronous
PB14.Signal=USART1_TX
PB15.GPIOParameters=GPIO_Speed
PB15.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB15.Locked=true
PB15.Mode=Asynchronous
PB15.Signal=USART1_RX
PB2.GPIOParameters=GPIO_Speed
PB2.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB2.Locked=true
PB2.Mode=Single Bank 1
PB2.Signal=QUADSPI_CLK
PC1.GPIOParameters=GPIO_Speed,PinAttribute
PC1.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC1.Mode=RMII
PC1.PinAttribute=Free
PC1.Signal=ETH_MDC
PC13.GPIOParameters=GPIO_Label,PinAttribute
PC13.GPIO_Label=DO_ETH_RESET
PC13.Locked=true
PC13.PinAttribute=Free
PC13.Signal=GPIO_Output
PC14-OSC32_IN\ (OSC32_IN).GPIOParameters=PinAttribute
PC14-OSC32_IN\ (OSC32_IN).Mode=LSE-External-Oscillator
PC14-OSC32_IN\ (OSC32_IN).PinAttribute=Free
PC14-OSC32_IN\ (OSC32_IN).Signal=RCC_OSC32_IN
PC15-OSC32_OUT\ (OSC32_OUT).GPIOParameters=PinAttribute
PC15-OSC32_OUT\ (OSC32_OUT).Mode=LSE-External-Oscillator
PC15-OSC32_OUT\ (OSC32_OUT).PinAttribute=Free
PC15-OSC32_OUT\ (OSC32_OUT).Signal=RCC_OSC32_OUT
PC2_C.Locked=true
PC2_C.Mode=SdramChipSelect1_1
PC2_C.Signal=FMC_SDNE0
PC3_C.GPIOParameters=GPIO_Label
PC3_C.GPIO_Label=AI_PCB_VER_1
PC3_C.Mode=IN1-Single-Ended
PC3_C.Signal=ADC3_INP1
PC4.GPIOParameters=GPIO_Speed
PC4.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC4.Mode=RMII
PC4.Signal=ETH_RXD0
PC5.GPIOParameters=GPIO_Speed
PC5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC5.Mode=RMII
PC5.Signal=ETH_RXD1
PC8.GPIOParameters=GPIO_Label
PC8.GPIO_Label=TP_PC8
PC8.Locked=true
PC8.Signal=GPIO_Output
PD0.Signal=FMC_D2_DA2
PD1.Signal=FMC_D3_DA3
PD10.Signal=FMC_D15_DA15
PD11.GPIOParameters=GPIO_Speed
PD11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD11.Locked=true
PD11.Mode=Single Bank 1
PD11.Signal=QUADSPI_BK1_IO0
PD12.GPIOParameters=GPIO_Speed
PD12.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD12.Locked=true
PD12.Mode=Single Bank 1
PD12.Signal=QUADSPI_BK1_IO1
PD13.GPIOParameters=GPIO_Speed
PD13.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD13.Mode=Single Bank 1
PD13.Signal=QUADSPI_BK1_IO3
PD14.Signal=FMC_D0_DA0
PD15.Signal=FMC_D1_DA1
PD2.GPIOParameters=GPIO_Label
PD2.GPIO_Label=TP_PD2
PD2.Locked=true
PD2.Signal=GPIO_Output
PD3.GPIOParameters=GPIO_Label
PD3.GPIO_Label=TP_PD3
PD3.Locked=true
PD3.Signal=GPIO_Output
PD4.GPIOParameters=GPIO_Label
PD4.GPIO_Label=TP_LED_1
PD4.Locked=true
PD4.Signal=GPIO_Output
PD5.GPIOParameters=GPIO_Speed
PD5.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD5.Mode=Asynchronous
PD5.Signal=USART2_TX
PD6.GPIOParameters=GPIO_Speed
PD6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD6.Locked=true
PD6.Mode=Asynchronous
PD6.Signal=USART2_RX
PD7.GPIOParameters=GPIO_Label
PD7.GPIO_Label=TP_LED_2
PD7.Locked=true
PD7.Signal=GPIO_Output
PD8.Signal=FMC_D13_DA13
PD9.Signal=FMC_D14_DA14
PE0.GPIOParameters=PinAttribute
PE0.PinAttribute=Free
PE0.Signal=FMC_NBL0
PE1.GPIOParameters=PinAttribute
PE1.PinAttribute=Free
PE1.Signal=FMC_NBL1
PE10.Signal=FMC_D7_DA7
PE11.Signal=FMC_D8_DA8
PE12.Signal=FMC_D9_DA9
PE13.Signal=FMC_D10_DA10
PE14.Signal=FMC_D11_DA11
PE15.Signal=FMC_D12_DA12
PE2.GPIOParameters=GPIO_Speed,PinAttribute
PE2.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PE2.Mode=Single Bank 1
PE2.PinAttribute=Free
PE2.Signal=QUADSPI_BK1_IO2
PE7.Signal=FMC_D4_DA4
PE8.Signal=FMC_D5_DA5
PE9.Signal=FMC_D6_DA6
PF0.GPIOParameters=PinAttribute
PF0.PinAttribute=Free
PF0.Signal=FMC_A0
PF1.GPIOParameters=PinAttribute
PF1.PinAttribute=Free
PF1.Signal=FMC_A1
PF10.GPIOParameters=GPIO_Label
PF10.GPIO_Label=AI_PCB_VER_2
PF10.Mode=IN6-Single-Ended
PF10.Signal=ADC3_INP6
PF11.Signal=FMC_SDNRAS
PF12.Signal=FMC_A6
PF13.Signal=FMC_A7
PF14.Signal=FMC_A8
PF15.Signal=FMC_A9
PF2.GPIOParameters=PinAttribute
PF2.PinAttribute=Free
PF2.Signal=FMC_A2
PF3.GPIOParameters=PinAttribute
PF3.PinAttribute=Free
PF3.Signal=FMC_A3
PF4.GPIOParameters=PinAttribute
PF4.PinAttribute=Free
PF4.Signal=FMC_A4
PF5.GPIOParameters=PinAttribute
PF5.PinAttribute=Free
PF5.Signal=FMC_A5
PF6.GPIOParameters=GPIO_Speed,PinAttribute
PF6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PF6.Mode=Asynchronous
PF6.PinAttribute=Free
PF6.Signal=UART7_RX
PF7.GPIOParameters=GPIO_Speed,PinAttribute
PF7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PF7.Mode=Asynchronous
PF7.PinAttribute=Free
PF7.Signal=UART7_TX
PG0.Signal=FMC_A10
PG1.Signal=FMC_A11
PG10.GPIOParameters=GPIO_Label
PG10.GPIO_Label=DO_4MOSC_SEL
PG10.Locked=true
PG10.Signal=GPIO_Output
PG11.GPIOParameters=GPIO_Speed
PG11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PG11.Locked=true
PG11.Mode=RMII
PG11.Signal=ETH_TX_EN
PG12.GPIOParameters=GPIO_Speed
PG12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PG12.Locked=true
PG12.Mode=RMII
PG12.Signal=ETH_TXD1
PG13.GPIOParameters=GPIO_Speed,PinAttribute
PG13.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PG13.Locked=true
PG13.Mode=RMII
PG13.PinAttribute=Free
PG13.Signal=ETH_TXD0
PG15.GPIOParameters=PinAttribute
PG15.PinAttribute=Free
PG15.Signal=FMC_SDNCAS
PG2.Signal=FMC_A12
PG4.Signal=FMC_A14_BA0
PG5.Signal=FMC_A15_BA1
PG6.GPIOParameters=GPIO_Speed
PG6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG6.Locked=true
PG6.Mode=Single Bank 1
PG6.Signal=QUADSPI_BK1_NCS
PG7.GPIOParameters=GPIO_Label
PG7.GPIO_Label=TP_PG7
PG7.Locked=true
PG7.Signal=GPIO_Output
PG8.Signal=FMC_SDCLK
PG9.GPIOParameters=GPIO_Label
PG9.GPIO_Label=TP_LED_7
PG9.Locked=true
PG9.Signal=GPIO_Output
PH0-OSC_IN\ (PH0).GPIOParameters=PinAttribute
PH0-OSC_IN\ (PH0).Mode=HSE-External-Oscillator
PH0-OSC_IN\ (PH0).PinAttribute=Free
PH0-OSC_IN\ (PH0).Signal=RCC_OSC_IN
PH1-OSC_OUT\ (PH1).GPIOParameters=PinAttribute
PH1-OSC_OUT\ (PH1).Mode=HSE-External-Oscillator
PH1-OSC_OUT\ (PH1).PinAttribute=Free
PH1-OSC_OUT\ (PH1).Signal=RCC_OSC_OUT
PH10.GPIOParameters=GPIO_Label
PH10.GPIO_Label=DO_ANT_POW_ON
PH10.Locked=true
PH10.Signal=GPIO_Output
PH12.GPIOParameters=GPIO_Label
PH12.GPIO_Label=DI_ETH_nINT
PH12.Locked=true
PH12.Signal=GPIO_Input
PH13.Locked=true
PH13.Mode=FDCAN_Activate
PH13.Signal=FDCAN1_TX
PH14.Locked=true
PH14.Mode=FDCAN_Activate
PH14.Signal=FDCAN1_RX
PH2.Locked=true
PH2.Mode=SdramChipSelect1_1
PH2.Signal=FMC_SDCKE0
PH5.Locked=true
PH5.Signal=FMC_SDNWE
PI0.GPIOParameters=GPIO_Label
PI0.GPIO_Label=DO_FSK_CHECK_LOC
PI0.Locked=true
PI0.Signal=GPIO_Output
PI1.GPIOParameters=GPIO_Label
PI1.GPIO_Label=DO_Selector_4M
PI1.Locked=true
PI1.Signal=GPIO_Output
PI2.GPIOParameters=GPIO_Label
PI2.GPIO_Label=DO_Alarm_relay
PI2.Locked=true
PI2.Signal=GPIO_Output
PJ12.GPIOParameters=GPIO_Label
PJ12.GPIO_Label=TP_LED_3
PJ12.Locked=true
PJ12.Signal=GPIO_Output
PJ13.GPIOParameters=GPIO_Label
PJ13.GPIO_Label=TP_LED_4
PJ13.Locked=true
PJ13.Signal=GPIO_Output
PJ14.GPIOParameters=GPIO_Label
PJ14.GPIO_Label=TP_LED_5
PJ14.Locked=true
PJ14.Signal=GPIO_Output
PJ15.GPIOParameters=GPIO_Label
PJ15.GPIO_Label=TP_LED_6
PJ15.Locked=true
PJ15.Signal=GPIO_Output
PK3.GPIOParameters=GPIO_Label
PK3.GPIO_Label=TP_LED_8
PK3.Locked=true
PK3.Signal=GPIO_Output
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H743BITx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.12.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x400
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Receiver_Navtex.ioc
ProjectManager.ProjectName=Receiver_Navtex
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_FDCAN1_Init-FDCAN1-false-HAL-true,5-MX_FMC_Init-FMC-false-HAL-true,6-MX_I2C2_Init-I2C2-false-HAL-true,7-MX_QUADSPI_Init-QUADSPI-false-HAL-true,8-MX_RTC_Init-RTC-false-HAL-true,9-MX_DAC1_Init-DAC1-false-HAL-true,10-MX_UART4_Init-UART4-false-HAL-true,11-MX_TIM3_Init-TIM3-false-HAL-true,12-MX_ADC1_Init-ADC1-false-HAL-true,13-MX_UART7_Init-UART7-false-HAL-true,14-MX_ADC3_Init-ADC3-false-HAL-true,15-MX_USART2_UART_Init-USART2-false-HAL-true,16-MX_LWIP_Init-LWIP-false-HAL-false,17-MX_IWDG1_Init-IWDG1-false-HAL-true,18-MX_HRTIM_Init-HRTIM-false-HAL-true,19-MX_USART1_UART_Init-USART1-false-HAL-true,20-MX_CRC_Init-CRC-false-HAL-true,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true
QUADSPI.ChipSelectHighTime=QSPI_CS_HIGH_TIME_6_CYCLE
QUADSPI.ClockPrescaler=1
QUADSPI.FifoThreshold=4
QUADSPI.FlashSize=POSITION_VAL(MT25QL128A_FLASH_SIZE) - 1
QUADSPI.IPParameters=ClockPrescaler,FifoThreshold,SampleShifting,ChipSelectHighTime,FlashSize
QUADSPI.IPParametersWithoutCheck=FlashSize
QUADSPI.SampleShifting=QSPI_SAMPLE_SHIFTING_HALFCYCLE
RCC.ADCFreq_Value=100000000
RCC.AHB12Freq_Value=*********
RCC.AHB4Freq_Value=*********
RCC.APB1Freq_Value=*********
RCC.APB2Freq_Value=*********
RCC.APB3Freq_Value=*********
RCC.APB4Freq_Value=*********
RCC.AXIClockFreq_Value=*********
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CortexFreq_Value=*********
RCC.CpuClockFreq_Value=*********
RCC.D1CPREFreq_Value=*********
RCC.D1PPRE=RCC_APB3_DIV2
RCC.D2PPRE1=RCC_APB1_DIV2
RCC.D2PPRE2=RCC_APB2_DIV2
RCC.D3PPRE=RCC_APB4_DIV2
RCC.DFSDMACLkFreq_Value=*********
RCC.DFSDMFreq_Value=*********
RCC.DIVM1=2
RCC.DIVM2=2
RCC.DIVM3=16
RCC.DIVN1=80
RCC.DIVN2=75
RCC.DIVN3=100
RCC.DIVP1Freq_Value=*********
RCC.DIVP2=9
RCC.DIVP2Freq_Value=100000000
RCC.DIVP3Freq_Value=75000000
RCC.DIVQ1Freq_Value=*********
RCC.DIVQ2=10
RCC.DIVQ2Freq_Value=90000000
RCC.DIVQ3Freq_Value=75000000
RCC.DIVR1Freq_Value=*********
RCC.DIVR2Freq_Value=450000000
RCC.DIVR3Freq_Value=75000000
RCC.FDCANCLockSelection=RCC_FDCANCLKSOURCE_PLL2
RCC.FDCANFreq_Value=90000000
RCC.FMCFreq_Value=*********
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=*********
RCC.HCLKFreq_Value=*********
RCC.HPRE=RCC_HCLK_DIV2
RCC.HRTIMCLockSelection=RCC_HRTIM1CLK_CPUCLK
RCC.HRTIMFreq_Value=*********
RCC.HSE_VALUE=24000000
RCC.I2C123Freq_Value=*********
RCC.I2C4Freq_Value=*********
RCC.IPParameters=ADCFreq_Value,AHB12Freq_Value,AHB4Freq_Value,APB1Freq_Value,APB2Freq_Value,APB3Freq_Value,APB4Freq_Value,AXIClockFreq_Value,CECFreq_Value,CKPERFreq_Value,CortexFreq_Value,CpuClockFreq_Value,D1CPREFreq_Value,D1PPRE,D2PPRE1,D2PPRE2,D3PPRE,DFSDMACLkFreq_Value,DFSDMFreq_Value,DIVM1,DIVM2,DIVM3,DIVN1,DIVN2,DIVN3,DIVP1Freq_Value,DIVP2,DIVP2Freq_Value,DIVP3Freq_Value,DIVQ1Freq_Value,DIVQ2,DIVQ2Freq_Value,DIVQ3Freq_Value,DIVR1Freq_Value,DIVR2Freq_Value,DIVR3Freq_Value,FDCANCLockSelection,FDCANFreq_Value,FMCFreq_Value,FamilyName,HCLK3ClockFreq_Value,HCLKFreq_Value,HPRE,HRTIMCLockSelection,HRTIMFreq_Value,HSE_VALUE,I2C123Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM345Freq_Value,LPUART1Freq_Value,LSE_Drive_Capability,LTDCFreq_Value,MCO1PinFreq_Value,MCO2PinFreq_Value,PLL1_VCI_Range-AdvancedSettings,PLL2FRACN,PLL2_VCI_Range-AdvancedSettings,PLL2_VCO_SEL-AdvancedSettings,PLL3_VCO_SEL-AdvancedSettings,PLLSourceVirtual,QSPIFreq_Value,RNGFreq_Value,RTCClockSelection,RTCFreq_Value,SAI1Freq_Value,SAI23Freq_Value,SAI4AFreq_Value,SAI4BFreq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SPI123Freq_Value,SPI45Freq_Value,SPI6Freq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,Tim1OutputFreq_Value,Tim2OutputFreq_Value,TraceFreq_Value,USART16Freq_Value,USART234578Freq_Value,USBFreq_Value,VCO1OutputFreq_Value,VCO2OutputFreq_Value,VCO3OutputFreq_Value,VCOInput1Freq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value,WatchDogFreq_Value
RCC.LPTIM1Freq_Value=*********
RCC.LPTIM2Freq_Value=*********
RCC.LPTIM345Freq_Value=*********
RCC.LPUART1Freq_Value=*********
RCC.LSE_Drive_Capability=RCC_LSEDRIVE_HIGH
RCC.LTDCFreq_Value=75000000
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=*********
RCC.PLL1_VCI_Range-AdvancedSettings=RCC_PLL1VCIRANGE_0
RCC.PLL2FRACN=0
RCC.PLL2_VCI_Range-AdvancedSettings=RCC_PLL2VCIRANGE_0
RCC.PLL2_VCO_SEL-AdvancedSettings=RCC_PLL2VCOWIDE
RCC.PLL3_VCO_SEL-AdvancedSettings=RCC_PLL3VCOWIDE
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.QSPIFreq_Value=*********
RCC.RNGFreq_Value=48000000
RCC.RTCClockSelection=RCC_RTCCLKSOURCE_LSE
RCC.RTCFreq_Value=32768
RCC.SAI1Freq_Value=*********
RCC.SAI23Freq_Value=*********
RCC.SAI4AFreq_Value=*********
RCC.SAI4BFreq_Value=*********
RCC.SDMMCFreq_Value=*********
RCC.SPDIFRXFreq_Value=*********
RCC.SPI123Freq_Value=*********
RCC.SPI45Freq_Value=*********
RCC.SPI6Freq_Value=*********
RCC.SWPMI1Freq_Value=*********
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Tim1OutputFreq_Value=*********
RCC.Tim2OutputFreq_Value=*********
RCC.TraceFreq_Value=64000000
RCC.USART16Freq_Value=*********
RCC.USART234578Freq_Value=*********
RCC.USBFreq_Value=*********
RCC.VCO1OutputFreq_Value=*********
RCC.VCO2OutputFreq_Value=*********
RCC.VCO3OutputFreq_Value=*********
RCC.VCOInput1Freq_Value=12000000
RCC.VCOInput2Freq_Value=12000000
RCC.VCOInput3Freq_Value=1500000
RCC.WatchDogFreq_Value=32000
SH.ADCx_INP3.0=ADC1_INP3,IN3-Single-Ended
SH.ADCx_INP3.ConfNb=1
SH.ADCx_INP5.0=ADC1_INP5,IN5-Single-Ended
SH.ADCx_INP5.ConfNb=1
SH.ADCx_INP9.0=ADC1_INP9,IN9-Single-Ended
SH.ADCx_INP9.ConfNb=1
SH.COMP_DAC11_group.0=DAC1_OUT1,DAC_OUT1
SH.COMP_DAC11_group.ConfNb=1
SH.COMP_DAC12_group.0=DAC1_OUT2,DAC_OUT2
SH.COMP_DAC12_group.ConfNb=1
SH.FMC_A0.0=FMC_A0,13b-sda1
SH.FMC_A0.ConfNb=1
SH.FMC_A1.0=FMC_A1,13b-sda1
SH.FMC_A1.ConfNb=1
SH.FMC_A10.0=FMC_A10,13b-sda1
SH.FMC_A10.ConfNb=1
SH.FMC_A11.0=FMC_A11,13b-sda1
SH.FMC_A11.ConfNb=1
SH.FMC_A12.0=FMC_A12,13b-sda1
SH.FMC_A12.ConfNb=1
SH.FMC_A14_BA0.0=FMC_BA0,FourSdramBanks1
SH.FMC_A14_BA0.ConfNb=1
SH.FMC_A15_BA1.0=FMC_BA1,FourSdramBanks1
SH.FMC_A15_BA1.ConfNb=1
SH.FMC_A2.0=FMC_A2,13b-sda1
SH.FMC_A2.ConfNb=1
SH.FMC_A3.0=FMC_A3,13b-sda1
SH.FMC_A3.ConfNb=1
SH.FMC_A4.0=FMC_A4,13b-sda1
SH.FMC_A4.ConfNb=1
SH.FMC_A5.0=FMC_A5,13b-sda1
SH.FMC_A5.ConfNb=1
SH.FMC_A6.0=FMC_A6,13b-sda1
SH.FMC_A6.ConfNb=1
SH.FMC_A7.0=FMC_A7,13b-sda1
SH.FMC_A7.ConfNb=1
SH.FMC_A8.0=FMC_A8,13b-sda1
SH.FMC_A8.ConfNb=1
SH.FMC_A9.0=FMC_A9,13b-sda1
SH.FMC_A9.ConfNb=1
SH.FMC_D0_DA0.0=FMC_D0,sd-16b-d1
SH.FMC_D0_DA0.ConfNb=1
SH.FMC_D10_DA10.0=FMC_D10,sd-16b-d1
SH.FMC_D10_DA10.ConfNb=1
SH.FMC_D11_DA11.0=FMC_D11,sd-16b-d1
SH.FMC_D11_DA11.ConfNb=1
SH.FMC_D12_DA12.0=FMC_D12,sd-16b-d1
SH.FMC_D12_DA12.ConfNb=1
SH.FMC_D13_DA13.0=FMC_D13,sd-16b-d1
SH.FMC_D13_DA13.ConfNb=1
SH.FMC_D14_DA14.0=FMC_D14,sd-16b-d1
SH.FMC_D14_DA14.ConfNb=1
SH.FMC_D15_DA15.0=FMC_D15,sd-16b-d1
SH.FMC_D15_DA15.ConfNb=1
SH.FMC_D1_DA1.0=FMC_D1,sd-16b-d1
SH.FMC_D1_DA1.ConfNb=1
SH.FMC_D2_DA2.0=FMC_D2,sd-16b-d1
SH.FMC_D2_DA2.ConfNb=1
SH.FMC_D3_DA3.0=FMC_D3,sd-16b-d1
SH.FMC_D3_DA3.ConfNb=1
SH.FMC_D4_DA4.0=FMC_D4,sd-16b-d1
SH.FMC_D4_DA4.ConfNb=1
SH.FMC_D5_DA5.0=FMC_D5,sd-16b-d1
SH.FMC_D5_DA5.ConfNb=1
SH.FMC_D6_DA6.0=FMC_D6,sd-16b-d1
SH.FMC_D6_DA6.ConfNb=1
SH.FMC_D7_DA7.0=FMC_D7,sd-16b-d1
SH.FMC_D7_DA7.ConfNb=1
SH.FMC_D8_DA8.0=FMC_D8,sd-16b-d1
SH.FMC_D8_DA8.ConfNb=1
SH.FMC_D9_DA9.0=FMC_D9,sd-16b-d1
SH.FMC_D9_DA9.ConfNb=1
SH.FMC_NBL0.0=FMC_NBL0,Sd2ByteEnable1
SH.FMC_NBL0.ConfNb=1
SH.FMC_NBL1.0=FMC_NBL1,Sd2ByteEnable1
SH.FMC_NBL1.ConfNb=1
SH.FMC_SDCLK.0=FMC_SDCLK,13b-sda1
SH.FMC_SDCLK.ConfNb=1
SH.FMC_SDNCAS.0=FMC_SDNCAS,13b-sda1
SH.FMC_SDNCAS.ConfNb=1
SH.FMC_SDNRAS.0=FMC_SDNRAS,13b-sda1
SH.FMC_SDNRAS.ConfNb=1
SH.FMC_SDNWE.0=FMC_SDNWE,13b-sda1
SH.FMC_SDNWE.ConfNb=1
TIM3.IPParameters=Prescaler,Period
TIM3.Period=100 - 1
TIM3.Prescaler=50 -1
UART4.BaudRate=9600
UART4.FIFOMode=FIFOMODE_DISABLE
UART4.IPParameters=BaudRate,FIFOMode
UART7.BaudRate=1000000
UART7.IPParameters=BaudRate
USART1.IPParameters=VirtualMode-Asynchronous
USART1.VirtualMode-Asynchronous=VM_ASYNC
USART2.AutoBaudRateEnableParam=UART_ADVFEATURE_AUTOBAUDRATE_ENABLE
USART2.IPParameters=VirtualMode-Asynchronous,AutoBaudRateEnableParam
USART2.VirtualMode-Asynchronous=VM_ASYNC
VP_ADC3_Vref_Input.Mode=IN-Vrefint
VP_ADC3_Vref_Input.Signal=ADC3_Vref_Input
VP_CRC_VS_CRC.Mode=CRC_Activate
VP_CRC_VS_CRC.Signal=CRC_VS_CRC
VP_FREERTOS_VS_CMSIS_V1.Mode=CMSIS_V1
VP_FREERTOS_VS_CMSIS_V1.Signal=FREERTOS_VS_CMSIS_V1
VP_HRTIM_VS_hrtimTimerANoOutput.Mode=No_output_TA
VP_HRTIM_VS_hrtimTimerANoOutput.Signal=HRTIM_VS_hrtimTimerANoOutput
VP_HRTIM_VS_hrtimTimerBNoOutput.Mode=No_output_TB
VP_HRTIM_VS_hrtimTimerBNoOutput.Signal=HRTIM_VS_hrtimTimerBNoOutput
VP_IWDG1_VS_IWDG.Mode=IWDG_Activate
VP_IWDG1_VS_IWDG.Signal=IWDG1_VS_IWDG
VP_LWIP_VS_Enabled.Mode=Enabled
VP_LWIP_VS_Enabled.Signal=LWIP_VS_Enabled
VP_MEMORYMAP_VS_MEMORYMAP.Mode=CurAppReg
VP_MEMORYMAP_VS_MEMORYMAP.Signal=MEMORYMAP_VS_MEMORYMAP
VP_RTC_VS_RTC_Activate.Mode=RTC_Enabled
VP_RTC_VS_RTC_Activate.Signal=RTC_VS_RTC_Activate
VP_RTC_VS_RTC_Calendar.Mode=RTC_Calendar
VP_RTC_VS_RTC_Calendar.Signal=RTC_VS_RTC_Calendar
VP_SYS_VS_tim1.Mode=TIM1
VP_SYS_VS_tim1.Signal=SYS_VS_tim1
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
board=custom
rtos.0.ip=FREERTOS
isbadioc=false
