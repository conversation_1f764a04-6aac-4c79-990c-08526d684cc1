/**
******************************************************************************
* @file      system_status.c
* <AUTHOR>
* @date      2023-09-05
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "model.h"

/*******************************************************************************************
 * @brief Variables
******************************************************************************************/
SystemStatus        g_curSysStatus __attribute__((aligned(4))) = {0};
SysStatus_Handler   g_hSysStatus __attribute__((aligned(4))) = {0};

/*******************************************************************************************
  * @brief Functions List
*******************************************************************************************/
static void setMacAddress(sMac Mac);
static sMac getMacAddress(void);

static void Init_SysStatus(SystemStatus *pStat);
void SystemStatusHandler_Init(void);

/*******************************************************************************************
 * @brief 
 * @param
 * @return 
*******************************************************************************************/
static void setMacAddress(sMac Mac)
{
	g_hSysStatus.m_pStat->mac = Mac;
}

/*******************************************************************************************
 * @brief 
 * @param
 * @return 
*******************************************************************************************/
static sMac getMacAddress(void)
{
    return g_hSysStatus.m_pStat->mac;
}

/*******************************************************************************************
 * @brief 
 * @param
 * @return 
*******************************************************************************************/
static sGPS_Component *getGpsData(void)
{
    return &g_hSysStatus.m_pStat->gps;
}

/*******************************************************************************************
 * @brief 
 * @param
 * @return 
 *******************************************************************************************/
static char *getSwVersionString(int *pVer) 
{
	static char str_arr[20] = {0, };

	snprintf(str_arr, sizeof(str_arr), "V%d.%d.%d.%d", pVer[0], pVer[1], pVer[2], pVer[3]);
	
	return str_arr;
}

/*******************************************************************************************
 * @brief 
 * @param
 * @return 
 *******************************************************************************************/
static char *getHwVersionString(int *pVer) 
{
	static char str_arr[20] = {0, };

	snprintf(str_arr, sizeof(str_arr), "V%d.%d", pVer[0], pVer[1]);
	
	return str_arr;
}

/*******************************************************************************************
 * @brief Initialize System Status
 * @param
 * @return
*******************************************************************************************/
static void Init_SysStatus(SystemStatus *pStat)
{
    if(pStat == NULL)
    {
        return;
    }

    memset(pStat, 0x00, sizeof(SystemStatus));
}

/*******************************************************************************************
 * @brief
 * @param
 * @return 
*******************************************************************************************/
void SystemStatusHandler_Init(void)
{
    g_hSysStatus.m_pStat = &g_curSysStatus;
    g_hSysStatus.m_Drv.pMac = &gDrvMac;
    g_hSysStatus.m_Drv.pRtc = &gDrvRtc;
    g_hSysStatus.m_Drv.pAdc = &gDrvAdc;
    
    g_hSysStatus.InitSysStatus = Init_SysStatus;

    g_hSysStatus.getGpsData = getGpsData;

    g_hSysStatus.setMacAddress = setMacAddress;
    g_hSysStatus.getMacAddress = getMacAddress;

    g_hSysStatus.getSwVersionString = getSwVersionString;
	g_hSysStatus.getHwVersionString = getHwVersionString;
    
    g_hSysStatus.InitSysStatus(g_hSysStatus.m_pStat);

    DEBUG_MSG("***************************************\r\n");
	DEBUG_MSG("* System Status Variable Size Check * \r\n");
	DEBUG_MSG("* Size : %d\r\n", sizeof(SystemStatus));
	DEBUG_MSG("***************************************\r\n");
}
