/**
******************************************************************************
* @file      system_RTC.c
* <AUTHOR>
* @date      2023-11-09
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2023 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "model.h"
#include "system_RTC.h"

extern RTC_HandleTypeDef hrtc;

u8 rtc_reset_flag = 0;
void Msg_Std_Time_Update(void)
{
    RTC_TimeTypeDef sTime = {0};
    RTC_DateTypeDef sDate = {0};
    static u8 pre_sec = 0;
    
    // u8 sysMode = 0;
    // sysMode = g_hSysStatus.m_pStat->mode.system;
    // if(sysMode != SYS_BOOT_RUN)
    // {
    //     return;
    // }

    if(rtc_reset_flag == 1)
    {
        sDate.Year      = g_hSysStatus.m_pStat->timedate.bits.year;
        sDate.Month     = g_hSysStatus.m_pStat->timedate.bits.month;
        sDate.Date      = g_hSysStatus.m_pStat->timedate.bits.day;

        sTime.Hours     = g_hSysStatus.m_pStat->timedate.bits.hour;
        sTime.Minutes   = g_hSysStatus.m_pStat->timedate.bits.min;
        sTime.Seconds   = g_hSysStatus.m_pStat->timedate.bits.sec;

        HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
        HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    }

    HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN);

    if(pre_sec != sTime.Seconds)
    {
        g_hSysStatus.m_pStat->timedate.bits.year        = sDate.Year;
        g_hSysStatus.m_pStat->timedate.bits.month       = sDate.Month;
        g_hSysStatus.m_pStat->timedate.bits.day         = sDate.Date;

        g_hSysStatus.m_pStat->timedate.bits.hour        = sTime.Hours;
        g_hSysStatus.m_pStat->timedate.bits.min         = sTime.Minutes;
        g_hSysStatus.m_pStat->timedate.bits.sec         = sTime.Seconds;

        // HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BIN);
        // HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BIN);
    }
    pre_sec = sTime.Seconds;
}

void Msg_Std_Time_Update_Fast_Testing(void)
{
    static int cnt = 0;
    static RTC_TimeTypeDef sTime = {0};
    static RTC_DateTypeDef sDate = 
    {
        0,
        0,
        0,
        15,
    };

    cnt += 1;
    if(cnt >= 10)
    {
        sTime.Minutes += 1;
        if(sTime.Minutes >= 60)
        {
            sTime.Minutes -= 60;
            sTime.Hours += 1;
            if(sTime.Hours >= 24)
            {
                sTime.Hours -= 24;
                sDate.Date += 1;
                if(sDate.Date >= 30)
                {
                    sDate.Date -= 30;
                    sDate.Month += 1;
                    if(sDate.Month >= 12)
                    {
                        sDate.Month -= 12;
                        sDate.Year += 1;
                    }
                }
            }
        }

        g_hSysStatus.m_pStat->timedate.bits.year        = sDate.Year;
        g_hSysStatus.m_pStat->timedate.bits.month       = sDate.Month;
        g_hSysStatus.m_pStat->timedate.bits.day         = sDate.Date;

        g_hSysStatus.m_pStat->timedate.bits.hour        = sTime.Hours;
        g_hSysStatus.m_pStat->timedate.bits.min         = sTime.Minutes;
        g_hSysStatus.m_pStat->timedate.bits.sec         = sTime.Seconds;
        
        cnt = 0;
    }
}

void System_Rtc_Task(void)
{
    Msg_Std_Time_Update();
    // Msg_Std_Time_Update_Fast_Testing();
}
