/**
******************************************************************************
* @file      TargetEthernet.c
* <AUTHOR>
* @date      2023-1-10
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetBoard.h"

void TargetEthernet_Init(void const *argument)
{
	//struct netif *netif = (struct netif *)argument;

	// TargetTCP_Init(netif);
	// TargetUDP_Init(netif);
}
