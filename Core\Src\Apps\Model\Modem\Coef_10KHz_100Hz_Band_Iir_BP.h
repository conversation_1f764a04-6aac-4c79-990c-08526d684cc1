/**
******************************************************************************
* @file      Coef_10KHz_100Hz_Band_Iir_BP.c
* <AUTHOR>
* @date      2024-09-04
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_MODEM_COEF_10KHZ_100HZ_BAND_IIR_BP_H_
#define SRC_APPS_MODEL_MODEM_COEF_10KHZ_100HZ_BAND_IIR_BP_H_

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "AllConst.h"
#include "CommonLib.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  FLT_10KHZ_100HZ_BAND_IIR_BP_SIZE         (16)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

#if defined(__cplusplus)
extern "C" {
#endif   // __cplusplus


//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
extern tBaseIIR G_xIir_10Khz_BP_Of_518KHz;
extern tBaseIIR G_xIir_10Khz_BP_Of_490KHz;
extern tBaseIIR G_xIir_10Khz_BP_Of_42095KHz;
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

#endif /* SRC_APPS_MODEL_MODEM_COEF_10KHZ_100HZ_BAND_IIR_BP_H_ */
