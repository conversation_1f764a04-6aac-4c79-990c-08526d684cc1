/*
 * LowPassFilter.h
 *
 *  Created on: Sep 8, 2024
 *      Author: jay.yang
 */

#ifndef SRC_AUDIO_BC_H_
#define SRC_AUDIO_BC_H_

#include <stdint.h>
#include <stdbool.h>

#define DECIMATE_FACTOR         (6)
#define DWSBUFFCNT              (3)
#define DWSBUFFSIZE             (80)

#define I2S_AUDIO_OUT_SCALE		(1)
#define I2S_AUDIO_DATA_MAX      (+32767)
#define I2S_AUDIO_DATA_MIN      (-32768)
#define I2S_AUDIO_DATA_HALF     (I2S_AUDIO_DATA_MAX / 2)
#define CODEC_DATA_INT_TO_FLOAT (1.0f / (float)I2S_AUDIO_DATA_MAX)
#define CODEC_DATA_FLOAT_TO_INT ((float)I2S_AUDIO_DATA_MAX)
#define CODEC_OUT_SCALED_FACTOR (float)(I2S_AUDIO_DATA_MAX*I2S_AUDIO_OUT_SCALE)

typedef  struct {
    float         *pData;
    volatile int   nPosX;
}xLPFX;

typedef  struct {
    uint16_t      *pData[DWSBUFFCNT];
    volatile int   nHead;
    volatile int   nTail;
    volatile int   nPosX;

    volatile float fSmplAvr;
    volatile int32_t  iSmplAvr;
    volatile uint32_t dSmplCnt;
}xDWSX;

void     audio_bc_create(void);
void     audio_bc_destroy(void);
void     audio_bc_reset(void);
uint16_t audio_bc_lpf_apply(uint16_t fIn);
void     audio_bc_dwn_smpl(float fIn);
bool     audio_bc_is_data_ready(void);
uint16_t *audio_bc_get_data(void);

float    CodecIntDataToFloat(int16_t nData);
int16_t  CodecFloatToIntData(float fData);
int16_t  CodecScaledFloatToIntData(float fData);
#endif /* SRC_AUDIO_BC_H_ */
