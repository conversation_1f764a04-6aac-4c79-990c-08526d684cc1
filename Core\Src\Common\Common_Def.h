/**
******************************************************************************
* @file      System_Def.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_COMMON_SYSTEM_DEF_H_
#define SRC_COMMON_SYSTEM_DEF_H_

#include "main.h"

/******************************************************************************
 * MPU_FSK1 = International FSK
 * MPU_FSK2 = Local FSK
 * 4MOSC_SEL = ??
 * DAC1_OUT1 = 1.7KHz +- 85Hz
 * UART7 = Control Unit Comm.
 * USART1 = Print Comm.
 * UART4 = INS Unit Comm.
 * USART2 = Bam Comm.
 * UART5 = GPS Comm.
******************************************************************************/

#ifndef TRUE
#define TRUE	1
#endif

#ifndef FALSE
#define FALSE	0
#endif

#define MAX(a,b) ((a > b) ? a : b)
#define MIN(a,b) ((a > b) ? b : a)
#define ABS(x) ((x > 0) ? (x) : (-x))

#define TBD_TRUE	1
#define TBD_FALSE	0

#define TBD_SUCCESS	1
#define TBD_FAIL		0

#define TBD_ON		1
#define TBD_OFF		0

// type definition has fixed '24.06.01
typedef unsigned char       BOOL;
typedef char*               String;
typedef signed char         i8;
typedef short               i16;
typedef int                 i32;
typedef long long           i64;
typedef unsigned char       u8;
typedef unsigned short      u16;
typedef unsigned int        u32;
typedef unsigned long long  u64;
typedef float               f32;
typedef double              f64;
typedef unsigned char       BOOL;

typedef union 
{
    u32 data;
    struct
    {
        // all 4byte
        u8 sec : 6;
        u8 min : 6;
        u8 hour : 5;
        u8 day : 5;
        u8 month : 4;
        u8 year : 6;
    }bits;
}TimeDateFor4byte_s;

#endif /* SRC_COMMON_SYSTEM_DEF_H_ */
