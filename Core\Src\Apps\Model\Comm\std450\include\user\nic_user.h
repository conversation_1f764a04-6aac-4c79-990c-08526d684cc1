///@file     nic_user.h
///@brief    Network Interface Card user application function code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#ifndef NIC_USER_H
#define NIC_USER_H

extern int  get_nic_count(void);
extern int  check_nic(const char *nic);
extern int  check_nic_connect(const char *nic);
extern int  check_nic_link_status(void);
extern int  check_ping(const char *ip);
extern int  check_multicast(const char *nic);
extern void set_multicast(const char* nic, int set);
extern void set_igmp_ver(const char* nic, int igmp_ver);
#endif  /* NIC_USER_H */