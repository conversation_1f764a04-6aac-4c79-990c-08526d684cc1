/**
******************************************************************************
* @file      mt25ql128aba.h
* <AUTHOR>
* @date      2023-4-19
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef __MT25QL128ABA_H__
#define __MT25QL128ABA_H__

#ifdef __cplusplus
 extern "C" {
#endif

#include "stm32h7xx_hal.h"
#include "mt25ql128aba_conf.h"

/** @defgroup MT25QL128A_Exported_Types
  * @{
  */
typedef struct {
	uint32_t FlashSize;          /*!< Size of the flash */
	uint32_t EraseSectorSize;    /*!< Size of sectors for the erase operation */
	uint32_t EraseSectorsNumber; /*!< Number of sectors for the erase operation */
	uint32_t ProgPageSize;       /*!< Size of pages for the program operation */
	uint32_t ProgPagesNumber;    /*!< Number of pages for the program operation */
}MT25QL128A_Info;

#define MT25QL128A_MAX_BULK_ERASE_TIME         250000
#define MT25QL128A_MAX_SECTOR_ERASE_TIME       3000
#define MT25QL128A_MAX_SUBSECTOR_ERASE_TIME    800

/* MT25QL Component Error codes *********************************************/
#define MT25QL128A_OK                           0
#define MT25QL128A_ERROR_INIT                  	-1
#define MT25QL128A_ERROR_COMMAND               	-2
#define MT25QL128A_ERROR_TRANSMIT              	-3
#define MT25QL128A_ERROR_RECEIVE               	-4
#define MT25QL128A_ERROR_AUTOPOLLING           	-5
#define MT25QL128A_ERROR_MEMORYMAPPED          	-6
#define MT25QL128A_ERROR_UNKNOWN						-7
/**exported type **/


/******************MT25QL128A_Info_t**********************/
typedef struct
{
	uint32_t FlashSize;          /*!< Size of the flash */
	uint32_t EraseSectorSize;    /*!< Size of sectors for the erase operation */
	uint32_t EraseSectorsNumber; /*!< Number of sectors for the erase operation */
	uint32_t ProgPageSize;       /*!< Size of pages for the program operation */
	uint32_t ProgPagesNumber;    /*!< Number of pages for the program operation */
}MT25QL128A_Info_t;


/******************MT25QL128A_Transfer_t**********************/
#if (TBD_TYPE == BDT_MFHF_ES1)
typedef enum
{
  MT25QL128A_EXT_SPI_MODE = 0,              /*!< 1-1-1 commands, Power on H/W default setting */
  MT25QL128A_DUAL_SPI_MODE,                 /*!< 1-1-2, 1-2-2 read commands                   */
  MT25QL128A_QUAD_SPI_MODE,                 /*!< 4-4-4 commands                               */
}MT25QL128A_Interface_t;

#else
typedef enum
{
  MT25QL128A_SPI_MODE = 0,                 /*!< 1-1-1 commands, Power on H/W default setting */
  MT25QL128A_SPI_2IO_MODE,                 /*!< 1-1-2, 1-2-2 read commands                   */
  MT25QL128A_SPI_4IO_MODE,                 /*!< 1-1-4, 1-4-4 read commands                   */
  MT25QL128A_QPI_MODE                      /*!< 4-4-4 commands                               */
}MT25QL128A_Interface_t;
#endif  

/******************MT25QL128A_Transfer_t**********************/

typedef enum
{
  MT25QL128A_STR_TRANSFER = 0,             /* Single Transfer Rate */
  MT25QL128A_DTR_TRANSFER                  /* Double Transfer Rate */
} MT25QL128A_Transfer_t;

/******************MT25QL128A_DualFlash_t**********************/
typedef enum
{
  MT25QL128A_DUALFLASH_DISABLE = QSPI_DUALFLASH_DISABLE, /*!<  Single flash mode              */
  MT25QL128A_DUALFLASH_ENABLE  = QSPI_DUALFLASH_ENABLE
} MT25QL128A_DualFlash_t;


/******************MT25QL128A_Erase_t**********************/
typedef enum
{
  MT25QL128A_ERASE_4K = 0,                 /*!< 4K size Sector erase */
  MT25QL128A_ERASE_32K,                    /*!< 32K size Block erase */
  MT25QL128A_ERASE_64K,                    /*!< 64K size Block erase */
  MT25QL128A_ERASE_CHIP                    /*!< Whole chip erase     */
} MT25QL128A_Erase_t;
/**
  * @}
  */

/** @defgroup MT25QL128A_Exported_Constants
  * @{
  */

/**
  * @brief  MT25QL Configuration
  */
#define MT25QL128A_FLASH_SIZE                  0x1000000 /* 128 MBits => 16MBytes */
#define MT25QL128A_SECTOR_SIZE                 0x10000   /* 256 sectors of 64KBytes */
#define MT25QL128A_SUBSECTOR_SIZE              0x1000    /* 4096 subsectors of 4kBytes */
#define MT25QL128A_PAGE_SIZE                   0x100     /* 65536 pages of 256 bytes */

#define MT25QL128A_DIE_ERASE_MAX_TIME          460000
#define MT25QL128A_SECTOR_ERASE_MAX_TIME       1000
#define MT25QL128A_SUBSECTOR_ERASE_MAX_TIME    400

/* software RESET Operations */
#define MT25QL128A_RESET_ENABLE_CMD                     0x66
#define MT25QL128A_RESET_MEMORY_CMD                     0x99

/* READ ID Operations */
#define MT25QL128A_READ_ID_CMD									0x9E
#define MT25QL128A_READ_ID_CMD2									0x9F
#define MT25QL128A_MULTIPLE_IO_READ_ID_CMD					0xAF
#define MT25QL128A_READ_SERIAL_FLASH_DISCO_PARAM_CMD		0x5A

/* READ MEMORY Operations */
#define MT25QL128A_READ_CMD										0x03
#define MT25QL128A_FAST_READ_CMD									0x0B
#define MT25QL128A_DUAL_OUT_FAST_READ_CMD						0x3B
#define MT25QL128A_DUAL_INOUT_FAST_READ_CMD					0xBB
#define MT25QL128A_QUAD_OUT_FAST_READ_CMD						0x6B
#define MT25QL128A_QUAD_INOUT_FAST_READ_CMD					0xEB
#define MT25QL128A_DTR_FAST_READ_CMD							0x0D
#define MT25QL128A_DTR_DUAL_OUT_FAST_READ_CMD				0x3D
#define MT25QL128A_DTR_DUAL_INOUT_FAST_READ_CMD				0xBD
#define MT25QL128A_DTR_QUAD_OUT_FAST_READ_CMD				0x6D
#define MT25QL128A_DTR_QUAD_INOUT_FAST_READ_CMD				0xED
#define MT25QL128A_QUAD_INOUT_WORD_READ						0xE7

/* WRITE Operations */
#define MT25QL128A_WRITE_ENABLE_CMD								0x06
#define MT25QL128A_WRITE_DISABLE_CMD							0x04

/* READ REGISTER Operations */
#define MT25QL128A_READ_STATUS_REG_CMD							0x05
#define MT25QL128A_READ_FLAG_STATUS_REG_CMD					0x70
#define MT25QL128A_READ_NONVOL_CFG_REG_CMD					0xB5
#define MT25QL128A_READ_VOL_CFG_REG_CMD						0x85
#define MT25QL128A_READ_ENHANCED_VOL_CFG_REG_CMD			0x65
#define MT25QL128A_READ_GEN_PURPOSE_READ_REG_CMD			0x96

/* WRITE REGISTER Operations */
#define MT25QL128A_WRITE_STATUS_REG_CMD						0x01
#define MT25QL128A_WRITE_NONVOL_CFG_REG_CMD					0xB1
#define MT25QL128A_WRITE_VOL_CFG_REG_CMD						0x81
#define MT25QL128A_WRITE_ENHANCED_VOL_CFG_REG_CMD			0x61

/* CLEAR FLAG STATUS REGISTER Operation */
#define MT25QL128A_CLEAR_FLAG_STATUS_REG_CMD					0x50

/* PROGRAM Operations */
#define MT25QL128A_PAGE_PROG_CMD									0x02
#define MT25QL128A_DUAL_IN_FAST_PROG_CMD						0xA2
#define MT25QL128A_EXT_DUAL_IN_FAST_PROG_CMD					0xD2
#define MT25QL128A_QUAD_IN_FAST_PROG_CMD						0x32
#define MT25QL128A_EXT_QUAD_IN_FAST_PROG_CMD					0x38

/* ERASE Operations */
#define MT25QL128A_32K_SUBSECTOR_ERASE_CMD					0x52
#define MT25QL128A_4K_SUBSECTOR_ERASE_CMD						0x20
#define MT25QL128A_SECTOR_ERASE_CMD								0xD8
#define MT25QL128A_BULK_ERASE_CMD								0xC7
#define MT25QL128A_BULK_ERASE_CMD2								0x60

/* SUSPEND/RESUME Operations */
#define MT25QL128A_PROG_ERASE_SUSPEND_CMD						0x75	
#define MT25QL128A_PROG_ERASE_RESUME_CMD						0x7A	

/* ONE-TIME PROGRAMMABLE (OTP) Operations */
#define MT25QL128A_READ_OTP_ARRAY_CMD							0x4B	
#define MT25QL128A_PROG_OTP_ARRAY_CMD							0x42	

/* QUAD PROTOCOL Operations */
#define MT25QL128A_ENTER_QUAD_INOUT_MODE_CMD					0x35	
#define MT25QL128A_RESET_QUAD_INOUT_MODE_CMD					0xF5	

/* DEEP POWER-DOWN Operations */
#define MT25QL128A_ENTER_DEEP_POWER_DOWN_CMD					0xB9	
#define MT25QL128A_RELEASE_FROM_DEEP_POWER_DOWN_CMD		0xAB	

/* ADVANCED SECTOR PROTECTION Operations */
#define MT25QL128A_READ_SECTOR_PROTECTION_CMD				0x2D	
#define MT25QL128A_PROGRAM_SECTOR_PROTECTION_CMD			0x2C	
#define MT25QL128A_READ_VOLATILE_LOCK_BITS_CMD				0xE8	
#define MT25QL128A_WRITE_VOLATILE_LOCK_BITS_CMD				0xE5	
#define MT25QL128A_READ_NON_VOLATILE_LOCK_BITS_CMD			0xE2	
#define MT25QL128A_WRITE_NON_VOLATILE_LOCK_BITS_CMD		0xE3	
#define MT25QL128A_ERASE_NON_VOLATILE_LOCK_BITS_CMD		0xE4	
#define MT25QL128A_READ_GLOBAL_FREEZE_BIT_CMD				0xA7	
#define MT25QL128A_WRITE_GLOBAL_FREEZE_BIT_CMD				0xA6	
#define MT25QL128A_READ_PASSWORD_CMD							0x27	
#define MT25QL128A_WRITE_PASSWORD_CMD							0x28	
#define MT25QL128A_UNLOCK_PASSWORD_CMD							0x29	

/* ADVANCED FUNCTION INTERFACE Operations */
#define MT25QL128A_INTERFACE_ACTIVATION_CMD					0x9B
#define MT25QL128A_CYCLIC_REDUNDANCY_CHECK_CMD				0x9B
#define MT25QL128A_CYCLIC_REDUNDANCY_CHECK_CMD2				0x26

/**
  * @brief  MT25QL Registers
  */
/* Status Register */
#define MT25QL128A_SR_WIP                      ((uint8_t)0x01)    /*!< Write in progress */
#define MT25QL128A_SR_WREN                     ((uint8_t)0x02)    /*!< Write enable latch */
#define MT25QL128A_SR_BLOCKPR                  ((uint8_t)0x5C)    /*!< Block protected against program and erase operations */
#define MT25QL128A_SR_PRBOTTOM                 ((uint8_t)0x20)    /*!< Protected memory area defined by BLOCKPR starts from top or bottom */
#define MT25QL128A_SR_SRWREN                   ((uint8_t)0x80)    /*!< Status register write enable/disable */

/* Non volatile Configuration Register */
#define MT25QL128A_NVCR_NBADDR                 ((uint16_t)0x0001) /*!< 3-bytes or 4-bytes addressing */
#define MT25QL128A_NVCR_SEGMENT                ((uint16_t)0x0002) /*!< Upper or lower 128Mb segment selected by default */
#define MT25QL128A_NVCR_DUAL                   ((uint16_t)0x0004) /*!< Dual I/O protocol */
#define MT25QL128A_NVCR_QUAB                   ((uint16_t)0x0008) /*!< Quad I/O protocol */
#define MT25QL128A_NVCR_RH                     ((uint16_t)0x0010) /*!< Reset/hold */
#define MT25QL128A_NVCR_DTRP                   ((uint16_t)0x0020) /*!< Double transfer rate protocol */
#define MT25QL128A_NVCR_ODS                    ((uint16_t)0x01C0) /*!< Output driver strength */
#define MT25QL128A_NVCR_XIP                    ((uint16_t)0x0E00) /*!< XIP mode at power-on reset */
#define MT25QL128A_NVCR_NB_DUMMY               ((uint16_t)0xF000) /*!< Number of dummy clock cycles */

/* Volatile Configuration Register */
#define MT25QL128A_VCR_WRAP                    ((uint8_t)0x03)    /*!< Wrap */
#define MT25QL128A_VCR_XIP                     ((uint8_t)0x08)    /*!< XIP */
#define MT25QL128A_VCR_NB_DUMMY                ((uint8_t)0xF0)    /*!< Number of dummy clock cycles */

/* Extended Address Register */
#define MT25QL128A_EAR_HIGHEST_SE              ((uint8_t)0x03)    /*!< Select the Highest 128Mb segment */
#define MT25QL128A_EAR_THIRD_SEG               ((uint8_t)0x02)    /*!< Select the Third 128Mb segment */
#define MT25QL128A_EAR_SECOND_SEG              ((uint8_t)0x01)    /*!< Select the Second 128Mb segment */
#define MT25QL128A_EAR_LOWEST_SEG              ((uint8_t)0x00)    /*!< Select the Lowest 128Mb segment (default) */

/* Enhanced Volatile Configuration Register */
#define MT25QL128A_EVCR_ODS                    ((uint8_t)0x07)    /*!< Output driver strength */
#define MT25QL128A_EVCR_RH                     ((uint8_t)0x10)    /*!< Reset/hold */
#define MT25QL128A_EVCR_DTRP                   ((uint8_t)0x20)    /*!< Double transfer rate protocol */
#define MT25QL128A_EVCR_DUAL                   ((uint8_t)0x40)    /*!< Dual I/O protocol */
#define MT25QL128A_EVCR_QUAD                   ((uint8_t)0x80)    /*!< Quad I/O protocol */

/* Flag Status Register */
#define MT25QL128A_FSR_NBADDR                  ((uint8_t)0x01)    /*!< 3-bytes or 4-bytes addressing */
#define MT25QL128A_FSR_PRERR                   ((uint8_t)0x02)    /*!< Protection error */
#define MT25QL128A_FSR_PGSUS                   ((uint8_t)0x04)    /*!< Program operation suspended */
#define MT25QL128A_FSR_PGERR                   ((uint8_t)0x10)    /*!< Program error */
#define MT25QL128A_FSR_ERERR                   ((uint8_t)0x20)    /*!< Erase error */
#define MT25QL128A_FSR_ERSUS                   ((uint8_t)0x40)    /*!< Erase operation suspended */
#define MT25QL128A_FSR_READY                   ((uint8_t)0x80)    /*!< Ready or command in progress */

/**
  * @}
  */

/** @defgroup MT25QL128A_Exported_Functions
  * @{
  */

int32_t MT25QL128A_GetFlashInfo(MT25QL128A_Info_t *pInfo);
#if (TBD_TYPE == BDT_MFHF_ES1)
int32_t MT25QL128A_AutoPollingMemReady(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode,uint32_t Timeout);
#else
int32_t MT25QL128A_AutoPollingMemReady(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);
#endif

/* Register/Setting Commands *************************************************/
int32_t MT25QL128A_WriteEnable(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);

int32_t MT25QL128A_Sub4KSectorErase(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint32_t BlockAddress);
int32_t MT25QL128A_Sub32KSectorErase(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint32_t BlockAddress);
int32_t MT25QL128A_SectorErase(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint32_t BlockAddress);
int32_t MT25QL128A_ChipErase(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);

int32_t MT25QL128A_PageProgram(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint8_t *pData, uint32_t WriteAddr, uint32_t Size);
int32_t MT25QL128A_ReadSTR(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint32_t ReadAddr, uint8_t *pData, uint32_t Size);
int32_t MT25QL128A_ReadDTR(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint8_t *pData, uint32_t ReadAddr, uint32_t Size);
int32_t MT25QL128A_ReadStatusRegister(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint8_t *Value);
int32_t MT25QL128A_EnterQuadMode(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);
int32_t MT25QL128A_ExitQuadMode(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);

int32_t MT25QL128A_EnableMemoryMappedMode(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);
int32_t MT25QL128A_WriteDisable(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);
int32_t MT25QL128A_DummyCyclesCfg(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);
int32_t MT25QL128A_ReadID(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint8_t *ID);

int32_t MT25QL128A_ResetMemory(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);
int32_t MT25QL128A_ResetEnable(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);

int32_t MT25QL128A_ReadSPBLockRegister(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode, uint8_t *SPBRegister);
int32_t MT25QL128A_ReleaseFromDeepPowerDown(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);
int32_t MT25QL128A_EnterDeepPowerDown(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);
int32_t MT25QL128A_ProgEraseResume(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);
int32_t MT25QL128A_ProgEraseSuspend(QSPI_HandleTypeDef *Ctx, MT25QL128A_Interface_t Mode);
/**
  * @}
  */

#ifdef __cplusplus
 	}
#endif

#endif	/* __MT25QL128ABA_H__ */

