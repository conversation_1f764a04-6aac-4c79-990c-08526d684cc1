/**
******************************************************************************
* @file      Nmea0183.c
* <AUTHOR>
* @date      2024-07-07
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "Nmea0183.h"

/************************************************************************************************************/
/**
 * @brief  
 * @param  
 * @return 
*/
/************************************************************************************************************/
int nmea0183_parsing(char *pSentence, int nSize)
{
	int nRet = NMEA0183_ERROR;

	if((pSentence != NULL) && (nSize >0))
	{
		if(nmea0183_set_sentence(pSentence, nSize) == NMEA0183_OK)
		{
            nRet = NMEA0183_OK;
		}
	}
	return nRet;
}

/************************************************************************************************************/
/**
 * @brief  
 * @param  
 * @return 
*/
/************************************************************************************************************/
int nmea0183_set_sentence(char *pInput, int nSize)
{
	int nRet = NMEA0183_ERROR;
	
	if(pInput != NULL)
	{
		if(nmea0183_validate(pInput, nSize) == NMEA0183_OK)
		{
			return NMEA0183_OK;
		}
	}
	
	return nRet;
}

/************************************************************************************************************/
/**
 * @brief  Validation of input nmea sentence
 * @param  pInput(input nmea sentence), nSize(byte size of input nmea sentence)
 * @return -1(an input nmea sentence is a invalid sentence), 0(an input nmea sentence is a valid sentence)
*/
/************************************************************************************************************/
int nmea0183_validate(char *pInput, int nSize)
{
	int nRet = NMEA0183_OK;
	char strCheckSum[3];
	u8 calCs = 0;
	u8 rcvCs = 0;
	
	/* should have atleast 9 characters */
	if(nSize < 9)
	{
		DEBUG_MSG("[ERROR] nmea0183 - size min \r\n");
		return NMEA0183_ERROR;
	}

	/* should be less or equal to 82 characters */
	if(nSize > NMEA0183_MAX_SIZE)
	{
		DEBUG_MSG("[ERROR] nmea0183 - size max \r\n");
		return NMEA0183_ERROR;
	}

	/* should start with '$' or '!' */
	if((*pInput != NMEA0183_GENERAL_S_DEL) && (*pInput != NMEA0183_ENCAPSULATED_S_DEL))
	{
		DEBUG_MSG("[ERROR] nmea0183 - start frame >> %s %c\r\n",pInput, *pInput);
		return NMEA0183_ERROR;
	}

	/* should end with '\r', '\n' */
	if((pInput[nSize-2] != NMEA0183_CR) && (pInput[nSize-1] != NMEA0183_LF))
	{
		DEBUG_MSG("[ERROR] nmea0183 - end frame \r\n");
		return NMEA0183_ERROR;
	}

	if(nmea0183_has_checksum(pInput, nSize) == NMEA0183_OK)
	{
		strCheckSum[0] = pInput[nSize - 4];
		strCheckSum[1] = pInput[nSize - 3];
		strCheckSum[2] = '\0';
		rcvCs = (u8)strtol(strCheckSum, NULL, 16);
		calCs = nmea0183_calc_checksum(pInput);
		if(rcvCs != calCs)
		{
			DEBUG_MSG("[ERROR] nmea0183 - comp checksum \r\n");
			return NMEA0183_ERROR;
		}
	}

	/* compare calculated checksum with input checksum */
	return nRet;
}

/************************************************************************************************************/
/**
 * @brief  
 * @param  
 * @return 
*/
/************************************************************************************************************/
u8 nmea0183_calc_checksum(const char *pSentence)
{
	const char *n = pSentence + 1;
	u8 cs = 0;

	while((*n != NMEA0183_CS_DEL)
			&& (*n != NMEA0183_CR)
			&& (*n != NMEA0183_NULL_CHAR))
	{
		cs ^= (u8)(*n);
		n++;
	}
	return cs;
}

/************************************************************************************************************/
/**
 * @brief  
 * @param  
 * @return 
*/
/************************************************************************************************************/
int nmea0183_has_checksum(const char *pSentence, int nSize)
{
	int nRet = NMEA0183_ERROR;
	if(nSize > 5)
	{
		//DEBUG_MSG("[ERROR] nmea0183 - has checksum >> %c\r\n",pSentence[nSize - 5]);
		if(pSentence[nSize - 5] == NMEA0183_CS_DEL)
		{
			nRet = NMEA0183_OK;
		}
	}
	return nRet;
}