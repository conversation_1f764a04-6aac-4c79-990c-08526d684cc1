/**
******************************************************************************
* @file      userdef.h
* <AUTHOR>
* @date      2022-09-23
* @brief
******************************************************************************
* @attention
*
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef __USERDEF_H__
#define __USERDEF_H__

#include "stdio.h"

#define EN_DBG_MSG
#define ARRAY_SIZE(array)  (sizeof(array) / sizeof(array[0]))

#ifdef EN_DBG_MSG
#define DM(...)			printf(__VA_ARGS__)   // DEBUG_MSG
#define DD(...)
#define DE(...)         printf(__VA_ARGS__)   // DEBUG_ERROR
#else
#define DM(...)
#define DD(...)
#define DE(...)
#endif

#define MAX(a,b) ((a > b) ? a : b)
#define MIN(a,b) ((a > b) ? b : a)

#ifndef TRUE
#define TRUE      1
#endif

#ifndef FALSE
#define FALSE     0
#endif

#define PASS     (0)
#define FAIL     (-1)

#ifndef YES
#define YES      1
#endif

#ifndef NO
#define NO       0
#endif

typedef signed char         s8;
typedef signed short        s16;
typedef signed int          s32;

typedef unsigned char       u8;
typedef unsigned short      u16;
typedef unsigned int        u32;
typedef unsigned long long  u64;

typedef float               float32_t;

#ifndef NULL
#define NULL  (void *) 0
#endif

#if defined(bool)
#else
typedef int bool;
#endif
#endif /* INC_USERDEF_H_ */
