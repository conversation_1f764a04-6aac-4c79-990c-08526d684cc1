/*
 * crc_utils.h
 *
 *  Created on: Apr 1, 2025
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#ifndef FWUPDATE_CRC_UTILS_H_
#define FWUPDATE_CRC_UTILS_H_

#include "stdint.h"

/**
 * @brief CRC-16 CCITT (Initial: 0xFFFF, Poly: 0x1021, No Reverse) 계산
 * @param data CRC를 계산할 데이터 배열 포인터
 * @param length 데이터 배열의 길이 (바이트 단위)
 * @retval 계산된 16비트 CRC 값
 */
uint16_t CalculateCrc16Ccitt(const uint8_t *data, uint16_t length);

#endif /* FWUPDATE_CRC_UTILS_H_ */
