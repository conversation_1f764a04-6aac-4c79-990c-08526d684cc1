/**
******************************************************************************
* @file      Nmea0183.h
* <AUTHOR>
* @date      2024-07-07
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_NMEA0183_NMEA0183_H_
#define SRC_APPS_MODEL_COMM_NMEA0183_NMEA0183_H_

#include "Common.h"

#define NMEA0183_MAX_SIZE				(82+1)	// Max NMEA0183 sentence size
#define NMEA0183_MIN_SIZE				8			// Min NMEA0183 sentence size
#define NMEA0183_TID_SIZE				2			// NMEA0183 Talker ID size
#define NMEA0183_SID_SIZE				3			// NMEA0183 Sentence ID size

#define NMEA0183_GENERAL_S_DEL				'$'	// General sentence start delimeter
#define NMEA0183_ENCAPSULATED_S_DEL			'!'	// Encapsulated sentence start delimeter
#define NMEA0183_F_DEL							','	// field of sentence delimeter
#define NMEA0183_CS_DEL							'*'	// checksum field delimeter	

#define NMEA0183_CR						0x0D
#define NMEA0183_LF						0x0A

#ifndef MAXFLOAT
#define MAXFLOAT						3.40282347e+38F
#endif

#define NMEA0183_NULL_DOUBLE			MAXFLOAT
#define NMEA0183_NULL_INTEGER			(int)0xc4653600
#define NMEA0183_NULL_CHAR				'\0'

#define NMEA0183_ERROR			-1	
#define NMEA0183_OK				0

u8 nmea0183_calc_checksum(const char *pSentence);
int nmea0183_validate(char *pInput, int nSize);
int nmea0183_set_sentence(char *pInput, int nSize);
int nmea0183_parsing(char *pSentence, int nSize);
int nmea0183_has_checksum(const char *pSentence, int nSize);

#endif /* SRC_APPS_MODEL_COMM_NMEA0183_NMEA0183_H_ */
