/**
******************************************************************************
* @file      TargetMac.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetBoard.h"
#include "TargetMac.h"

extern I2C_HandleTypeDef hi2c2;

sDrvMacComponent gDrvMac;

void TBD_Eeprom_Mac_Read(u8 *pMac)
{
    u8 cmd = TBD_MAC_CMD_READ_ADDR;
    u8 DeviceAddress = TBD_MAC_DEV_ADDR;
    u16 TimeOut = TBD_MAC_TIMEOUT;
    u8 MacAddrSize = MAX_MAC_ADDR_SIZE;

    if(pMac == NULL)
    {
        DEBUG_MSG("[%s] EEPROM Getting Function Input Parameter NULL\r\n",__FUNCTION__);
        return;
    }
    
    if(HAL_I2C_Master_Transmit(&hi2c2, DeviceAddress, &cmd, sizeof(cmd), TimeOut) == HAL_OK)
    {
        if(HAL_I2C_Master_Receive(&hi2c2, DeviceAddress, pMac, MacAddrSize, TimeOut) == HAL_OK)
        {
            DEBUG_MSG("[%s] EEPROM Getting Mac %d.%d.%d.%d.%d.%d\r\n",__FUNCTION__, pMac[0], pMac[1], pMac[2], pMac[3], pMac[4], pMac[5]);
        }
        else
        {
            DEBUG_MSG("[%s] EEPROM Getting I2C Receiver Fail\r\n",__FUNCTION__);
        }
    }
    else
    {
        DEBUG_MSG("[%s] EEPROM Getting I2C Master Fail\r\n",__FUNCTION__);
    }
}

/**
  * @brief  
  * @param  
  * @retval 
  */
void TBD_Init_Eeprom(void)
{
    memset(&gDrvMac, 0, sizeof(sDrvMacComponent));

    TBD_Eeprom_Mac_Read(&gDrvMac.Addr[0]);
}


