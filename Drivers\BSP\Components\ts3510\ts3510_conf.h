/**
 ******************************************************************************
 * @file    ts3510_conf.h
 * <AUTHOR> Application Team
 * @brief   This file contains specific configuration for the
 *          ts3510.c that can be modified by user.
 ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2019 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************
 */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef TS3510_CONF_H
#define TS3510_CONF_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
/* Macros --------------------------------------------------------------------*/
/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
#define TS3510_MAX_X_LENGTH                  4096U
#define TS3510_MAX_Y_LENGTH                  4096U
  
#ifdef __cplusplus
}
#endif
#endif /* TS3510_CONF_H */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
