/**
******************************************************************************
* @file      icomm_map.c
* <AUTHOR>
* @date      2024-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "Internal.h"
#include "TargetBoard.h"

Icomm_Protocol_Tx_s gIcommData_Tx;
Icomm_Protocol_Rx_s gIcommData_Rx;
Icomm_Status_Tx_s gIcommStatus_Tx[I_COMM_TX_TYPE_MAX] = {0};
Icomm_Status_Rx_s gIcommStatus_Rx[I_COMM_RX_TYPE_MAX] = {0};

int gIcommConfig_Rx_Tb[][2] =
{
        // CMD                                                  Length(max)
    {   I_COMM_RX_TYPE_SYSTEM_PARAMETER_SENDING_SUCCESS,            sizeof(Icomm_System_Ctrl_Rx_s)                  +COMM_FRAME_LEN},
    {   I_COMM_RX_TYPE_DIAG_REQ,                                    sizeof(Icomm_Diag_Req_Rx_s)                     +COMM_FRAME_LEN},
    {   I_COMM_RX_TYPE_NMEA0183_RECV,                               sizeof(Icomm_Nmea0183_Data_Rx_s)                +COMM_FRAME_LEN},
    {   I_COMM_RX_TYPE_GPIO_SET,                                    sizeof(Icomm_Gpio_Set_Rx_s)                     +COMM_FRAME_LEN},
    {   I_COMM_RX_TYPE_FSK_DEMOD_REQ_TEST,                          sizeof(Icomm_Fsk_DeMod_Test_Req_Rx_s)           +COMM_FRAME_LEN},
    {   I_COMM_RX_TYPE_CRC_RESP,                                    sizeof(Icomm_Response_CRC_Rx_s)                 +COMM_FRAME_LEN},
    {   I_COMM_RX_TYPE_CAN_SEND,                                    sizeof(Icomm_Can_Send_Rx_s)                     +COMM_FRAME_LEN},
    {   I_COMM_RX_COMM_CHECK,                                       sizeof(Icomm_Comm_Check_Rx_s)                   +COMM_FRAME_LEN},
    {   I_COMM_RX_TYPE_BAM_DATA_SEND,                               sizeof(Icomm_Nmea0183_Data_Rx_s)                +COMM_FRAME_LEN},
    {   I_COMM_RX_TYPE_SYSTEM_PARAMETER_GETTING_REQUEST,            sizeof(Icomm_System_Ctrl_Rx_s)                  +COMM_FRAME_LEN},
    {   I_COMM_RX_SYSTEM_SETTING_REQUEST,                           sizeof(Icomm_System_Setting_Request_Rx_s)       +COMM_FRAME_LEN},
    {   I_COMM_RX_SYSTEM_ALL_PARAMETER_SEND,                        sizeof(Icomm_Only_Byte_Tx_s)                    +COMM_FRAME_LEN},
    {   I_COMM_RX_RF_SELF_BER_TEST_REQUEST,                         sizeof(Icomm_Rf_Self_Test_Req_Rx_s)             +COMM_FRAME_LEN},

    {   I_COMM_TX_FIRMWARE_UPDATE_ENTRY_UPDATE,                     sizeof(Icomm_Only_Byte_Rx_s)                    +COMM_FRAME_LEN},
    {   I_COMM_TX_FIRMWARE_UPDATE_STATUS,                           sizeof(Icomm_Only_Byte_Rx_s)                    +COMM_FRAME_LEN},
    {   I_COMM_TX_FIRMWARE_UPDATE_DEVICE_SEARCH,                    sizeof(Icomm_Only_Byte_Rx_s)                    +COMM_FRAME_LEN},
    {   I_COMM_TX_FIRMWARE_UPDATE_DEVICE_SEARCH_ACK,                sizeof(Icomm_Only_Byte_Rx_s)                    +COMM_FRAME_LEN},
    {   I_COMM_RX_FIRMWARE_UPDATE_MASTER_BASE,                      sizeof(Icomm_Only_Byte_Rx_s)                    +COMM_FRAME_LEN},
    {   I_COMM_RX_FIRMWARE_UPDATE_SLAVE_BASE,                       sizeof(Icomm_Only_Byte_Rx_s)                    +COMM_FRAME_LEN},
    {   I_COMM_RX_RESET_REQUEST,                                    sizeof(Icomm_Reset_Req_Rx_s)                    +COMM_FRAME_LEN},
};

void icomm_map_Init(void)
{
    memset(&gIcommData_Tx, 0x00, sizeof(Icomm_Protocol_Tx_s));
    memset(&gIcommData_Rx, 0x00, sizeof(Icomm_Protocol_Rx_s));
    memset(&gIcommStatus_Tx, 0x00, sizeof(Icomm_Status_Tx_s));
    memset(&gIcommStatus_Rx, 0x00, sizeof(Icomm_Status_Rx_s));
}

void Reset_Tx_Icomm_Handshake_Status(int cmd)
{
    gIcommStatus_Tx[cmd].flag = 0;
}

void Set_Tx_Icomm_Handshake_Status(int cmd, int val)
{
    gIcommStatus_Tx[cmd].flag = val;
}

int Get_Tx_Icomm_Handshake_Status(int cmd)
{
    return gIcommStatus_Tx[cmd].flag;
}

void Reset_Rx_Icomm_Handshake_Status(int cmd, int timeout_tick)
{
    gIcommStatus_Rx[cmd].flag = COMM_STATE_RESET;
    gIcommStatus_Rx[cmd].timeout_tick = timeout_tick;
    gIcommStatus_Rx[cmd].cur_tick = 0;
}

void Set_Rx_Icomm_Handshake_Status(int cmd, int val)
{
    gIcommStatus_Rx[cmd].flag = val;
}

int Get_Rx_Icomm_Handshake_Status(int cmd)
{
    return gIcommStatus_Rx[cmd].flag;
}
