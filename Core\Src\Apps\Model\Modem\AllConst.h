﻿/**
 * @file      AllConst.h
 * <AUTHOR>
 * @brief     전역으로 사용되는 일반 상수들 정의
 * @version   0.1
 * @date      2022-08-11
 * 
 * @copyright Copyright (c) 2022
 * 
 */


#if !defined(__AllConst_H__)
#define      __AllConst_H__

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "arm_math.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "AscConst.h"
#include "MatConst.h"
#include "KeyConst.h"
#include "SysConst.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "CommonLib.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "SysLib.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "FskModem.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "NavModem.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "Common.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "Coef_10KHz_100Hz_Band_Iir_BP.h"
#include "Coef_3000Hz_Iir_LP.h"
#include "Coef3000HzFirLP.h"
#include "Coef_FS_24KHZ_HP_1700HZ_IIR.h"
#include "Coef_FS_24KHZ_LP_1700HZ_IIR.h"
#include "Coef_FS_24KHZ_BP_1600HZ_1800HZ_IIR.h"
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#define  RUN_SET_FLAG_VAL_NULL         (-1)
#define  RUN_SET_FLAG_VAL_OFF          (+0)
#define  RUN_SET_FLAG_VAL_ON           (+1)
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

#endif   // __AllConst_H__
