﻿/**
 * @file      SysLib.c
 * <AUTHOR>
 * @brief     시스템에서 많이 사용되는 함수 라이브러리
 * @version   0.1
 * @date      2022-08-11
 * 
 * @copyright Copyright (c) 2022
 * 
 */


//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#include "AllConst.h"
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========


//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
/**
 * @brief loop를 돌면서 일정시간 delay한다.
 * 
 * @param dLoopCnt delay 수
 */
void  SysRunDelayLoop(volatile uint32_t dLoopCnt)
{
      while (dLoopCnt)
             dLoopCnt--;
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief mili초간 delay한다. (Cortex-M7 의 SysTick-counter 이용)
 * 
 * @param dDelayMili delay할 시간(mili단위)
 */
void  SysRunDelayMiliSec(uint32_t dDelayMili)
{
      uint32_t dTotalTick;
      uint32_t dPrevCntr;
      uint32_t dCurrCntr;
      uint32_t dDiffCntr;

      if (dDelayMili == 0 || (SysTick->VAL && SysTick_CTRL_ENABLE_Msk) == 0)
          return;

      dTotalTick = (SYS_CLK_CPU_TICK_FREQUENCY / 1000) / 4;
      dDelayMili = dDelayMili                          * 4;

      while (dDelayMili--)
      {
             dPrevCntr = SysTick->VAL;

             while (1)
             {
                    dCurrCntr = SysTick->VAL;

                    if (dPrevCntr != dCurrCntr)
                    {
                        if (dCurrCntr > dPrevCntr)
                            dDiffCntr = (SysTick->LOAD - dCurrCntr) + dPrevCntr;
                        else
                            dDiffCntr = dCurrCntr - dPrevCntr;

                        if (dDiffCntr >= dTotalTick)
                            break;
                    }
             }
      }
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief micro초간 delay한다. (Cortex-M7 의 SysTick-counter 이용)
 * 
 * @param dDelayMicro delay할 시간(micro단위)
 */
void  SysRunDelayMicroSec(uint32_t dDelayMicro)
{
      uint32_t dTotalTick;
      uint32_t dPrevCntr;
      uint32_t dCurrCntr;
      uint32_t dDiffCntr;

      if (dDelayMicro == 0 || (SysTick->VAL && SysTick_CTRL_ENABLE_Msk) == 0)
          return;

      dTotalTick = (SYS_CLK_CPU_TICK_FREQUENCY / 1000000);

      while (dDelayMicro--)
      {
             dPrevCntr = SysTick->VAL;

             while (1)
             {
                    dCurrCntr = SysTick->VAL;

                    if (dPrevCntr != dCurrCntr)
                    {
                        if (dCurrCntr > dPrevCntr)
                            dDiffCntr = (SysTick->LOAD - dCurrCntr) + dPrevCntr;
                        else
                            dDiffCntr = dCurrCntr - dPrevCntr;

                        if (dDiffCntr >= dTotalTick)
                            break;
                    }
             }
      }
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief tick-카운터값을 mili초로 변환한다.
 * 
 * @param dTick      tick-카운터값
 * @return uint32_t  mili초
 */
uint32_t SysCalcTickToMili(uint32_t dTick)
{
      return(SYS_CONVERT_TICK_TO_MILI(dTick));
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief mili초를 tick-카운터값으로 변환한다.
 * 
 * @param dMili     mili초
 * @return uint32_t tick-카운터값
 */
uint32_t SysCalcMiliToTick(uint32_t dMili)
{
      return(SYS_CONVERT_MILI_TO_TICK(dMili));
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief 현재 tick-카운터값과 차이를 계산한다.
 * 
 * @param dTime     특정 시점의 tick-카운터값
 * @return uint32_t 차이값(Tick)
 */
uint32_t SysCalcDiffTimeTick(uint32_t dTick)
{
      uint32_t dHalTick;

      dHalTick = HAL_GetTick();

      if (dHalTick >= dTick)
          dHalTick -= dTick;
      else
          dHalTick += (0xffffffff - dTick);

      return(dHalTick);
}
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
/**
 * @brief 현재 tick-카운터값과 차이를 mili초로 계산한다.
 * 
 * @param dTime     특정 시점의 tick-카운터값
 * @return uint32_t 차이값(mili초)
 */
uint32_t SysCalcDiffTimeMili(uint32_t dTick)
{
      return(SYS_CONVERT_TICK_TO_MILI(SysCalcDiffTimeTick(dTick)));
}
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
