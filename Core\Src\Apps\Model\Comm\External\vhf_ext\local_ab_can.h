/**
 ******************************************************************************
 * @file      local_ab_can.h
 * <AUTHOR>
 * @date      2024-7-13
 * @brief
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2022 Intellian Technologies
 * All rights reserved.
 *
 ******************************************************************************
 */

#ifndef _LOCAL_ALARM_BOX_CAN_H_
#define _LOCAL_ALARM_BOX_CAN_H_

#ifdef __cplusplus
extern "C" {
#endif


#include "stm32h7xx_hal.h"	/* for data type(uint8_t, uint32_t .....) */
#include "TargetCan.h"
#include "ext_can_ctrl.h"

#define LAB_PERIODIC_ID		    (0x220)	// Local Alarm Box periodic id for VHF
#define LAB_EVENT_ID			(0x2A0)	// Local Alarm Box event id for VHF

// define alarm unit event can message's command byte index
#define LABE_CB_IDX_MODE				(0)
#define LABE_CB_IDX_DIST_BTN_STAT	(1)
#define LABE_CB_IDX_CON_REQ			(2)

// Event Type BYTE1(Mode)
#define LABE_MODE_MASK					(0x0F)

// Event Type BYTE2(Distress Button Status)
#define LABE_DIST_BTN_STAT_TICK_MASK	(0xFC)
#define LABE_DIST_BTN_STAT_BTN_MASK	(0x03)

#define LABE_DIST_BTN_STAT_TIME_0S	(0x00)
#define LABE_DIST_BTN_STAT_TIME_1S	(0x01)
#define LABE_DIST_BTN_STAT_TIME_2S	(0x02)
#define LABE_DIST_BTN_STAT_TIME_3S	(0x03)

#define LABE_DIST_BTN_STAT_NONE1		(0x00)
#define LABE_DIST_BTN_STAT_PRESSED	(0x01)
#define LABE_DIST_BTN_STAT_RELEASED	(0x02)
#define LABE_DIST_BTN_STAT_NONE2		(0x03)

// Event Type BYTE3(Connection Request)
#define LABE_CONN_REQ_NONE1				(0x00)
#define LABE_CONN_REQ_REQUEST			(0x01)
#define LABE_CONN_REQ_NONE2				(0x02)
#define LABE_CONN_REQ_NONE3				(0x03)

// define alarm unit periodic can message's command byte index
#define LABP_CB_IDX_MODE				(0)
#define LABP_CB_IDX_MUTE_STAT		(1)
#define LABP_CB_IDX_VER_MAJ			(5)
#define LABP_CB_IDX_VER_MIN			(6)
#define LABP_CB_IDX_VER_PATCH		(7)

// Periodic Type BYTE1(Mode)
#define LABP_MODE_MASK					(0x0F)

// Periodic Type BYTE2(Mute)
#define LABP_MUTE_STATUS_MASK			(0x03)
#define LABP_MUTE_STATUS_UNMUTE		(0x00)
#define LABP_MUTE_STATUS_MUTE			(0x01)

void eCan_lab_send_conn_req_ack(can_msg_s *pMsg);
void eCan_lab_proc_rx_periodic_msg(can_msg_s *pMsg);
void eCan_lab_proc_rx_event_msg(can_msg_s *pMsg);

#ifdef __cplusplus
}
#endif

#endif	/* _LOCAL_ALARM_BOX_CAN_H_ */

