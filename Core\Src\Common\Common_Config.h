/**
******************************************************************************
* @file      System_Config.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_COMMON_SYSTEM_CONFIG_H_
#define SRC_COMMON_SYSTEM_CONFIG_H_

/************************************************************************************
* x(Major) 버전 증가  : 신규 앱 정도의 큰 변화, 이전 버전과 호환되지 않는 API 변경 (count 1)
* y(Minor) 버전 증가  : 이전 버전과 호환되면서 기능의 변경, 추가된 경우 (count 1)
* z(Patch)/Revision 버전 증가  : 버그 수정 시 (count 1)
* rc(Release Candidate) : SW출시 전의 상태이며 동일 버전 Release 표시 

*** 
 * Version type : Internal
 * Desc : 내부용 및 관리용
 * Format : [x].[y].[z]-rc[x]
 * ex) 1.0.0-rc1
 
*** 
 * Version type : Official
 * Desc : Field 에서 보이는 Real Version
 * Format : [x].[y].[z]
 * ex) 1.0.0
***********************************************************************************/

// Receiver board
#define SYSTEM_SW_VER_MAJOR                 0
#define SYSTEM_SW_VER_MINOR				    0
#define SYSTEM_SW_VER_REV				    2
#define SYSTEM_SW_VER_RELEASE_CANDIDATE		0

#define FREQ_SAMPLE_BASE_1_785KHZ 0
#define FREQ_SAMPLE_BAND_PASS_10KHZ 1
#define NAVTEX_FREQ_SAMPLE_MODE FREQ_SAMPLE_BASE_1_785KHZ

#define debug 0
#define release 1
////////////////////////////////////////////////////////////////////
#define SYSTEM_MODE release


////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////
#if SYSTEM_MODE == debug
#define EN_DBG_MSG
// #define NAVTEX_MESSAGE_DEBUG_SEND_MSG
#else

#endif

//#define EN_DBG_COMM_PARA_MSG
#define EN_DBG_COMM_MSG


#ifdef EN_DBG_MSG
#define DEBUG_MSG(...)			printf(__VA_ARGS__);
#define DM(...)			        printf(__VA_ARGS__);
#define DEBUG_BREAK()           __asm("BKPT #0");
#else
#define DEBUG_BREAK()
#define DEBUG_MSG(...)			
#define DM(...)
#endif

#ifdef EN_DBG_COMM_MSG
#define DEBUG_COMM_MSG(...)     printf(__VA_ARGS__);
#else
#define DEBUG_COMM_MSG(...)
#endif

#endif /* SRC_COMMON_SYSTEM_CONFIG_H_ */
