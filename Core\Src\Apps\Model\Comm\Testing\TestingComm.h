/**
******************************************************************************
* @file      TestingComm.h
* <AUTHOR>
* @date      2024-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_DEBUGCOMM_H_
#define SRC_APPS_MODEL_COMM_DEBUGCOMM_H_

#include "Common.h"

typedef enum
{
    T_COMM_TX_TYPE_START = 0x00,

    //T_COMM_TX_TYPE_SYSTEM_STATE = 0x00,

    T_COMM_TX_TYPE_MAX,
}Testing_Comm_Tx_Type_t;

typedef enum
{
    T_COMM_RX_TYPE_START = 0x00,

    T_COMM_RX_TYPE_GPIO_CONTROL = 0x00,
    T_COMM_RX_TYPE_RF_CONTROL = 0x01,

    T_COMM_RX_TYPE_MAX,
}Testing_Comm_Rx_Type_t;

void TestingComm_Task(void);


#endif /* SRC_APPS_MODEL_COMM_DEBUGCOMM_H_ */
