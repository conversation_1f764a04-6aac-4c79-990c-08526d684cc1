///@file     pthread_user.h
///@brief    Pthread user application function code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#include <time.h>
#include <signal.h>

#ifndef PTHREAD_USER_H
#define PTHREAD_USER_H

#include "platform.h"

#if (USE_LINUX == 1)
#include <pthread.h>
#include <unistd.h>
#else
#include "arch/sys_arch.h"
#endif


/// @brief      Pthread information struct
struct pthread_info_s {
    char oper;                          ///< Pthread operation flag
    char pause;                         ///< Pthread pause flag
    int  signo;                         ///< signal number. (for evnet.)
#if (USE_LINUX == 1)
    timer_t 	timer_id;               ///< Thread timer id.
    pthread_t	pth_pid;                ///< Pthread id
    pthread_attr_t	pth_attr;           ///< Pthread attribute
#else
    struct itimerspec   itspec;         ///< timer spec. (for evnet.)
    TimerHandle_t       timer_id;       ///< Thread timer id.
    sys_thread_t        pth_pid;        ///< Pthread id
#endif
    void (*_proc)(void *);              ///< Pthread main process function pointer.
    void (*_stop)(void *);              ///< Pthread stop process function pointer.
    void (*_event)(int, void*, void*);  ///< Pthread event process function pointer.
};

typedef struct pthread_info_s pthread_info_t; ///Pthread inforamtion struct.

struct pth_args_s {
    pthread_info_t* info;
    void* args;       
};

typedef struct pth_args_s pth_args_t;   ///Pthread inforamtion struct.

extern void pthread_stop_func_set(pthread_info_t *info, void (*func_proc)(void*));
extern void pthread_proc_func_set(pthread_info_t *info, void (*func_proc)(void*));
extern void pthread_event_func_set(pthread_info_t *info, void (*func_proc)(int, void*, void*));
extern void pth_proc_thread_exit(pth_args_t *arg);
extern void pth_proc_thread_init(pthread_info_t *info, void *arg, void (*func_stop)(void*),
                                void (*func_proc)(void*), void (*func_event)(int, void*, void*));
extern void pth_proc_resume(pthread_info_t *info);
extern void pth_proc_pause(pthread_info_t *info);
extern void pth_event_signo_set(pthread_info_t *info, int signo);
extern int  pth_proc_tspec_set(pthread_info_t *info, int init_sec, int init_nsec,
                                int interval_sec, int interval_nsec);
extern void pth_event_set(pthread_info_t *info, void* arg);
extern void pth_event_reset(pthread_info_t *info, void* arg);
extern void pth_event_clear(pthread_info_t *info);

#endif /* PTHREAD_USER_H */