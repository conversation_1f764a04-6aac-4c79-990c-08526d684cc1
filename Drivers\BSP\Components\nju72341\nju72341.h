/**
******************************************************************************
* @file      nju72341.h
* <AUTHOR>
* @date      2023-4-7
* @brief     volume control driver for JRC NJU72341
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef _NJU_72341_H_
#define _NJU_72341_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32h7xx_hal.h"

#define NJU72341_DEV_ADDR			0x88

// The auto increment function cycles the select address as follows.
// 00H->01H->02H->00H
//	Control register default value is all “0”.
#define NJU72341_REG_VOL_A			0x00	// D7 ~ D4 (Don't care), D3~D2(VOLUME 2A), D1~D0(VOLUME 1A)
													// VOLUME1A/2A: 0 to +9dB (3dB/Step)
#define NJU72341_REG_VOL1B			0x01	// D7(ZERO1), D6~D0(VOLUME 1B)
#define NJU72341_REG_VOL2B			0x02	// D7(ZERO2), D6~D0(VOLUME 2B)
													// ZERO 1/2: Ch1/Ch2 Zero Cross Detection ON/OFF setting(“0”: OFF “1”: ON)
													// VOLUME1B/2B: Volume 1B/2B setting 0 to -95 dB (1dB/Step) / Mute

#define NJU72341_VOL1A_MASK		0x03
#define NJU72341_VOL1A_SHIFT		0

#define NJU72341_VOL2A_MASK		0x0C
#define NJU72341_VOL2A_SHIFT		2

#define NJU72341_ZERO1_MASK		0x80
#define NJU72341_ZERO1_SHIFT		7

#define NJU72341_ZERO2_MASK		0x80
#define NJU72341_ZERO2_SHIFT		7

#define NJU72341_VOL1B_MASK		0x7F
#define NJU72341_VOL1B_SHIFT		0

#define NJU72341_VOL2B_MASK		0x7F
#define NJU72341_VOL2B_SHIFT		0
	
#define NJU72341_ON					0x01
#define NJU72341_OFF					0x00

#define NJU72341_MIN_VOL1A			0x00
#define NJU72341_MAX_VOL1A			0x03

#define NJU72341_MIN_VOL2A			0x00
#define NJU72341_MAX_VOL2A			0x03

#define NJU72341_MIN_VOL1B			0x18	// -95 dB
#define NJU72341_MAX_VOL1B			0x77	// 0 dB

#define NJU72341_MIN_VOL2B			0x18	// -95 dB
#define NJU72341_MAX_VOL2B			0x77	// 0 dB

#define NJU72341_I2C_WAIT			100

#define NJU72341_OK					0
#define NJU72341_ERROR				-1

typedef struct {
	uint8_t volA_reg;
	uint8_t vol1B_reg;
	uint8_t vol2B_reg;
}NJU72341_REG;

typedef struct{
	I2C_HandleTypeDef *phI2c;		// I2C Handler
	GPIO_TypeDef *pMutePort;		// Mute Control Port	
	int			 nMutePin;			// Mute Control Pin
	NJU72341_REG reg;					// Register value
}NJU72341_Context;

void NJU72341_init(I2C_HandleTypeDef *pHI2C, GPIO_TypeDef *pMutePort, int nMutePin);

uint8_t NJU72341_get_vol1A();
uint8_t NJU72341_get_vol2A();
uint8_t NJU72341_get_zero1();
uint8_t NJU72341_get_zero2();
uint8_t NJU72341_get_vol1B();
uint8_t NJU72341_get_vol2B();

int NJU72341_set_vol1A(uint8_t vol);
int NJU72341_set_vol2A(uint8_t vol);

int NJU72341_set_zero1(uint8_t bOnOff);
int NJU72341_set_zero2(uint8_t bOnOff);
int NJU72341_set_vol1B(uint8_t nVol);
int NJU72341_set_vol2B(uint8_t nVol);
void NJU72341_set_vol_mute_control(uint8_t bOnOff);

#ifdef __cplusplus
}
#endif

#endif	/* _NJU_72341_H_ */

