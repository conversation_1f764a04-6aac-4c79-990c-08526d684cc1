/**
******************************************************************************
* @file      ab_can.h
* <AUTHOR>
* @date      2024-7-13
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2022 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/


#ifndef _ALARM_BOX_CAN_H_
#define _ALARM_BOX_CAN_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32h7xx_hal.h"	/* for data type(uint8_t, uint32_t .....) */
#include "TargetCan.h"
#include "ext_can_ctrl.h"

#define AB_PERIODIC_ID		(0x200)	// Alarm Unit periodic id for VHF
#define AB_EVENT_ID			(0x280)	// Alarm Unit event id for VHF

// define alarm box event can message's command byte index
#define ABE_CB_IDX_MODE				(0)
#define ABE_CB_IDX_DIST_BTN_STAT	(1)
#define ABE_CB_IDX_CON_REQ			(2)

// Event Type BYTE1(Mode)
#define ABE_MODE_MASK				(0x0F)

// Event Type BYTE2(Distress Button Status)
#define ABE_DIST_BTN_STAT_TICK_MASK	(0xFC)
#define ABE_DIST_BTN_STAT_BTN_MASK	(0x03)

#define ABE_DIST_BTN_STAT_TIME_0S	(0x00)
#define ABE_DIST_BTN_STAT_TIME_1S	(0x01)
#define ABE_DIST_BTN_STAT_TIME_2S	(0x02)
#define ABE_DIST_BTN_STAT_TIME_3S	(0x03)

#define ABE_DIST_BTN_STAT_NONE1		(0x00)
#define ABE_DIST_BTN_STAT_PRESSED	(0x01)
#define ABE_DIST_BTN_STAT_RELEASED	(0x02)
#define ABE_DIST_BTN_STAT_NONE2		(0x03)

// Event Type BYTE3(Connection Request)
#define ABE_CONN_REQ_NONE1				(0x00)
#define ABE_CONN_REQ_REQUEST			(0x01)
#define ABE_CONN_REQ_NONE2				(0x02)
#define ABE_CONN_REQ_NONE3				(0x03)

// define alarm box periodic can message's command byte index
#define ABP_CB_IDX_MODE				(0)
#define ABP_CB_IDX_MUTE_STAT		(1)
#define ABP_CB_IDX_VER_MAJ			(5)
#define ABP_CB_IDX_VER_MIN			(6)
#define ABP_CB_IDX_VER_PATCH		(7)

// Periodic Type BYTE1(Mode)
#define ABP_MODE_MASK				(0x0F)

// Periodic Type BYTE2(Mute)
#define ABP_MUTE_STATUS_MASK		(0x03)
#define ABP_MUTE_STATUS_UNMUTE		(0x00)
#define ABP_MUTE_STATUS_MUTE		(0x01)

void eCan_ab_send_conn_req_ack(can_msg_s *pMsg);

void eCan_ab_proc_rx_periodic_msg(can_msg_s *pMsg, uint32_t id);
void eCan_ab_proc_rx_event_msg(can_msg_s *pMsg, uint32_t id);

#ifdef __cplusplus
}
#endif

#endif	/* _ALARM_BOX_CAN_H_ */

