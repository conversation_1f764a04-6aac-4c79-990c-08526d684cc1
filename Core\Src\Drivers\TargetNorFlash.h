/**
******************************************************************************
* @file      TargetNorFlash.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef _TARGET_NOR_FLASH_H_
#define _TARGET_NOR_FLASH_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32h7xx_hal.h"
#include "mt25ql128aba.h"
#include "TargetConfig.h"

#define TBD_QSPI_NOR_FLASH_BASE_ADDR	QSPI_BASE

#define NOR_FLASH_NAVTEX_MSG_BLOCK_SIZE 0x2000

#define NOR_FLASH_SUB_SECTOR_SIZE 0x1000

void TBD_nor_flash_get_info();
int32_t TBD_nor_reset_memory(QSPI_HandleTypeDef *pHandle);
int32_t TBD_nor_dummy_cycle_cfg(QSPI_HandleTypeDef *pHandle);
int32_t TBD_nor_erase_chip(QSPI_HandleTypeDef *pHandle);

int TBD_nor_flash_nav_msg_write(uint32_t Block, uint8_t *pInfoData, uint32_t InfoSize, uint8_t *pMsgData, uint32_t MsgSize);
int TBD_nor_flash_nav_msg_read(uint32_t Block, uint8_t *pInfoData, uint32_t InfoSize, uint8_t *pMsgData, uint32_t MsgSize);

void TBD_init_nor_flash();

#ifdef __cplusplus
}
#endif

#endif	/* _TARGET_NOR_FLASH_H_ */