﻿/**
 * @file      MatConst.h
 * <AUTHOR>
 * @brief     전역으로 사용되는 수학 상수들 정의
 * @version   0.1
 * @date      2022-08-11
 * 
 * @copyright Copyright (c) 2022
 * 
 */


#if !defined(__MatConst_H__)
#define      __MatConst_H__

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#define  MAT_VAL_PI_D                  (3.141592653589793238460)
#define  MAT_VAL_PI_F                  (3.14159265358979323846f)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  MAT_VAL_PI_MUL_2_D            (MAT_VAL_PI_D * 2.00)
#define  MAT_VAL_PI_MUL_2_F            (MAT_VAL_PI_F * 2.0f)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  MAT_VAL_PI_DIV_2_D            (MAT_VAL_PI_D / 2.00)
#define  MAT_VAL_PI_DIV_2_F            (MAT_VAL_PI_F / 2.0f)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  MAT_DEG_TO_RAD_D              (MAT_VAL_PI_D / 180.00)
#define  MAT_DEG_TO_RAD_F              (MAT_VAL_PI_F / 180.0f)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  MAT_RAD_TO_DEG_D              (180.00 / MAT_VAL_PI_D)
#define  MAT_RAD_TO_DEG_F              (180.0f / MAT_VAL_PI_F)
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

#endif   // __MatConst_H__
