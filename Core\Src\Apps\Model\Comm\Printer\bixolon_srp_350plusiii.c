/**
******************************************************************************
* @file      bixolon_srp_350plusiii.c
* <AUTHOR>
* @date      2025-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

//#include "bixolon.h"
#include "bixolon_srp_350plusiii.h"
#include "model.h"

#define UDP_DATA_LEN          PROTOCOL_LEN

#define PRINTER_MAX_COUNT     4
#define UDP_SRC_PORT          48780
#define UDP_DST_PORT          48781
#define TIMEOUT_MS            500

static const uint8_t g_cmd_find[] = {'F', 'I', 'N', 'D'};
static const uint8_t g_cmd_imin[] = {'I', 'M', 'I', 'N'};
static const uint8_t g_cmd_sett[] = {'S', 'E', 'T', 'T'};
static const uint8_t g_cmd_setc[] = {'S', 'E', 'T', 'C'};

static bixolon_eth_printer_t g_find_printer[PRINTER_MAX_COUNT];
static bixolon_eth_printer_t g_set_result;

uint16_t packet_count = 0;
volatile uint8_t receive_complete = 0;
volatile uint8_t set_complete = 0;
osTimerId timeout_timer_id;

static void timeout_callback(void const *argument);
static void udp_recv_callback(void *arg, struct udp_pcb *upcb, struct pbuf *p, const ip_addr_t *addr, u16_t port);
static void boxolon_eth_debug_out_printer_info(bixolon_eth_printer_t printer);

static void timeout_callback(void const *argument)
{
    receive_complete = 1; //타임아웃 발생시 종료 플래그를 설정
    if ((struct pbuf *)argument != NULL)
    {
    	pbuf_free((struct pbuf *)argument);
    }
}

static void udp_recv_callback(void *arg, struct udp_pcb *upcb, struct pbuf *p, const ip_addr_t *addr, u16_t port)
{
	if (p != NULL && p->len == UDP_DATA_LEN && packet_count < PRINTER_MAX_COUNT)
	{
		if (memcmp(p->payload, g_cmd_imin, CMD_LEN * sizeof(uint8_t)) == 0) // CMD IMIN
		{
			memcpy(g_find_printer[packet_count].data, p->payload, UDP_DATA_LEN);

			DM("=================================\r\n");
			DM("Printer %d\r\n", packet_count + 1);
			boxolon_eth_debug_out_printer_info(g_find_printer[packet_count]);

			packet_count++;
			osTimerStart(timeout_timer_id, TIMEOUT_MS); //새로운 패킷이 들어올때마다 타이머 재시작
		}
		else if (memcmp(p->payload, g_cmd_setc, CMD_LEN * sizeof(uint8_t)) == 0) //CMD SETC
		{
			memcpy(g_set_result.data, p->payload, UDP_DATA_LEN);
			DM("=================================\r\n");
			DM("Printer Config Applied\r\n");

			boxolon_eth_debug_out_printer_info(g_set_result);
			receive_complete = 1;
		    set_complete = 1;
			osTimerStop(timeout_timer_id);
		}
		pbuf_free(p);
	}
	else if (p != NULL)
    {
        pbuf_free(p); //잘못된 패킷은 해제
    }
}

static void boxolon_eth_debug_out_printer_info(bixolon_eth_printer_t printer)
{
	DM("=================================\r\n");
	DM("MAC Addr : %02X:%02X:%02X:%02X:%02X:%02X\r\n",
			printer.property.mac[0],
			printer.property.mac[1],
			printer.property.mac[2],
			printer.property.mac[3],
			printer.property.mac[4],
			printer.property.mac[5]);

	DM("Use DHCP : %s\r\n", printer.property.use_dhcp ? "YES" : "NO");

	DM("IP Addr : %d.%d.%d.%d\r\n",
			printer.property.ip[0],
			printer.property.ip[1],
			printer.property.ip[2],
			printer.property.ip[3]);

	DM("Subnet : %d.%d.%d.%d\r\n",
			printer.property.subnet[0],
			printer.property.subnet[1],
			printer.property.subnet[2],
			printer.property.subnet[3]);

	DM("GW Addr : %d.%d.%d.%d\r\n",
			printer.property.gateway[0],
			printer.property.gateway[1],
			printer.property.gateway[2],
			printer.property.gateway[3]);

	DM("COMM Port : %d\r\n", (printer.property.port_H << 8) | printer.property.port_L);
	DM("COMM Timeout : %d\r\n",(printer.property.timeout_H << 8) | printer.property.timeout_L);
	DM("=================================\r\n");
}

int bixolon_eth_find_printer(void)
{
    struct udp_pcb *upcb;
    ip_addr_t broadcast_addr;
    ip_addr_t any_addr;
    struct pbuf *p = NULL;
    err_t err;

    packet_count = 0;

    // 수신 Timeout 타이머 생성
    osTimerDef(timeout_timer, timeout_callback);
    timeout_timer_id = osTimerCreate(osTimer(timeout_timer), osTimerOnce, (void *)p);

    if (timeout_timer_id == NULL)
    {
        return -1; //타이머 생성 실패
    }

    // udp pcb 생성
    upcb = udp_new();
    if (upcb == NULL)
    {
        osTimerDelete(timeout_timer_id);
        return -1; //PCB 생성 실패
    }

    // 송수신 포트 바인딩
    IP4_ADDR(&any_addr, IPADDR_ANY, IPADDR_ANY, IPADDR_ANY, IPADDR_ANY);
    err = udp_bind(upcb, &any_addr, UDP_SRC_PORT);
    if (err != ERR_OK)
    {
        udp_remove(upcb);
        osTimerDelete(timeout_timer_id);
        return -1; //바인딩 실패
    }

    // 송신 버퍼 할당
    p = pbuf_alloc(PBUF_TRANSPORT, CMD_LEN, PBUF_RAM);
    if (p == NULL)
    {
        udp_remove(upcb);
        return -1;
    }

    memcpy(p->payload, &g_cmd_find, CMD_LEN);

    // 검색 명령 송신 (FIND)
    IP4_ADDR(&broadcast_addr, 255, 255, 255, 255);
    err = udp_sendto(upcb, p, &broadcast_addr, UDP_DST_PORT);
    if (err != ERR_OK)
    {
        pbuf_free(p);
        udp_remove(upcb);
        return -1;
    }

    //수신 시작
    udp_recv(upcb, udp_recv_callback, NULL);
    osTimerStart(timeout_timer_id, TIMEOUT_MS); //타임아웃 시작

    while (receive_complete == 0)
    {
        osDelay(10); // 적절한 딜레이
    }

    udp_remove(upcb);
    osTimerDelete(timeout_timer_id);

    receive_complete = 0;

	return packet_count;
}

bixolon_eth_set_result_t bixolon_eth_set_config(bixolon_eth_printer_t printer)
{
    struct udp_pcb *upcb;
    ip_addr_t broadcast_addr;
    ip_addr_t any_addr;
    struct pbuf *p;
    err_t err;

    set_complete = 0;

    // 수신 Timeout 타이머 생성
    osTimerDef(timeout_timer, timeout_callback);
    timeout_timer_id = osTimerCreate(osTimer(timeout_timer), osTimerOnce, NULL);

    if (timeout_timer_id == NULL)
    {
        return BIXOLON_ETH_SET_ERROR; //타이머 생성 실패
    }

    // udp pcb 생성
    upcb = udp_new();
    if (upcb == NULL)
    {
        osTimerDelete(timeout_timer_id);
        return BIXOLON_ETH_SET_ERROR; //PCB 생성 실패
    }

    // 송수신 포트 바인딩
    IP4_ADDR(&any_addr, IPADDR_ANY, IPADDR_ANY, IPADDR_ANY, IPADDR_ANY);
    err = udp_bind(upcb, &any_addr, UDP_SRC_PORT);
    if (err != ERR_OK)
    {
        udp_remove(upcb);
        osTimerDelete(timeout_timer_id);
        return BIXOLON_ETH_SET_ERROR; //바인딩 실패
    }

    // 송신 버퍼 할당
    p = pbuf_alloc(PBUF_TRANSPORT, PROTOCOL_LEN, PBUF_RAM);
    if (p == NULL)
    {
        udp_remove(upcb);
        return BIXOLON_ETH_SET_ERROR;
    }

    memcpy(p->payload, &printer, PROTOCOL_LEN);
    // 설정 명령 지정(SETT)
    memcpy(p->payload, &g_cmd_sett, CMD_LEN);

    // 설정 명령 송신 (SETT)
    IP4_ADDR(&broadcast_addr, 255, 255, 255, 255);
    err = udp_sendto(upcb, p, &broadcast_addr, UDP_DST_PORT);
    if (err != ERR_OK)
    {
        pbuf_free(p);
        udp_remove(upcb);
        return BIXOLON_ETH_SET_ERROR;
    }

    //수신 시작
    udp_recv(upcb, udp_recv_callback, NULL);
    osTimerStart(timeout_timer_id, TIMEOUT_MS); //타임아웃 시작

    while (receive_complete == 0)
    {
        osDelay(10);
    }

    if (p != NULL)
    {
    	pbuf_free(p);
    }

    udp_remove(upcb);
    osTimerDelete(timeout_timer_id);

    receive_complete = 0;

    if (set_complete) // 설정 완료 수신시 (SETC)
    	return BIXOLON_ETH_SET_OK;
    else
    	return BIXOLON_ETH_SET_ERROR;
}
