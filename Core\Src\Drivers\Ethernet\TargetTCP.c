/**
******************************************************************************
* @file      TargetTCP.c
* <AUTHOR>
* @date      2024-6-10
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetEthernet.h"

static tcp_ip_port_pcb_s Tbd_TcpConf[TCP_TYPE_MAX];

void Tcp_Ip_Client_Config(void const *argument)
{
	//struct netif *netif = (struct netif *)argument;

	IP4_ADDR(&Tbd_TcpConf[TCP_CLIENT_PRINTER].remote_ip, 10,1,140,250);
	Tbd_TcpConf[TCP_CLIENT_PRINTER].remote_port = 9100;

	// IP4_ADDR(&Tbd_TcpConf[TCP_CLIENT_PRINTER].remote_ip, 10,1,140,56);
	// Tbd_TcpConf[TCP_CLIENT_PRINTER].remote_port = 8040;
}

void Tcp_Ip_Server_Config(void const *argument)
{
	//struct netif *netif = (struct netif *)argument;
}

void Tcp_Client_Error_Callback(void *arg, err_t err)
{
	tcp_ip_port_pcb_s *p = (tcp_ip_port_pcb_s *)arg;

	p->err_cb_state = err;
}

err_t Tcp_Client_Connected_Callback(void *arg, struct tcp_pcb *tpcb, err_t err)
{
	tcp_ip_port_pcb_s *p = (tcp_ip_port_pcb_s *)arg;

	if(err != ERR_OK)
	{
		return err;
	}

	p->cur_state = TCP_CLIENT_STATE_CONNECTED;

	/* Set priority for the client pcb */
	tcp_setprio(tpcb, TCP_PRIO_NORMAL);

	/* pass newly allocated es structure as argument to tpcb */
	tcp_arg(tpcb, p);

	/* initialize LwIP tcp_sent callback function */
	tcp_sent(tpcb, Tcp_Client_Sent_Callback);

	/* initialize LwIP tcp_recv callback function */ 
	tcp_recv(tpcb, Tcp_Client_Recv_Callback);

	/* Error Callback */ 
	tcp_err(tpcb, Tcp_Client_Error_Callback);

	/* initialize LwIP tcp_poll callback function */
	tcp_poll(tpcb, Tcp_Client_Poll_Callback, 0);

	return ERR_OK;
}

void Tcp_Client_Connect(tcp_ip_port_pcb_t type)
{
	err_t err;
	if(Tbd_TcpConf[type].pcb != NULL)
	{
		Tcp_Client_Disconnect(type);
	}

	Tbd_TcpConf[type].pcb = tcp_new();
	if(Tbd_TcpConf[type].pcb != NULL)
	{
		tcp_arg(Tbd_TcpConf[type].pcb, &Tbd_TcpConf[type]);
		err = tcp_connect(Tbd_TcpConf[type].pcb, &Tbd_TcpConf[type].remote_ip, Tbd_TcpConf[type].remote_port, Tcp_Client_Connected_Callback);
		if(err == ERR_OK)
		{
			DEBUG_MSG("[%s] Tcp Client Connect Success Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																				(Tbd_TcpConf[type].remote_ip.addr & 0xFF),
																				((Tbd_TcpConf[type].remote_ip.addr >> 8) & 0xFF),
																				((Tbd_TcpConf[type].remote_ip.addr >> 16) & 0xFF),
																				((Tbd_TcpConf[type].remote_ip.addr >> 24) & 0xFF),
																				Tbd_TcpConf[type].remote_port);
		}
		else
		{
            DEBUG_MSG("[%s] Tcp Client Connect Error Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																				(Tbd_TcpConf[type].remote_ip.addr & 0xFF),
																				((Tbd_TcpConf[type].remote_ip.addr >> 8) & 0xFF),
																				((Tbd_TcpConf[type].remote_ip.addr >> 16) & 0xFF),
																				((Tbd_TcpConf[type].remote_ip.addr >> 24) & 0xFF),
																				Tbd_TcpConf[type].remote_port);
            Tcp_Client_Disconnect(type);
		}
	}
	else
	{
        DEBUG_MSG("[%s] Tcp Client Connect Error Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																				(Tbd_TcpConf[type].remote_ip.addr & 0xFF),
																				((Tbd_TcpConf[type].remote_ip.addr >> 8) & 0xFF),
																				((Tbd_TcpConf[type].remote_ip.addr >> 16) & 0xFF),
																				((Tbd_TcpConf[type].remote_ip.addr >> 24) & 0xFF),
																				Tbd_TcpConf[type].remote_port);
        Tcp_Client_Disconnect(type);
	}
}

void Tcp_Client_Disconnect(tcp_ip_port_pcb_t type)
{
    tcp_arg(Tbd_TcpConf[type].pcb, NULL);
    tcp_sent(Tbd_TcpConf[type].pcb, NULL);
    tcp_recv(Tbd_TcpConf[type].pcb, NULL);
    tcp_err(Tbd_TcpConf[type].pcb, NULL);
    tcp_poll(Tbd_TcpConf[type].pcb, NULL, 0);

    tcp_close(Tbd_TcpConf[type].pcb);    //close connection
}

void Tcp_Client_Send_Str(tcp_ip_port_pcb_t type, char *pStr)
{
	err_t wr_err = ERR_OK;
	struct pbuf *p_tx; 
	u16 len = strlen(pStr);

	if(Tbd_TcpConf[type].cur_state != TCP_CLIENT_STATE_CONNECTED)
	{
        Tcp_Client_Disconnect(type);
        Tcp_Client_Connect(type);
		DEBUG_MSG("[%s] Tcp Client Send Error Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																					(Tbd_TcpConf[type].remote_ip.addr & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 8) & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 16) & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 24) & 0xFF),
																					Tbd_TcpConf[type].remote_port);
	}

	p_tx = pbuf_alloc(PBUF_TRANSPORT, len, PBUF_POOL);
	if(p_tx != NULL)
	{
		pbuf_take(p_tx, (char*)pStr, len);

		wr_err = tcp_write(Tbd_TcpConf[type].pcb, p_tx->payload, p_tx->len, 1);
		if(wr_err != ERR_OK)
		{
            DEBUG_MSG("[%s] Tcp Client Send Error Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																					(Tbd_TcpConf[type].remote_ip.addr & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 8) & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 16) & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 24) & 0xFF),
																					Tbd_TcpConf[type].remote_port);
            Tcp_Client_Disconnect(type);
            Tcp_Client_Connect(type);
		}

		pbuf_free(p_tx);
	}
}

void Tcp_Client_Send_Data(tcp_ip_port_pcb_t type, u8 *pData, u16 len)
{
	err_t wr_err = ERR_OK;
	struct pbuf *p_tx; 

    if(Tbd_TcpConf[type].cur_state != TCP_CLIENT_STATE_CONNECTED)
	{
        Tcp_Client_Disconnect(type);
        Tcp_Client_Connect(type);
		DEBUG_MSG("[%s] Tcp Client Send Error Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																					(Tbd_TcpConf[type].remote_ip.addr & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 8) & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 16) & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 24) & 0xFF),
																					Tbd_TcpConf[type].remote_port);
	}

    p_tx = pbuf_alloc(PBUF_TRANSPORT, len, PBUF_POOL);
	if(p_tx != NULL)
	{
		pbuf_take(p_tx, (char*)pData, len);

		wr_err = tcp_write(Tbd_TcpConf[type].pcb, p_tx->payload, p_tx->len, 1);
		if(wr_err != ERR_OK)
		{
            DEBUG_MSG("[%s] Tcp Client Send Error Ip = %lu.%lu.%lu.%lu Port = %d\r\n", __FUNCTION__,
																					(Tbd_TcpConf[type].remote_ip.addr & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 8) & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 16) & 0xFF),
																					((Tbd_TcpConf[type].remote_ip.addr >> 24) & 0xFF),
																					Tbd_TcpConf[type].remote_port);
            Tcp_Client_Disconnect(type);
            Tcp_Client_Connect(type);
		}

		pbuf_free(p_tx);
	}
}

err_t Tcp_Client_Poll_Callback(void *arg, struct tcp_pcb *tpcb)
{
	return ERR_OK;
}

err_t Tcp_Client_Sent_Callback(void *arg, struct tcp_pcb *tpcb, u16_t len)
{
	return ERR_OK;
}

err_t Tcp_Client_Recv_Callback(void *arg, struct tcp_pcb *tpcb, struct pbuf *p, err_t err)
{ 
	tcp_ip_port_pcb_s *pVal = (tcp_ip_port_pcb_s *)arg;

	if(p == NULL)
	{
		pVal->cur_state = TCP_CLIENT_STATE_CLOSING;

		tcp_recv(tpcb, NULL);
  		tcp_sent(tpcb, NULL);
  		tcp_poll(tpcb, NULL,0);
		tcp_close(tpcb);

		return ERR_OK;
	}
	else if(err != ERR_OK)
	{
		pbuf_free(p);

		tcp_recv(tpcb, NULL);
		tcp_sent(tpcb, NULL);
		tcp_poll(tpcb, NULL,0);
		tcp_close(tpcb);

		return err;
	}
	else if(pVal->cur_state == TCP_CLIENT_STATE_CONNECTED)
	{
		/* Acknowledge data reception */
		tcp_recved(tpcb, p->tot_len); 

		/* free pbuf and do nothing */
		pbuf_free(p);
		return ERR_OK;
	}
	/* data received when connection already closed */
	else 
	{
		/* Acknowledge data reception */
		tcp_recved(tpcb, p->tot_len);

		/* free pbuf and do nothing */
    	pbuf_free(p);

		return ERR_OK;
	}
}

void tcp_test_thread(void const * argument)
{
    for(;;)
    {
        //Tcp_Client_Send_Str(TCP_CLIENT_PRINTER, "abcdef");
        osDelay(1000);
    }
}

void TargetTCP_Init(void const *argument)
{
    struct netif *netif = (struct netif *)argument;

    Tcp_Ip_Client_Config(netif);
	Tcp_Ip_Server_Config(netif);
	Tcp_Client_Connect(TCP_CLIENT_PRINTER);

    osThreadDef(_tcp_test_thread, tcp_test_thread, osPriorityNormal, 0, configMINIMAL_STACK_SIZE * 5);
  	osThreadCreate (osThread(_tcp_test_thread), NULL);
}
