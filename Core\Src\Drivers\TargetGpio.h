/**
******************************************************************************
* @file      TargetGpio.h
* <AUTHOR>
* @date      2024-05-10
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_DRIVERS_TARGETGPIO_H_
#define SRC_DRIVERS_TARGETGPIO_H_

typedef enum
{    
    RF_RELAY_CH_NONE = 0,
    RF_RELAY_CH_490KHZ = RF_RELAY_CH_NONE,
    RF_RELAY_CH_4209_5KHZ,
    RF_RELAY_CH_MAX,
} RF_Relay_Ch_t;

typedef struct
{
    int rf_relay;
    int ant_power;
    int alarm_relay;
    int freq_4m_gen;
} DrvGpioComponent_s;

void TargetGpio_Init(void);
void TargetGpio_Task(void);

void Set_Gpio_RF_Relay(int onoff);
int Get_Gpio_RF_Relay(void);

void Set_Gpio_Ant_Power(int onoff);
int Get_Gpio_Ant_Power(void);

void Set_Gpio_Alarm_Relay(int onoff);
int Get_Gpio_Alarm_Relay(void);

void Set_Gpio_4M_Freq_Generator(int onoff);
int Get_Gpio_4M_Freq_Generator(void);

extern DrvGpioComponent_s gDrvGpio;

#endif /* SRC_DRIVERS_TARGETGPIO_H_ */
