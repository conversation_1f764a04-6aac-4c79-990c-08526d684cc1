/**
******************************************************************************
* @file      TargetConfig.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef _TARGET_CONFIG_H_
#define _TARGET_CONFIG_H_

#ifdef __cplusplus
extern "C" {
#endif
#include <stdio.h>

// CubeMX : (<PERSON><PERSON>) Change _Min_Heap_Size ( 0x200 -> 0x400)
// CubeMX : (Linker Script) Change _Min_Statck_Size ( 0x400 -> 0x800)
// CubeMX : Set FreeRtos Heap Size 15KB -> 30KB
// CubeMX : (Linker Script) Add section .lwip_sec
// CubeMX : Add 2 Region of mpu for lwip(refer to cubeMx->CORTEX_M7)
// ethernetif.c : declare memp_memory_RX_POOL_base

// Define target board type
#define BDT_STM32H743I_EVAL2	        0	// STM32H743I Evalutaion board version 2
#define BDT_MFHF_ES1                    1	// MFHF Control Unit - ES1
//#define BDT_NAVTEX_BD_MFHF_KOMARINE		1	// MFHF Control Unit - ES1
		
#define TBD_TYPE              BDT_MFHF_ES1

#ifdef __cplusplus
}
#endif

#endif	/* _TARGET_CONFIG_H_ */

