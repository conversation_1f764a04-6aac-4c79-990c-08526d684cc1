/**
******************************************************************************
* @file      bixolon_srp_350plusiii.h
* <AUTHOR>
* @date      2025-05-27
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_COMM_PRINTER_BIXOLON_SRP_350PLUSIII_H_
#define SRC_APPS_MODEL_COMM_PRINTER_BIXOLON_SRP_350PLUSIII_H_

#define CMD_LEN            4
#define MAC_ADDR_LEN       6
#define IP_ADDR_LEN        4
#define PROTOCOL_LEN       sizeof(bixolon_eth_prop_t)

#include <stdbool.h>
#include "Lwip.h"

#include "lwip/api.h"
#include "lwip/udp.h"
#include "lwip/ip_addr.h"

typedef enum 
{
	BIXOLON_ETH_SET_OK,
	BIXOLON_ETH_SET_ERROR,
	BIXOLON_ETH_SET_MAX
} bixolon_eth_set_result_t;

typedef enum
{
	BIXOLON_INF_ETH,
	BIXOLON_INF_SERIAL,
	BIXOLON_INF_MAX
} bixolon_inf_t;

typedef enum
{
	BIXOLON_RESULT_OK,
	BIXOLON_RESULT_ETH_ERROR,
	BIXOLON_RESULT_SERIAL_ERROR,
	BIXOLON_RESULT_MAX
} bixolon_result_t;

typedef struct 
{
	uint8_t cmd[CMD_LEN];
	uint8_t mac[MAC_ADDR_LEN];
	uint8_t ip[IP_ADDR_LEN];
	uint8_t subnet[IP_ADDR_LEN];
	uint8_t gateway[IP_ADDR_LEN];
	uint8_t port_H;
	uint8_t port_L;
	uint8_t unknown[3];
	uint8_t use_dhcp;
	uint8_t timeout_H;
	uint8_t timeout_L;
} bixolon_eth_prop_t;

typedef union {
	bixolon_eth_prop_t property;
	uint8_t data[PROTOCOL_LEN];
} bixolon_eth_printer_t;

int bixolon_eth_find_printer(void);
bixolon_eth_set_result_t bixolon_eth_set_config(bixolon_eth_printer_t printer);

void bixolon_init();
bixolon_result_t bixolon_print_text(bixolon_inf_t inf, char *text);



#endif /* SRC_APPS_MODEL_COMM_PRINTER_BIXOLON_SRP_350PLUSIII_H_ */
