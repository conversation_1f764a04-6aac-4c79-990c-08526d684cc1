/**
 * @file      Coef3000HzIirLP.h
 * <AUTHOR>
 * @brief     VHF Main Function
 * @version   0.1
 * @date      2022-08-31
 *
 * @copyright Copyright (c) 2022
 *
 */


#if !defined(__Coef3000HzIirLP_H__)
#define      __Coef3000HzIirLP_H__

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "AllConst.h"
#include "CommonLib.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  FLT_3000HZ_IIR_LP_SIZE         (10)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

#if defined(__cplusplus)
extern "C" {
#endif   // __cplusplus


//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
extern tBaseIIR    G_xIirTx3000HzLP;
extern tBaseIIR    G_xIirRx3000HzLP;
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

#if defined(__cplusplus)
           }
#endif   // __cplusplus


#endif   // __Coef3000HzIirLP_H__
