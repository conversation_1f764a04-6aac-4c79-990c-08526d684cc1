/**
******************************************************************************
* @file      TargetISR.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetBoard.h"
#include "model.h"

void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    // static u8 tog = 0;
    // u8 SysMode = g_hSysStatus.m_pStat->mode.system;

    // if(GPIO_Pin == EXISR_SAMPLE_FREQ_Pin)
    // {
    //     if(SysMode == SYS_BOOT_RUN)
    //     {
    //         FSK_Msg_Tx_Run();
    //         FSK_Msg_Rx_Run();

    //         tog ^= 0x01;
    //         HAL_GPIO_WritePin(DO_FSK_SAMPLE_FREQ_DETECT_GPIO_Port, DO_FSK_SAMPLE_FREQ_DETECT_Pin, tog);

    //         gDrvAdc.Adc1_DiffCnt = gDrvAdc.Adc1_IsrCnt;
    //         gDrvAdc.Adc1_IsrCnt = 0;

    //         gDrvAdc.Adc3_DiffCnt = gDrvAdc.Adc3_IsrCnt;
    //         gDrvAdc.Adc3_IsrCnt = 0;
    //     }
    // }
}
