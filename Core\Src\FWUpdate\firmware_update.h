/*
 * firmware_update.h
 *
 *  Created on: Apr 1, 2025
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#ifndef FWUPDATE_FIRMWARE_UPDATE_H_
#define FWUPDATE_FIRMWARE_UPDATE_H_

#include "stdint.h"       // 표준 정수 타입
#include "cmsis_os.h"     // FreeRTOS 사용 시 (osThreadId 등)
#include "lwip/sockets.h" // lwIP 소켓 API 헤더
#include "lwip/ip_addr.h" // IP 주소 타입
#include "TargetCan.h"

// --- 상수 정의 ---
#define FWU_UDP_PORT                18931 // *** 장치 UDP 리스닝/응답 포트 ***
#define FWU_PC_UDP_PORT             18930 // PC UDP 리스닝/응답 포트
#define FWU_PC_TCP_SERVER_PORT      18932 // PC TCP 서버 포트
#define FWU_BUFFER_SIZE             2048  // TCP 수신 버퍼 크기
#define FWU_FLASH_PAGE_SIZE         (128 * 1024) // STM32H743 플래시 섹터 크기 (128KB)
#define FWU_UART_RELAY_SIZE         1024 // 내부 UART 프로토콜을 이용한 최소 Chunk 전송 단위
#define FWU_UART_BUFFER_SIZE        (FWU_UART_RELAY_SIZE + 4) * 8  // TCP 수신 버퍼 크기
#define FWU_DEFAULT_TCP_TIMEOUT_MS  5000  // *** TCP 읽기 기본 타임아웃 (5초) ***
#define FWU_DEFAULT_UART_RETRY_MS   5000  // *** UART 읽기 기본 타임아웃 (5초) ***
#define FWU_DEFAULT_CAN_RETRY_MS    1000  // *** CAN 읽기 기본 타임아웃 (1초) ***
#define FWU_MAX_RETRY_COUNT         3

// --- *** 비활성 뱅크 CPU 시작 주소 상수 추가 *** ---
#define FWU_INACTIVE_BANK_START_ADDR ((uint32_t)0x08100000)
#define FWU_FLASH_BANK_SIZE          ((uint32_t)0x00100000) // 1MB

// SHA-256 해시 크기
#define SHA256_HASH_SIZE            32

// --- 펌웨어 업데이트 상태 정의 ---
typedef enum
{
    FWU_STATE_IDLE,             // 유휴 상태
    FWU_STATE_READY,            // UDP F0/01 수신, TCP 연결 준비
    FWU_STATE_CONNECTING,       // PC TCP 서버에 연결 시도 중
    FWU_STATE_CONNECTED,        // TCP 연결 성공, F1/04 전송 완료, 데이터 수신 대기

    FWU_STATE_SELF_RECEIVING,   // 자신의 F0/04 바이너리 청크 수신 및 RAM 버퍼링 중
    FWU_STATE_UART_TGT_RECEIVING, // UART Target 장비의 F0/04 바이너리 청크 수신 및 RAM 버퍼링 중
	FWU_STATE_UART_TGT_WAIT_ACK, // UART Target 장비 ACK 대기

    FWU_STATE_CAN_TGT_RECEIVING, // CAN Target 장비의 F0/04 바이너리 청크 수신 및 RAM 버퍼링 중
	FWU_STATE_CAN_TGT_WAIT_ACK, // CAN Target 장비 ACK 대기
	FWU_STATE_CAN_TGT_ACK_ERROR,
	FWU_STATE_CAN_TGT_FLASHING,

    FWU_STATE_WRITE_PAGE,       // 128KB 페이지 플래시 기록 중 (Blocking 상태)
    FWU_STATE_WRITE_ERROR,      // 플래시 쓰기 오류 발생
    FWU_STATE_VERIFYING,        // 수신 완료 후 펌웨어 검증 중
    FWU_STATE_VERIFY_FAILED,    // 검증 실패
    FWU_STATE_COMPLETE,         // F0/05 수신, 최종 처리 및 F1/06 전송 대기 (검증 후 상태)
    FWU_STATE_READY_TO_SWAP,    // F1/06 전송 완료, 뱅크 스왑 및 리부팅 준비

	FWU_STATE_UART_TGT_COMPLETE, // UART Target 업데이트 완료
    FWU_STATE_CAN_TGT_COMPLETE, // CAN Target 업데이트 완료

    FWU_STATE_ERROR             // 복구 불가능한 오류 상태
} FirmwareUpdateState_t;

// --- *** DeviceInfo 구조체 정의 추가 *** ---
typedef enum
{
	FWU_DEVICE_MFHF = 0b01,
	FWU_DEVICE_VHF = 0b10,
	FWU_DEVICE_NAVTEX = 0b11
} fwu_device_type_t;

typedef enum
{
	FWU_SUB_DEVICE_CONTROLLER = 0b001,
	FWU_SUB_DEVICE_TRANSCEIVER = 0b010,
	FWU_SUB_DEVICE_ATU = 0b011,
	FWU_SUB_DEVICE_REMOTE_HANDSET = 0b100,
	FWU_SUB_DEVICE_ALARM_BOX = 0b101,
	FWU_SUB_DEVICE_ALARM_UNIT = 0b110,
	FWU_SUB_DEVICE_RECEIVER = 0b111
} fwu_sub_device_type_t;

typedef union
{
	uint8_t val;
	struct
	{
		uint8_t DeviceType : 2;
		uint8_t SubDeviceType : 3;
		uint8_t DeviceNumber : 3;
	};
} fwu_device_param_t;

// 장치 검색 응답(FA/00) 페이로드 구조
typedef struct FWUDeviceInfo
{
	fwu_device_param_t DeviceParam;
    uint8_t MajorVersion;
    uint8_t MinorVersion;
    uint8_t PatchVersion;
    uint32_t IpAddress; // Little Endian
    uint32_t SubnetMask; // Little Endian
} FWU_DeviceInfo_t;

typedef struct FWUTargetInfo
{
	FWU_DeviceInfo_t device;
	uint8_t up_major;
	uint8_t up_minor;
	uint8_t up_patch;
} FWU_UpdateInfo_t;

// --- 프로토콜 명령/서브명령 코드 ---
// PC -> GMDSS
#define FWU_CMD_MASTER_BASE                0xF0
#define FWU_CMD_MASTER_CAN_METAINFO        FWU_CMD_MASTER_BASE
#define FWU_SUB_MASTER_CAN_METAINFO        0x00
#define FWU_CMD_MASTER_UPDATE_READY_REQ    FWU_CMD_MASTER_BASE
#define FWU_SUB_MASTER_UPDATE_READY_REQ    0x01
#define FWU_CMD_MASTER_BINARY_CHUNK        FWU_CMD_MASTER_BASE
#define FWU_SUB_MASTER_BINARY_CHUNK        0x02
#define FWU_CMD_MASTER_BINARY_COMPLETE     FWU_CMD_MASTER_BASE
#define FWU_SUB_MASTER_BINARY_COMPLETE     0x03

// GMDSS -> PC
#define FWU_CMD_SLAVE_BASE                 0xF1
#define FWU_CMD_SLAVE_UPDATE_SEND_REQ      FWU_CMD_SLAVE_BASE
#define FWU_SUB_SLAVE_UPDATE_SEND_REQ      0x04
#define FWU_CMD_SLAVE_BINARY_CHUNK_ACK     FWU_CMD_SLAVE_BASE
#define FWU_SUB_SLAVE_BINARY_CHUNK_ACK     0x05
#define FWU_CMD_SLAVE_BINARY_COMPLETE_ACK  FWU_CMD_SLAVE_BASE
#define FWU_SUB_SLAVE_BINARY_COMPLETE_ACK  0x06
#define FWU_CMD_ERROR                      FWU_CMD_SLAVE_BASE
#define FWU_SUB_ERROR                      0xFF

// GMDSS Internal CMD
#define FWU_CMD_ENTRY_UPDATE_MODE          0xFE
#define FWU_SUB_ENTRY_UPDATE_MODE_REQ      0x00
#define FWU_SUB_ENTRY_UPDATE_MODE_ACK      0x0A

// GMDSS Internal CMD
#define FWU_CMD_UPDATE_STATUS              0xFC
#define FWU_SUB_UPDATE_STATUS_RESET        0xFF
#define FWU_SUB_UPDATE_STATUS_START        0x01 // byte1: dev_prop, byte 2: major ver, byte 3: minor ver, byte 4: patch ver
#define FWU_SUB_UPDATE_STATUS_PROGRESS     0x02 // byte1~4: uint32_t progress
#define FWU_SUB_UPDATE_STATUS_FLASHING     0x03
#define FWU_SUB_UPDATE_STATUS_FINISH       0x04
#define FWU_SUB_UPDATE_ERROR       		   0x05

// GMDSS Internal CMD
#define FWU_CMD_DEVICE_SEARCH              0xFF
#define FWU_SUB_DEVICE_SEARCH              0x00

// GMDSS Internal CMD
#define FWU_CMD_DEVICE_SEARCH_ACK          0xFA
#define FWU_SUB_DEVICE_SEARCH_ACK          0x00

// --- 함수 프로토타입 ---
int FirmwareUpdate_Init(void);
void FirmwareUpdate_Task(void const *argument);
FirmwareUpdateState_t FirmwareUpdate_GetState(void);
void FirmwareUpdate_proxy_device_search_response_udp(uint8_t *p_frame, uint16_t frame_len);
void FirmwareUpdate_proxy_fwu_to_tcp(uint8_t *p_frame, uint16_t frame_len);

void FirmwareUpdate_proc_can_cmd(can_msg_s msg);

#endif /* FWUPDATE_FIRMWARE_UPDATE_H_ */
