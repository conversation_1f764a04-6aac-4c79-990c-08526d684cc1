/**
******************************************************************************
* @file      Coef_FS_24KHZ_LP_1700HZ_IIR.c
* <AUTHOR>
* @date      2025-03-26
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2025 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_APPS_MODEL_MODEM_COEF_FS_24KHZ_LP_1700HZ_IIR_H_
#define SRC_APPS_MODEL_MODEM_COEF_FS_24KHZ_LP_1700HZ_IIR_H_

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "AllConst.h"
#include "CommonLib.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  FLT_FS_24KHZ_LP_1700HZ_IIR_SIZE         (22)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

#if defined(__cplusplus)
extern "C" {
#endif   // __cplusplus


//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
extern tBaseIIR G_xIir_Fs_24Khz_Lp_1700hz_IIR_Int_Ch;
extern tBaseIIR G_xIir_Fs_24Khz_Lp_1700hz_IIR_Local_Ch;
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========


#endif /* SRC_APPS_MODEL_MODEM_COEF_FS_24KHZ_LP_1700HZ_IIR_H_ */
