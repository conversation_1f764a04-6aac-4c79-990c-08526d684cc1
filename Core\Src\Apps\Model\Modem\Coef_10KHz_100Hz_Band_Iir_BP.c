/**
******************************************************************************
* @file      Coef_10KHz_100Hz_Band_Iir_BP.c
* <AUTHOR>
* @date      2024-09-04
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "AllConst.h"
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
//------_------_------_------_------_------_------_------//
//      must invert the sign of the coefficients A       //
//------_------_------_------_------_------_------_------//
static const double vCoeffA[FLT_10KHZ_100HZ_BAND_IIR_BP_SIZE + 0] = 
{
    +0.000000000000002,
    -7.748056999718240,
    +0.000000000000016,
    -26.268039673858300,
    +0.000000000000048,
    -50.896464681790500,
    +0.000000000000077,
    -61.643871967785200,
    +0.000000000000075,
    -47.789633293686700,
    +0.000000000000044,
    -23.158916966655900,
    +0.000000000000014,
    -6.413937555783170,
    +0.000000000000002,
    -0.777263922719693,
};
static const double vCoeffB[FLT_10KHZ_100HZ_BAND_IIR_BP_SIZE + 1] = 
{ 
    +0.000291838836112,
    +0.000000000000000,
    +0.002282155767441,
    +0.000000000000000,
    +0.007857672215776,
    +0.000000000000000,
    +0.015560694322588,
    +0.000000000000000,
    +0.019386678116844,
    +0.000000000000000,
    +0.015560694322588,
    +0.000000000000000,
    +0.007857672215776,
    +0.000000000000000,
    +0.002282155767441,
    +0.000000000000000,
    +0.000291838836112,
};

 //-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
tBaseIIR    G_xIir_10Khz_BP_Of_518KHz =
          {
            .dSizeB = FLT_10KHZ_100HZ_BAND_IIR_BP_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };

tBaseIIR    G_xIir_10Khz_BP_Of_490KHz =
          {
            .dSizeB = FLT_10KHZ_100HZ_BAND_IIR_BP_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };

tBaseIIR    G_xIir_10Khz_BP_Of_42095KHz =
          {
            .dSizeB = FLT_10KHZ_100HZ_BAND_IIR_BP_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
