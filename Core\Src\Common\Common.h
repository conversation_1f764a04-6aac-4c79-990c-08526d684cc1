/**
******************************************************************************
* @file      Common.h
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#ifndef SRC_COMMON_COMMON_H_
#define SRC_COMMON_COMMON_H_

#include "Common_Config.h"
#include "Common_Def.h"
#include "Common_Include.h"
#include "MassVariable.h"

u32 getMovingAvgFiltered(u32 *pArrVal, u8 *pFlagHold, u32 val, u32 cnt);
uint16_t crc16_calc(const uint16_t init_crc, const void *p, uint32_t len);
int Common_Tick_Period_Timer(int *p_tick, int stndard_tick);
int Common_Tick_End_Timer(int *p_tick, int stndard_tick);

#endif /* SRC_COMMON_COMMON_H_ */
