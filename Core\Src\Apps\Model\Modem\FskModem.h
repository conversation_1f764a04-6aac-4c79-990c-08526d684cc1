﻿/**
 * @file      FskModem.h
 * <AUTHOR>
 * @brief     FskModem 관련 함수
 * @version   0.1
 * @date      2022-10-26
 * 
 * @copyright Copyright (c) 2022
 * 
 */


#if !defined(__FskModem_H__)
#define      __FskModem_H__

//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#include "AllConst.h"
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define FSK_DEBUG_NONE 0
#define FSK_DEBUG_ON 1
#define FSK_DEBUG_OFF 2
#define FSK_DEBUG_MODE FSK_DEBUG_ON
//==========================================================================================
//#define FSK_SAMPLE_POINT 120
#define FSK_SAMPLE_POINT 79
//#define FSK_SAMPLE_POINT 9
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define FSK_TX_VOLTAGE_PP 22
#define FSK_TX_VOLTAGE_OFFSET 8
//==========================================================================================
#define FSK_DIAG_DOT_NUM 100
//==========================================================================================
#define  FSK_MDM_BIT_DATA_EMPTY        (-1)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  FSK_MDM_BAUD_RATE_0100        (100)
#define  FSK_MDM_BAUD_RATE_1200        (1200)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  FSK_MDM_FREQ_SPC_0100         (1700 + 85)
#define  FSK_MDM_FREQ_MRK_0100         (1700 - 85)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  FSK_MDM_FREQ_SPC_1200         (1700 + 00)
#define  FSK_MDM_FREQ_MRK_1200         (1700 - 400)
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
#define  TWO_OSC_RUN_MODE_FILTER       (0)
#define  TWO_OSC_RUN_MODE_FUNCTION     (1)
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
typedef enum
{
  FSK_FREQ_CH_START = 0,
  FSK_FREQ_LOC_490KHZ = FSK_FREQ_CH_START,
  FSK_FREQ_INT_518KHZ,
  FSK_FREQ_LOC_42095KHZ,
  FSK_FREQ_MAX,
}FSK_FREQ_CH_t;

typedef enum
{
  FSK_MODE_TYPE_NONE = 0,
  FSK_MODE_TYPE_RUN = FSK_MODE_TYPE_NONE,
  FSK_MODE_TYPE_DIAG,
} FSK_RECV_MODE_t;

typedef enum
{
  FSK_DIAG_STATE_NONE = 0,
  FSK_DIAG_STATE_RUN,
  FSK_DIAG_STATE_END,
} FSK_DIAG_STATE_t;
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
#if defined(__cplusplus)
extern "C" {
#endif   //// __cplusplus

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
typedef  struct {
         uint32_t   dRunMode;                    // FULL_SAMPLING_RATE / FSK_100_MDM_BAUD_RATE;
         float32_t  fVcoPhs;
         float32_t  fOscPhs;                     // oscillating Phase (2.0 * sin(pi * dOscFrq / nSmpFrq))
         float32_t  fCosVal;                     // cos
         float32_t  fSinVal;                     // sin
       } tTwoOSC;
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
typedef  struct {
         tTwoOSC    xSpcOSC;
         tTwoOSC    xMrkOSC;

         float32_t fSpcCos;
         float32_t fSpcSin;
         float32_t fMrkCos;
         float32_t fMrkSin;
         
         float32_t fSpcSum;
         float32_t fMrkSum;

         uint32_t   dRunSize;                    // FULL_SAMPLING_RATE / FSK_100_MDM_BAUD_RATE;
         uint32_t   dRunPosX;

         float32_t *pRunSpcI;
         float32_t *pRunSpcQ;
         float32_t *pRunMrkI;
         float32_t *pRunMrkQ;

         float32_t  fSumSpcI;
         float32_t  fSumSpcQ;
         float32_t  fSumMrkI;
         float32_t  fSumMrkQ;

         uint32_t    vBitPack[4];
         int32_t    dDetermineFreq;

         uint32_t   dBitDataP;                   // Previous data
         uint32_t   dBitDataC;                   // Current  data
         uint32_t   dBitData;

         uint32_t   dRecvSize;
         uint8_t   *pRecvData;
         uint32_t   dRecvHead;
         uint32_t   dRecvTail;

         uint32_t   dPllFullV;
         uint32_t   dPllHalfV;
         uint32_t   dPllSmpPt[FSK_SAMPLE_POINT];
         uint32_t   dPllSmpPt_Div;
         uint32_t   dPllSmpPtStepC; 
         uint32_t   dPllSmpPtStepP;
         uint32_t   dPllSmpPtStepCnt;
         int32_t    dPllSmpPtCnt;
         uint32_t   dPllTarget_10per;
         uint32_t   dPllTarget_90per;

        //  uint32_t   dPllIncrV;
        //  uint32_t   dPllCalibrationV;
        //  uint32_t   dPllMaxCalibrationV;
        //  uint32_t   dPllStepV;
        //  uint32_t   dPllValue;
        //  int32_t   dPllDiffValue;
        //  int32_t   dPllDiffBuffer[30];
        //  uint32_t   dPllDiffCnt;

        int32_t   dPllIncrV;
        int32_t   dPllCalibrationV;
        int32_t   dPllMaxCalibrationV;
        int32_t   dPllStepV;
        int32_t   dPllValue;
        int32_t   dPllDiffValue;
        int32_t   dPllDiffBuffer[30];
        int32_t   dPllDiffCnt;

         uint32_t   dAdcSize;
         uint16_t  *pAdcData;
         uint32_t   dAdcPosX;
         uint32_t   dAdcSumX;
         uint16_t   wAdcAvrX;

         uint16_t    dFreqCh;
       } tFskRxMDM;
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
typedef enum
{
  TX_STATE_STOP,
  TX_STATE_MARK_OUT,
  TX_STATE_SPACE_OUT,
  TX_STATE_DOT_OUT,
  TX_STATE_MSG_OUT,
} FskTxState_t;
typedef  struct {
         float32_t  fSpcPhiVal;
         float32_t  fMrkPhiVal;
         float32_t  fVcoPhiVal;
         float32_t  fVcoAmpVal;

         uint32_t   dSendSize;
         uint8_t   *pSendData;
         uint32_t   nSendHead;
         uint32_t   nSendTail;

         uint32_t   dTickValX;
         uint32_t   dTickCntr;
         uint32_t   dSendData;
         uint32_t   dSendMode;

         float32_t v_pp;
         float32_t v_offset;
       } tFskTxMDM;
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
void     MakeInitTwoOSC(tTwoOSC *pOSC, uint32_t dRunMode, uint32_t dSmpFrq, uint32_t dOscFrq);
void     CalcNewTwoOsc(tTwoOSC *pOSC);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void     MakeInitFskRxModem(tFskRxMDM *pFSK, uint32_t dSmpFrq, uint32_t dBaudRate, uint32_t dSpcFreq, uint32_t dMrkFreq, uint32_t dBufSize, uint32_t dAdcSize);
void     ConfigFskRxModemFreqCh(tFskRxMDM *pFSK, uint16_t Freq);
uint32_t RxSamplePointStepCheck(tFskRxMDM *pFSK);
uint32_t RxSamplePointFinalResult(tFskRxMDM *pFSK);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void     MakeInitFskTxModem(tFskTxMDM *pFSK, uint32_t dSmpFrq, uint32_t dBaudRate, uint32_t dSpcFreq, uint32_t dMrkFreq, uint32_t dBufSize);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
int      ReadFskRxBitData(tFskRxMDM *pFSK);
void     ApndFskRxBitData(tFskRxMDM *pFSK, uint8_t bBitData);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void     ClearAllFskRxBitData(tFskRxMDM *pFSK);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  ClearAllFskTxBitData(tFskTxMDM *pFSK);
int      ReadFskTxBitData(tFskTxMDM *pFSK);
void     ApndFskTxBitData(tFskTxMDM *pFSK, uint8_t bBitData);
void  ApndFskTx7BitData(tFskTxMDM *pFSK, uint8_t bByteData);
void    ApndFskTxDiagDotData(tFskTxMDM *pFSK, uint8_t len);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void  SetFskTxSendMode(tFskTxMDM *pFSK, FskTxState_t State);
uint32_t GetFskTxSendMode(tFskTxMDM *pFSK);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
float32_t GetFskTxAmpValue(tFskTxMDM *pFSK);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
int      CalcFskRxAdcAvrVal(tFskRxMDM *pFSK, uint16_t wAdcVal);
void     DecideFskRxBitData(tFskRxMDM *pFSK, int nAdcVal);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void     RunFskRxModemISR(tFskRxMDM *pFSK, int nAdcVal);
void     RunFskTxModemISR(tFskTxMDM *pFSK);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
int Proc_Fsk_DeMod_Test(int ch, int final_val);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
void Set_Self_BER_Rf_Test(tFskTxMDM *pFSK_Tx);
void Proc_Self_BER_Rf_Test(tFskRxMDM *pFSK_Rx, tFskTxMDM *pFSK_Tx, int final_val);
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
extern   tFskTxMDM G_xMsgFskTxMDM;
extern   tFskRxMDM G_xMsgFskRxMDM_518KHz;
extern   tFskRxMDM G_xMsgFskRxMDM_490KHz;
extern   tFskRxMDM G_xMsgFskRxMDM_42095KHz;
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

#if defined(__cplusplus)
           }
#endif   // __cplusplus


#endif   // __FskModem_H__

