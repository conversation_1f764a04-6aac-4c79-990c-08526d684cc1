///@file     std450_sf.h
///@brief    IEC 61162-450 System Function (SF) block behavior code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#ifndef STD450_SF_H
#define STD450_SF_H

#include "std450.h"

STD450_BEGIN_DECLS

#define MIME_TYPE_LEN  32

#define MIME_TYPE_GIF  "image/gif"
#define MIME_TYPE_BMP  "image/x-ms-bmp"
#define MIME_TYPE_GTAR "application/x-gtar"
#define MIME_TYPE_TAR  "application/x-tar"
#define MIME_TYPE_ZIP  "application/zip"
#define MIME_TYPE_XML  "application/xml"
#define MIME_TYPE_BIN  "application/octet-stream"

#define EXTENSION_GIF  ".gif"
#define EXTENSION_BMP  ".bmp"
#define EXTENSION_GTAR ".gtar"
#define EXTENSION_TAR  ".tar"
#define EXTENSION_ZIP  ".zip"
#define EXTENSION_XML  ".xml"
#define EXTENSION_BIN  ".bin"

#define BINARY_VERSION 2
#define MIN_UDPBC_LEN  6
#define MAX_PACKET_CNT 1000
#define MAX_TX_TIMEOUT 5
#define MAX_RX_TIMEOUT 5

///@brief SF communication Type
enum {
    SF_COMM_TYPE_NONE=0,    ///>None.
    SF_COMM_TYPE_UDP,       ///>UDP (Ethernet)
    SF_COMM_TYPE_UART,      ///>UART (RS-422/485)
    SF_COMM_TYPE_CAN,       ///>CAN
    SF_COMM_TYPE_TCP,       ///>TCP (Ethernet)
    SF_COMM_TYPE_MAX = SF_COMM_TYPE_TCP,
};

///@brief SF function Type
enum {
    SF_FUNC_TYPE_NONE=0,    /// None.
    SF_FUNC_TYPE_UDPBC,     /// Header UdPbC: Transmission of IEC 61162-1 formatted sentences.
    SF_FUNC_TYPE_RAUDP,     /// Header RaUdP: Transmission of binary files.
    SF_FUNC_TYPE_RRUDP,     /// Header RrUdP: Transmission of re-transmittable binary files.
    SF_FUNC_TYPE_NKPGN,     /// Header NkPgN: Transmission of IEC 61162-3 PGN messages.
    SF_FUNC_TYPE_RRTCP,     /// Header RrTcP: Transmission of binary files using TCP protocol.
    SF_FUNC_TYPE_MAX= SF_FUNC_TYPE_RRTCP,
};

STD450_API int     std450_new_sf(std450_t *dev450, int sf_count);
STD450_API void    std450_free_sf(std450_t *dev450);
STD450_API int     std450_sf_open(std450_t *dev450, int idx, ...);
STD450_API int     std450_sf_close(std450_t *dev450, int idx);
STD450_API int     std450_sf_type_set(std450_t *dev450, int idx, int comm_type, int func_type);
STD450_API int     std450_sf_set_src(std450_t *dev450, int idx, char* talker, int number);
STD450_API void    std450_sf_get_src(std450_t *dev450, int idx, char* src);
STD450_API int     std450_sf_set_dst(std450_t *dev450, int idx, char* talker, int number);
STD450_API int     std450_sf_add_dst(std450_t *dev450, int idx, char* talker, int number);
STD450_API int     std450_sf_get_dst(std450_t *dev450, int idx, char* dst);
STD450_API int     std450_sf_set_opt(std450_t *dev450, int idx, ...);
STD450_API void    std450_sf_run(std450_t *dev450, int idx);
STD450_API void    std450_sf_stop(std450_t *dev450, int idx);
STD450_API ssize_t std450_sf_write_msg(std450_t *dev450, int idx, void *msg, int len);
STD450_API ssize_t std450_sf_read_msg(std450_t *dev450, int idx, void *msg, int len);
STD450_API ssize_t std450_sf_write_tm_msg(std450_t *dev450, int idx, int msec, void *msg, int len);
STD450_API ssize_t std450_sf_read_tm_msg(std450_t *dev450, int idx, int msec, void *msg, int len);


STD450_END_DECLS

#endif  /* STD450_SF_H */