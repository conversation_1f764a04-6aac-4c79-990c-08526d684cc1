/**
  ******************************************************************************
  * @file    LwIP/LwIP_TCP_Echo_Server/Src/app_ethernet.c
  * <AUTHOR> Application Team
  * @brief   Ethernet specefic module
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include <stdio.h>
#include "ethernetif.h"
#include "lwip/opt.h"
#if LWIP_DHCP
#include "lwip/dhcp.h"
#endif
#include "lwip/tcpip.h"
#include "lwip/sockets.h"
#include "app_ethernet.h"
#include "TargetBoard.h"
#include "std450.h"
#include "std450_sf.h"
#include "voicecast/G729A/typedef.h"
#include "voicecast/RTP/rtp-packet.h"
#include "voicecast/audio_bc.h"
#include "model.h"

#if LWIP_DHCP
#define MAX_DHCP_TRIES  4
static __IO DHCP_State_t    DHCP_state = DHCP_OFF;
#endif

#ifdef ENABLE_TCP_TEST
static __IO SOCK_State_t     SOCK_State = SOCK_OFF;
static int g_address_assigned = 0;
static int _Sock;
static struct sockaddr_in _Addr;
struct tcp_pcb *pcb_client;
static ip_addr_t server_addr;
err_t err;
uint8_t test_data[] = "Hi, This is test equipment!!!\r\n";
#endif

static struct netif gnetif;
static int address_assigned = 0;
static int eth_link_status = 0;
std450_t *g_dev450 = NULL;

/**
  * @brief  Setup the network interface
  * @param  None
  * @retval None
  */
 void Netif_Config(void)
 {
	ip_addr_t ipaddr;
	ip_addr_t netmask;
	ip_addr_t gw;

	IP_ADDR4(&ipaddr, 	g_hSysCfg.m_pCfg->network.static_ip[0],
						g_hSysCfg.m_pCfg->network.static_ip[1],
						g_hSysCfg.m_pCfg->network.static_ip[2],
						g_hSysCfg.m_pCfg->network.static_ip[3]);

	IP_ADDR4(&netmask, 	g_hSysCfg.m_pCfg->network.static_netmask[0],
						g_hSysCfg.m_pCfg->network.static_netmask[1],
						g_hSysCfg.m_pCfg->network.static_netmask[2],
						g_hSysCfg.m_pCfg->network.static_netmask[3]);

	IP_ADDR4(&gw, 		g_hSysCfg.m_pCfg->network.static_gateway[0],
						g_hSysCfg.m_pCfg->network.static_gateway[1],
						g_hSysCfg.m_pCfg->network.static_gateway[2],
						g_hSysCfg.m_pCfg->network.static_gateway[3]);
 
	/* add the network interface */
	netif_add(&gnetif, &ipaddr, &netmask, &gw, NULL, &ethernetif_init, &tcpip_input);

	/*  Registers the default network interface */
	netif_set_default(&gnetif);

	ethernet_link_status_updated(&gnetif);
 
 #if LWIP_NETIF_LINK_CALLBACK
   	netif_set_link_callback(&gnetif, ethernet_link_status_updated);
   
 #if 1 // KPB 20231115 link changed bug fix
   osThreadDef(EthLink, user_ethernet_link_thread, osPriorityNormal, 0, configMINIMAL_STACK_SIZE *2);	
 #else
   osThreadDef(EthLink, ethernet_link_thread, osPriorityNormal, 0, configMINIMAL_STACK_SIZE *2);
 #endif
   osThreadId EthLink_Thread_Id;
   EthLink_Thread_Id = osThreadCreate (osThread(EthLink), &gnetif);
   if(EthLink_Thread_Id == NULL)
   {
	 DEBUG_MSG("[%s] EthLink thread create fail !!!!\r\n", __FUNCTION__);
   }
 #endif
 
 #if LWIP_DHCP
   /* Start DHCPClient */
   osThreadId DHCP_Thread_Id;
   osThreadDef(DHCP, DHCP_Thread, osPriorityBelowNormal, 0, configMINIMAL_STACK_SIZE * 4);
   DHCP_Thread_Id = osThreadCreate (osThread(DHCP), &gnetif);
   if(DHCP_Thread_Id == NULL)
   {
	 DEBUG_MSG("[%s] DHCP thread create fail !!!!\r\n", __FUNCTION__);
   }
 #endif
 
 #ifdef ENABLE_TCP_TEST
	 osThreadDef(thd_tcp, SOCK_Thread, osPriorityNormal, 0, configMINIMAL_STACK_SIZE *4);
	 osThreadCreate (osThread(thd_tcp), NULL);
 #endif
   //udp_echoserver_init();
 }
 
 #ifdef ENABLE_TCP_TEST
 static void app_send_data(void)
 {
   //tcp_write(pcb_client, &packet,sizeof(struct time_packet), TCP_WRITE_FLAG_COPY); //use copied data
   tcp_write(pcb_client, test_data,sizeof(test_data), 0); //use pointer, should not changed until receive ACK
 }
 
 
 static err_t tcp_callback_connected(void *arg, struct tcp_pcb *pcb_new, err_t err)
 {
   LWIP_UNUSED_ARG(arg);
 
   if (err != ERR_OK) //error when connect to the server
   {
	 return err;
   }
 
   return ERR_OK;
 }
 
 void SendTcpTest(struct netif *pIf)
 {
	 if (pcb_client == NULL)
	 {
		 pcb_client = tcp_new();
		 if (pcb_client == NULL) //lack of memory
		 {
			 memp_free(MEMP_TCP_PCB, pcb_client);
			 pcb_client = NULL;
			 printf("[pcb_client]lack of memory!\r\n");
		 }
	 }	
	 IP4_ADDR(&server_addr,10,1,201,19);
	 err = tcp_connect(pcb_client, &server_addr, 7000, tcp_callback_connected);
 
	 app_send_data();
	 if(err == ERR_ISCONN)
	 {
	 }
 }
 #endif

/**
  * @brief  Notify the User about the network interface config status
  * @param  netif: the network interface
  * @retval None
  */
void ethernet_link_status_updated(struct netif *netif)
{
	uint8_t iptxt[30];
	
    DEBUG_MSG("[%s] Status updated\r\n",__FUNCTION__);

	if (netif_is_up(netif))
 	{
		eth_link_status = 1;
		if(g_hSysCfg.m_pCfg->network.mode == MV_VAL_LAN_COMM_DHCP)
		{
			/* Update DHCP state machine */
#if LWIP_DHCP
			Set_DHCP_State(DHCP_START);
#endif
		}
		else
		{
			Set_address_assinged(0);

			netif_set_default(netif);
			netif_set_addr(netif, ip_2_ip4(&netif->ip_addr), ip_2_ip4(&netif->netmask), ip_2_ip4(&netif->gw));

			connect_std450();
			Set_address_assinged(1);

			g_hSysCfg.m_pCfg->network.mode = MV_VAL_LAN_COMM_STATIC;
			Internal_Tx_Network_Setting_Info(	eth_link_status, 
												g_hSysCfg.m_pCfg->network.mode,
												netif->ip_addr,
												netif->netmask,
												netif->gw);

			sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_addr(netif)));
			DEBUG_MSG("[%s][OK] Static IP address: %s\r\n",__FUNCTION__, iptxt);
			sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_netmask(netif)));
			DEBUG_MSG("[%s][OK] Static Netmask address: %s\r\n",__FUNCTION__, iptxt);
			sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_gw(netif)));
			DEBUG_MSG("[%s][OK] Static Gateway address: %s\r\n",__FUNCTION__, iptxt);
		}
  	}
	else
  	{
		eth_link_status = 0;
		if(g_hSysCfg.m_pCfg->network.mode == MV_VAL_LAN_COMM_DHCP)
		{
#if LWIP_DHCP
			Set_DHCP_State(DHCP_LINK_DOWN);
#endif
		}
		else
		{
			DEBUG_MSG("[%s][OK] The network cable is not connected\r\n", __FUNCTION__);
		}
		Set_address_assinged(0);

		Internal_Tx_Network_Setting_Info(	eth_link_status, 
											g_hSysCfg.m_pCfg->network.mode,
											gnetif.ip_addr,
											gnetif.netmask,
											gnetif.gw);
	}
}

/**
  * @brief  Set_Network
  * @param  
  * @retval 
*/
void Set_Static_Network(int *p_ip, int *p_netmask, int *p_gateway)
{
	ip_addr_t ipaddr;
	ip_addr_t netmask;
	ip_addr_t gw;
	uint8_t iptxt[30];

	if(Get_DHCP_State() == DHCP_START || Get_DHCP_State() == DHCP_WAIT_ADDRESS)
	{
		dhcp_release_and_stop(&gnetif);
		Set_DHCP_State(DHCP_OFF);
	}

	Set_address_assinged(0);
	IP_ADDR4(&ipaddr, p_ip[0] ,p_ip[1] , p_ip[2] , p_ip[3]);
	IP_ADDR4(&netmask, p_netmask[0], p_netmask[1], p_netmask[2], p_netmask[3]);
	IP_ADDR4(&gw, p_gateway[0], p_gateway[1], p_gateway[2], p_gateway[3]);
	netif_set_default(&gnetif);
	netif_set_addr(&gnetif, ip_2_ip4(&ipaddr), ip_2_ip4(&netmask), ip_2_ip4(&gw));
	connect_std450();
	Set_address_assinged(1);

	g_hSysCfg.m_pCfg->network.mode = MV_VAL_LAN_COMM_STATIC;
	Internal_Tx_Network_Setting_Info(	eth_link_status, 
										g_hSysCfg.m_pCfg->network.mode,
										gnetif.ip_addr,
										gnetif.netmask,
										gnetif.gw);
	
	sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_addr(&gnetif)));
	DEBUG_MSG("[%s][OK] Static IP address: %s\r\n",__FUNCTION__, iptxt);
	sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_netmask(&gnetif)));
	DEBUG_MSG("[%s][OK] Static Netmask address: %s\r\n",__FUNCTION__, iptxt);
	sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_gw(&gnetif)));
	DEBUG_MSG("[%s][OK] Static Gateway address: %s\r\n",__FUNCTION__, iptxt);
}

/**
  * @brief  Set_address_assinged
  * @param  
  * @retval 
*/
void Set_address_assinged(int state)
{
	address_assigned = state;
}

/**
  * @brief  Get_address_assinged
  * @param  
  * @retval 
*/
int Get_address_assinged(void)
{
	return address_assigned;
}

#if LWIP_DHCP
/**
  * @brief  Sets DHCP State
  * @param  
  * @retval 
*/
void Set_DHCP_State(int state)
{
	DHCP_state = state;
}

/**
  * @brief  Gets DHCP State
  * @param  
  * @retval 
*/
int Get_DHCP_State(void)
{
	return DHCP_state;
}

/**
  * @brief  DHCP periodic check
  * @param  netif
  * @retval None
  */
void DHCP_Thread(void const * argument)
{
	struct netif *netif = (struct netif *) argument;
	struct dhcp *dhcp;

	uint8_t iptxt[30];
	for (;;)
	{
		switch (Get_DHCP_State())
		{
			case DHCP_START:
				{
					Set_address_assinged(0);
					netif_set_default(netif);
					ip_addr_set_zero_ip4(&netif->ip_addr);
					ip_addr_set_zero_ip4(&netif->netmask);
					ip_addr_set_zero_ip4(&netif->gw);
					Set_DHCP_State(DHCP_WAIT_ADDRESS);
					DEBUG_MSG("[%s][OK] State: Looking for DHCP server ...\r\n",__FUNCTION__);
					dhcp_start(netif);
				}
				break;
			
			case DHCP_WAIT_ADDRESS:
				{
					if (dhcp_supplied_address(netif))
					{
						if(netif->ip_addr.addr == 0)
						{
							dhcp_release_and_stop(netif);
							Set_DHCP_State(DHCP_START);
						}
						else
						{
							Set_DHCP_State(DHCP_ADDRESS_ASSIGNED);

							sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_addr(netif)));
							DEBUG_MSG("[%s][OK] DHCP IP address: %s\r\n",__FUNCTION__, iptxt);
							sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_netmask(netif)));
							DEBUG_MSG("[%s][OK] DHCP Netmask address: %s\r\n",__FUNCTION__, iptxt);
							sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_gw(netif)));
							DEBUG_MSG("[%s][OK] DHCP Gateway address: %s\r\n",__FUNCTION__, iptxt);

							connect_std450();
							Set_address_assinged(1);

							g_hSysCfg.m_pCfg->network.mode = MV_VAL_LAN_COMM_DHCP;
							Internal_Tx_Network_Setting_Info(	eth_link_status, 
																g_hSysCfg.m_pCfg->network.mode,
																netif->ip_addr,
																netif->netmask,
																netif->gw);
							//TargetEthernet_Init(netif);
#ifdef ENABLE_TCP_TEST
							SendTcpTest(netif);
#endif
						}
					}
					else
					{
						dhcp = (struct dhcp *)netif_get_client_data(netif, LWIP_NETIF_CLIENT_DATA_INDEX_DHCP);

						/* DHCP timeout */
						if (dhcp->tries > MAX_DHCP_TRIES)
						{
							Set_DHCP_State(DHCP_TIMEOUT);

							// // /* Static address used */
							// IP_ADDR4(&ipaddr, 	g_hSysCfg.m_pCfg->network.static_ip[0],
							// 					g_hSysCfg.m_pCfg->network.static_ip[1],
							// 					g_hSysCfg.m_pCfg->network.static_ip[2],
							// 					g_hSysCfg.m_pCfg->network.static_ip[3]);
		
							// IP_ADDR4(&netmask, 	g_hSysCfg.m_pCfg->network.static_netmask[0],
							// 					g_hSysCfg.m_pCfg->network.static_netmask[1],
							// 					g_hSysCfg.m_pCfg->network.static_netmask[2],
							// 					g_hSysCfg.m_pCfg->network.static_netmask[3]);
						
							// IP_ADDR4(&gw, 		g_hSysCfg.m_pCfg->network.static_gateway[0],
							// 					g_hSysCfg.m_pCfg->network.static_gateway[1],
							// 					g_hSysCfg.m_pCfg->network.static_gateway[2],
							// 					g_hSysCfg.m_pCfg->network.static_gateway[3]);
							// netif_set_addr(netif, ip_2_ip4(&ipaddr), ip_2_ip4(&netmask), ip_2_ip4(&gw));
							// connect_std450();
							// Set_address_assinged(1);

							// g_hSysCfg.m_pCfg->network.mode = MV_VAL_LAN_COMM_STATIC;
							// Internal_Tx_Network_Setting_Info(	eth_link_status, 
							// 									g_hSysCfg.m_pCfg->network.mode,
							// 									netif->ip_addr,
							// 									netif->netmask,
							// 									netif->gw);

							// sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_addr(netif)));
							// DEBUG_MSG("[%s][OK] Static IP address: %s\r\n",__FUNCTION__, iptxt);
							// sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_netmask(netif)));
							// DEBUG_MSG("[%s][OK] Static Netmask address: %s\r\n",__FUNCTION__, iptxt);
							// sprintf((char *)iptxt, "%s", ip4addr_ntoa(netif_ip4_gw(netif)));
							// DEBUG_MSG("[%s][OK] Static Gateway address: %s\r\n",__FUNCTION__, iptxt);

							DEBUG_MSG("[%s][ERROR] DHCP Timeout!! Re try ...\r\n", __FUNCTION__);
						}
					}
				}
				break;

			case DHCP_TIMEOUT:
				dhcp_release_and_stop(netif);
				Set_DHCP_State(DHCP_START);
			break;

			case DHCP_LINK_DOWN:
				{
					Set_DHCP_State(DHCP_OFF);
					DEBUG_MSG("[%s][OK] The network cable is not connected\r\n", __FUNCTION__);
				}
				break;

			default:
				break;
		}
		/* wait 1 sec */
		osDelay(1000);
	}
}
#endif  /* LWIP_DHCP */

void Std450_Multicast_Send(int nCh, void *msg, int len)
{
	if(Get_address_assinged() == 1)
	{
		if(g_dev450 != NULL)
		{
			std450_send(g_dev450, nCh, msg, len);
		}
	}
}

void Std450_Recv_Task(void)
{
	uint8_t UdPBC_RX_BUFF[C_MAX_450_BUF] = {0, };

	for(int i=0; i<C_MAX_CH_NUM; i++)
	{
		int rtn  = std450_recv(g_dev450, i, UdPBC_RX_BUFF, C_MAX_450_BUF);
		if (rtn > 0)
		{
			int len = strlen(UdPBC_RX_BUFF);
			Internal_Tx_Nmea0183_Data(UdPBC_RX_BUFF, len);
			DEBUG_LOG("[%s][OK] ch %d : received data : %s\r\n", __FUNCTION__, i, UdPBC_RX_BUFF);
		}
	}
}

int connect_std450(void)
{	
    // 450 ����ü ���� �� data �Է�
	if(g_dev450 != NULL)
	{
		return 0;
	}

    IEC450_Info IEC450_Info;
    strcpy(IEC450_Info.NIC_IP, ip4addr_ntoa(netif_ip4_addr(&gnetif)));	// Sourc IP

    // MISC Channel
    strcpy(IEC450_Info.CH_INFO[CH_MISC].SourceID_token, "CR");      	// s:AI0001
    IEC450_Info.CH_INFO[CH_MISC].SourceID_num = 1;
    strcpy(IEC450_Info.CH_INFO[CH_MISC].DestiID_token, "EI");       	// d:EI0001
    IEC450_Info.CH_INFO[CH_MISC].DestiID_num = 1;
    IEC450_Info.CH_INFO[CH_MISC].Device = 1;
    IEC450_Info.CH_INFO[CH_MISC].Channel = 1;
    IEC450_Info.CH_INFO[CH_MISC].Transmission_Group = TRANSGROUP_MISC;
    IEC450_Info.CH_INFO[CH_MISC].FuncType = SF_FUNC_TYPE_UDPBC;

	// NAVD Channel
	strcpy(IEC450_Info.CH_INFO[CH_NAVD].SourceID_token, "CR");      	// s:AI0001
	IEC450_Info.CH_INFO[CH_NAVD].SourceID_num = 1;
	strcpy(IEC450_Info.CH_INFO[CH_NAVD].DestiID_token, "EI");       	// d:EI0001
	IEC450_Info.CH_INFO[CH_NAVD].DestiID_num = 1;
	IEC450_Info.CH_INFO[CH_NAVD].Device = 1;
	IEC450_Info.CH_INFO[CH_NAVD].Channel = 1;
	IEC450_Info.CH_INFO[CH_NAVD].Transmission_Group = TRANSGROUP_NAVD;
	IEC450_Info.CH_INFO[CH_NAVD].FuncType = SF_FUNC_TYPE_UDPBC;

	// RCOM Channel
	strcpy(IEC450_Info.CH_INFO[CH_RCOM].SourceID_token, "CR");      	// s:AI0001
	IEC450_Info.CH_INFO[CH_RCOM].SourceID_num = 1;
	strcpy(IEC450_Info.CH_INFO[CH_RCOM].DestiID_token, "EI");       	// d:EI0001
	IEC450_Info.CH_INFO[CH_RCOM].DestiID_num = 1;
	IEC450_Info.CH_INFO[CH_RCOM].Device = 1;
	IEC450_Info.CH_INFO[CH_RCOM].Channel = 1;
	IEC450_Info.CH_INFO[CH_RCOM].Transmission_Group = TRANSGROUP_RCOM;
	IEC450_Info.CH_INFO[CH_RCOM].FuncType = SF_FUNC_TYPE_UDPBC;

	// BAM 1 Channel
	strcpy(IEC450_Info.CH_INFO[CH_BAM_1].SourceID_token, "CR");      	// s:AI0001
	IEC450_Info.CH_INFO[CH_BAM_1].SourceID_num = 1;
	strcpy(IEC450_Info.CH_INFO[CH_BAM_1].DestiID_token, "EI");       	// d:EI0001
	IEC450_Info.CH_INFO[CH_BAM_1].DestiID_num = 1;
	IEC450_Info.CH_INFO[CH_BAM_1].Device = 1;
	IEC450_Info.CH_INFO[CH_BAM_1].Channel = 1;
	IEC450_Info.CH_INFO[CH_BAM_1].Transmission_Group = TRANSGROUP_BAM1;
	IEC450_Info.CH_INFO[CH_BAM_1].FuncType = SF_FUNC_TYPE_UDPBC;

	// BAM 2 Channel
	strcpy(IEC450_Info.CH_INFO[CH_BAM_2].SourceID_token, "CR");      	// s:AI0001
	IEC450_Info.CH_INFO[CH_BAM_2].SourceID_num = 1;
	strcpy(IEC450_Info.CH_INFO[CH_BAM_2].DestiID_token, "EI");       	// d:EI0001
	IEC450_Info.CH_INFO[CH_BAM_2].DestiID_num = 1;
	IEC450_Info.CH_INFO[CH_BAM_2].Device = 1;
	IEC450_Info.CH_INFO[CH_BAM_2].Channel = 1;
	IEC450_Info.CH_INFO[CH_BAM_2].Transmission_Group = TRANSGROUP_BAM2;
	IEC450_Info.CH_INFO[CH_BAM_2].FuncType = SF_FUNC_TYPE_UDPBC;

	g_dev450 = std450_open(&IEC450_Info);
    if (g_dev450 == 0)
    {
        DEBUG_LOG("[%s] Failed 450 Setting \r\n", __FUNCTION__);
        return 0;
    }

	return 1;
}

void disconnect_std450()
{
	if(g_dev450 != NULL)
	{
		std450_close(g_dev450);
		std450_free(g_dev450);
		g_dev450 = NULL;
	}
}
