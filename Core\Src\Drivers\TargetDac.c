/**
******************************************************************************
* @file      TargetDac.c
* <AUTHOR>
* @date      2024-04-30
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2024 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "TargetBoard.h"

extern DAC_HandleTypeDef hdac1;

sDrvDacComponent gDrvDac;

void TBD_init_dac(void)
{
	HAL_DAC_Start(&hdac1,DAC_CHANNEL_1);
    HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_1, DAC_ALIGN_12B_R, 0);

    HAL_DAC_Start(&hdac1,DAC_CHANNEL_2);
    HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_2, DAC_ALIGN_12B_R, 0);

    memset(&gDrvDac, 0, sizeof(sDrvDacComponent));
}

void TBD_Dac_Test_Task(void)
{
    // test routine
    //HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_1, DAC_ALIGN_12B_R, gDrvDac.Value[0]);
    //HAL_DAC_SetValue(&hdac1, DAC_CHANNEL_2, DAC_ALIGN_12B_R, gDrvDac.Value[1]);
}