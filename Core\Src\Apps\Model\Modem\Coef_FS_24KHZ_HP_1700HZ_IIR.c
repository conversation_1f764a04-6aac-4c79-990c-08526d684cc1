/**
******************************************************************************
* @file      Coef_FS_24KHZ_HP_1700HZ_IIR.c
* <AUTHOR>
* @date      2025-03-26
* @brief
******************************************************************************
* @attention
* 
* Copyright (c) 2025 Intellian Technologies
* All rights reserved.
*
******************************************************************************
*/

#include "AllConst.h"
//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========

//=======_=========_=========_=========_=========_=========_=========_=========_=========_=========
//------_------_------_------_------_------_------_------//
//      must invert the sign of the coefficients A       //
//------_------_------_------_------_------_------_------//
static const double vCoeffA[FLT_FS_24KHZ_HP_1700HZ_IIR_SIZE + 0] = 
{
  +11.881084674727800,
  -65.981736890979400,
  +226.970546593162000,
  -540.175341258651000,
  +940.736809340653000,
  -1236.133215826160000,
  +1244.812621603600000,
  -965.259406578241000,
  +573.481963928752000,
  -256.948620990941000,
  +84.181883087361300,
  -19.062295954771800,
  +2.670279170188740,
  -0.174570915332449,
};
static const double vCoeffB[FLT_FS_24KHZ_HP_1700HZ_IIR_SIZE + 1] = 
{ 
  0.417816844242126,
  -5.677572676137660,
  35.986784648368400,
  -141.018224021526000,
  381.658409439645000,
  -754.672177276512000,
  1124.304583271130000,
  -1281.999240458400000,
  1124.304583271130000,
  -754.672177276512000,
  381.658409439645000,
  -141.018224021526000,
  35.986784648368400,
  -5.677572676137660,
  0.417816844242126,
};

 //-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
tBaseIIR    G_xIir_Fs_24Khz_Hp_1700hz_IIR_Int_Ch =
          {
            .dSizeB = FLT_FS_24KHZ_HP_1700HZ_IIR_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };

tBaseIIR    G_xIir_Fs_24Khz_Hp_1700hz_IIR_Local_Ch =
          {
            .dSizeB = FLT_FS_24KHZ_HP_1700HZ_IIR_SIZE,

            .pCoefA = (float64_t *)vCoeffA,
            .pCoefB = (float64_t *)vCoeffB,
            .pDataB = NULL,

            .pFuncP = (float32_t (*)(void *, float32_t))CalcNewDataIIR
          };
//-------_---------_---------_---------_---------_---------_---------_---------_---------_---------
