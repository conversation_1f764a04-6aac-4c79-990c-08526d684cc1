/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    stm32h7xx_it.c
  * @brief   Interrupt Service Routines.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "stm32h7xx_it.h"
/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "TargetBoard.h"
#include "userdef.h"
#include <stdio.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN TD */

// HardFault 발생 시 스택 프레임 구조체
typedef struct {
  uint32_t r0;
  uint32_t r1;
  uint32_t r2;
  uint32_t r3;
  uint32_t r12;
  uint32_t lr;
  uint32_t pc;
  uint32_t psr;
} HardFault_stack_t;

/* USER CODE END TD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */

void HardFault_Handler_C(uint32_t *hardfault_args);
void print_call_stack(uint32_t *stack_ptr, uint32_t pc, uint32_t lr);
void analyze_fault_cause(uint32_t cfsr, uint32_t hfsr);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/* External variables --------------------------------------------------------*/
extern ETH_HandleTypeDef heth;
extern DMA_HandleTypeDef hdma_adc1;
extern DMA_HandleTypeDef hdma_adc3;
extern ADC_HandleTypeDef hadc1;
extern ADC_HandleTypeDef hadc3;
extern FDCAN_HandleTypeDef hfdcan1;
extern TIM_HandleTypeDef htim3;
extern DMA_HandleTypeDef hdma_uart4_tx;
extern DMA_HandleTypeDef hdma_uart4_rx;
extern DMA_HandleTypeDef hdma_uart7_tx;
extern DMA_HandleTypeDef hdma_uart7_rx;
extern DMA_HandleTypeDef hdma_usart1_tx;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern DMA_HandleTypeDef hdma_usart2_tx;
extern DMA_HandleTypeDef hdma_usart2_rx;
extern UART_HandleTypeDef huart4;
extern UART_HandleTypeDef huart7;
extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart2;
extern TIM_HandleTypeDef htim1;

/* USER CODE BEGIN EV */

/* USER CODE END EV */

/******************************************************************************/
/*           Cortex Processor Interruption and Exception Handlers          */
/******************************************************************************/
/**
  * @brief This function handles Non maskable interrupt.
  */
void NMI_Handler(void)
{
  /* USER CODE BEGIN NonMaskableInt_IRQn 0 */
  NVIC_SystemReset();
  /* USER CODE END NonMaskableInt_IRQn 0 */
  /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
  while (1)
  {
  }
  /* USER CODE END NonMaskableInt_IRQn 1 */
}

/**
  * @brief This function handles Hard fault interrupt.
  */
void HardFault_Handler(void)
{
  /* USER CODE BEGIN HardFault_IRQn 0 */

  // HardFault 발생 시 레지스터 정보 출력
  __asm volatile (
    "TST LR, #4 \n"
    "ITE EQ \n"
    "MRSEQ R0, MSP \n"
    "MRSNE R0, PSP \n"
    "B HardFault_Handler_C \n"
  );

  /* USER CODE END HardFault_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_HardFault_IRQn 0 */
    /* USER CODE END W1_HardFault_IRQn 0 */
  }
}

/**
  * @brief This function handles Memory management fault.
  */
void MemManage_Handler(void)
{
  /* USER CODE BEGIN MemoryManagement_IRQn 0 */
  NVIC_SystemReset();
  /* USER CODE END MemoryManagement_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_MemoryManagement_IRQn 0 */
    /* USER CODE END W1_MemoryManagement_IRQn 0 */
  }
}

/**
  * @brief This function handles Pre-fetch fault, memory access fault.
  */
void BusFault_Handler(void)
{
  /* USER CODE BEGIN BusFault_IRQn 0 */
  NVIC_SystemReset();
  /* USER CODE END BusFault_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_BusFault_IRQn 0 */
    /* USER CODE END W1_BusFault_IRQn 0 */
  }
}

/**
  * @brief This function handles Undefined instruction or illegal state.
  */
void UsageFault_Handler(void)
{
  /* USER CODE BEGIN UsageFault_IRQn 0 */
  NVIC_SystemReset();
  /* USER CODE END UsageFault_IRQn 0 */
  while (1)
  {
    /* USER CODE BEGIN W1_UsageFault_IRQn 0 */
    /* USER CODE END W1_UsageFault_IRQn 0 */
  }
}

/**
  * @brief This function handles Debug monitor.
  */
void DebugMon_Handler(void)
{
  /* USER CODE BEGIN DebugMonitor_IRQn 0 */

  /* USER CODE END DebugMonitor_IRQn 0 */
  /* USER CODE BEGIN DebugMonitor_IRQn 1 */

  /* USER CODE END DebugMonitor_IRQn 1 */
}

/******************************************************************************/
/* STM32H7xx Peripheral Interrupt Handlers                                    */
/* Add here the Interrupt Handlers for the used peripherals.                  */
/* For the available peripheral interrupt handler names,                      */
/* please refer to the startup file (startup_stm32h7xx.s).                    */
/******************************************************************************/

/**
  * @brief This function handles DMA1 stream0 global interrupt.
  */
void DMA1_Stream0_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Stream0_IRQn 0 */

  /* USER CODE END DMA1_Stream0_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_adc1);
  /* USER CODE BEGIN DMA1_Stream0_IRQn 1 */

  /* USER CODE END DMA1_Stream0_IRQn 1 */
}

/**
  * @brief This function handles DMA1 stream1 global interrupt.
  */
void DMA1_Stream1_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Stream1_IRQn 0 */

  /* USER CODE END DMA1_Stream1_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_adc3);
  /* USER CODE BEGIN DMA1_Stream1_IRQn 1 */

  /* USER CODE END DMA1_Stream1_IRQn 1 */
}

/**
  * @brief This function handles DMA1 stream2 global interrupt.
  */
void DMA1_Stream2_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Stream2_IRQn 0 */

  /* USER CODE END DMA1_Stream2_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_uart4_tx);
  /* USER CODE BEGIN DMA1_Stream2_IRQn 1 */

  /* USER CODE END DMA1_Stream2_IRQn 1 */
}

/**
  * @brief This function handles DMA1 stream3 global interrupt.
  */
void DMA1_Stream3_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Stream3_IRQn 0 */

  /* USER CODE END DMA1_Stream3_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_uart4_rx);
  /* USER CODE BEGIN DMA1_Stream3_IRQn 1 */

  /* USER CODE END DMA1_Stream3_IRQn 1 */
}

/**
  * @brief This function handles DMA1 stream6 global interrupt.
  */
void DMA1_Stream6_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Stream6_IRQn 0 */

  /* USER CODE END DMA1_Stream6_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_uart7_tx);
  /* USER CODE BEGIN DMA1_Stream6_IRQn 1 */

  /* USER CODE END DMA1_Stream6_IRQn 1 */
}

/**
  * @brief This function handles ADC1 and ADC2 global interrupts.
  */
void ADC_IRQHandler(void)
{
  /* USER CODE BEGIN ADC_IRQn 0 */

  /* USER CODE END ADC_IRQn 0 */
  HAL_ADC_IRQHandler(&hadc1);
  /* USER CODE BEGIN ADC_IRQn 1 */

  /* USER CODE END ADC_IRQn 1 */
}

/**
  * @brief This function handles FDCAN1 interrupt 0.
  */
void FDCAN1_IT0_IRQHandler(void)
{
  /* USER CODE BEGIN FDCAN1_IT0_IRQn 0 */

  /* USER CODE END FDCAN1_IT0_IRQn 0 */
  HAL_FDCAN_IRQHandler(&hfdcan1);
  /* USER CODE BEGIN FDCAN1_IT0_IRQn 1 */

  /* USER CODE END FDCAN1_IT0_IRQn 1 */
}

/**
  * @brief This function handles TIM1 update interrupt.
  */
void TIM1_UP_IRQHandler(void)
{
  /* USER CODE BEGIN TIM1_UP_IRQn 0 */

  /* USER CODE END TIM1_UP_IRQn 0 */
  HAL_TIM_IRQHandler(&htim1);
  /* USER CODE BEGIN TIM1_UP_IRQn 1 */

  /* USER CODE END TIM1_UP_IRQn 1 */
}

/**
  * @brief This function handles TIM3 global interrupt.
  */
void TIM3_IRQHandler(void)
{
  /* USER CODE BEGIN TIM3_IRQn 0 */

  /* USER CODE END TIM3_IRQn 0 */
  HAL_TIM_IRQHandler(&htim3);
  /* USER CODE BEGIN TIM3_IRQn 1 */

  /* USER CODE END TIM3_IRQn 1 */
}

/**
  * @brief This function handles USART1 global interrupt.
  */
void USART1_IRQHandler(void)
{
  /* USER CODE BEGIN USART1_IRQn 0 */

  /* USER CODE END USART1_IRQn 0 */
  HAL_UART_IRQHandler(&huart1);
  /* USER CODE BEGIN USART1_IRQn 1 */

  /* USER CODE END USART1_IRQn 1 */
}

/**
  * @brief This function handles USART2 global interrupt.
  */
void USART2_IRQHandler(void)
{
  /* USER CODE BEGIN USART2_IRQn 0 */

  /* USER CODE END USART2_IRQn 0 */
  HAL_UART_IRQHandler(&huart2);
  /* USER CODE BEGIN USART2_IRQn 1 */

  /* USER CODE END USART2_IRQn 1 */
}

/**
  * @brief This function handles DMA1 stream7 global interrupt.
  */
void DMA1_Stream7_IRQHandler(void)
{
  /* USER CODE BEGIN DMA1_Stream7_IRQn 0 */

  /* USER CODE END DMA1_Stream7_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_uart7_rx);
  /* USER CODE BEGIN DMA1_Stream7_IRQn 1 */

  /* USER CODE END DMA1_Stream7_IRQn 1 */
}

/**
  * @brief This function handles UART4 global interrupt.
  */
void UART4_IRQHandler(void)
{
  /* USER CODE BEGIN UART4_IRQn 0 */

  /* USER CODE END UART4_IRQn 0 */
  HAL_UART_IRQHandler(&huart4);
  /* USER CODE BEGIN UART4_IRQn 1 */

  /* USER CODE END UART4_IRQn 1 */
}

/**
  * @brief This function handles DMA2 stream0 global interrupt.
  */
void DMA2_Stream0_IRQHandler(void)
{
  /* USER CODE BEGIN DMA2_Stream0_IRQn 0 */

  /* USER CODE END DMA2_Stream0_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart1_tx);
  /* USER CODE BEGIN DMA2_Stream0_IRQn 1 */

  /* USER CODE END DMA2_Stream0_IRQn 1 */
}

/**
  * @brief This function handles DMA2 stream1 global interrupt.
  */
void DMA2_Stream1_IRQHandler(void)
{
  /* USER CODE BEGIN DMA2_Stream1_IRQn 0 */

  /* USER CODE END DMA2_Stream1_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart1_rx);
  /* USER CODE BEGIN DMA2_Stream1_IRQn 1 */

  /* USER CODE END DMA2_Stream1_IRQn 1 */
}

/**
  * @brief This function handles DMA2 stream2 global interrupt.
  */
void DMA2_Stream2_IRQHandler(void)
{
  /* USER CODE BEGIN DMA2_Stream2_IRQn 0 */

  /* USER CODE END DMA2_Stream2_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart2_tx);
  /* USER CODE BEGIN DMA2_Stream2_IRQn 1 */

  /* USER CODE END DMA2_Stream2_IRQn 1 */
}

/**
  * @brief This function handles DMA2 stream3 global interrupt.
  */
void DMA2_Stream3_IRQHandler(void)
{
  /* USER CODE BEGIN DMA2_Stream3_IRQn 0 */

  /* USER CODE END DMA2_Stream3_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_usart2_rx);
  /* USER CODE BEGIN DMA2_Stream3_IRQn 1 */

  /* USER CODE END DMA2_Stream3_IRQn 1 */
}

/**
  * @brief This function handles Ethernet global interrupt.
  */
void ETH_IRQHandler(void)
{
  /* USER CODE BEGIN ETH_IRQn 0 */

  /* USER CODE END ETH_IRQn 0 */
  HAL_ETH_IRQHandler(&heth);
  /* USER CODE BEGIN ETH_IRQn 1 */

  /* USER CODE END ETH_IRQn 1 */
}

/**
  * @brief This function handles UART7 global interrupt.
  */
void UART7_IRQHandler(void)
{
  /* USER CODE BEGIN UART7_IRQn 0 */

  /* USER CODE END UART7_IRQn 0 */
  HAL_UART_IRQHandler(&huart7);
  /* USER CODE BEGIN UART7_IRQn 1 */

  /* USER CODE END UART7_IRQn 1 */
}

/**
  * @brief This function handles ADC3 global interrupt.
  */
void ADC3_IRQHandler(void)
{
  /* USER CODE BEGIN ADC3_IRQn 0 */

  /* USER CODE END ADC3_IRQn 0 */
  HAL_ADC_IRQHandler(&hadc3);
  /* USER CODE BEGIN ADC3_IRQn 1 */

  /* USER CODE END ADC3_IRQn 1 */
}

/* USER CODE BEGIN 1 */

/**
 * @brief HardFault 발생 시 레지스터 정보와 콜 스택을 출력하는 함수
 * @param hardfault_args 스택 포인터 (MSP 또는 PSP)
 */
void HardFault_Handler_C(uint32_t *hardfault_args)
{
    HardFault_stack_t *stack = (HardFault_stack_t *)hardfault_args;

    // 잠시 대기하여 UART 출력이 안정화되도록 함
    for(volatile int i = 0; i < 1000000; i++);

    DM("\r\n");
    DM("=== HARDFAULT OCCURRED ===\r\n");
    DM("Stack Frame:\r\n");
    DM("  R0  = 0x%08lX\r\n", stack->r0);
    DM("  R1  = 0x%08lX\r\n", stack->r1);
    DM("  R2  = 0x%08lX\r\n", stack->r2);
    DM("  R3  = 0x%08lX\r\n", stack->r3);
    DM("  R12 = 0x%08lX\r\n", stack->r12);
    DM("  LR  = 0x%08lX\r\n", stack->lr);
    DM("  PC  = 0x%08lX\r\n", stack->pc);
    DM("  PSR = 0x%08lX\r\n", stack->psr);

    // 추가 레지스터 정보
    uint32_t cfsr = SCB->CFSR;
    uint32_t hfsr = SCB->HFSR;
    uint32_t dfsr = SCB->DFSR;
    uint32_t afsr = SCB->AFSR;
    uint32_t bfar = SCB->BFAR;
    uint32_t mmfar = SCB->MMFAR;

    DM("\r\nFault Status Registers:\r\n");
    DM("  CFSR = 0x%08lX\r\n", cfsr);
    DM("  HFSR = 0x%08lX\r\n", hfsr);
    DM("  DFSR = 0x%08lX\r\n", dfsr);
    DM("  AFSR = 0x%08lX\r\n", afsr);
    DM("  BFAR = 0x%08lX\r\n", bfar);
    DM("  MMFAR = 0x%08lX\r\n", mmfar);

    // Fault 원인 분석
    analyze_fault_cause(cfsr, hfsr);

    // 콜 스택 출력
    print_call_stack(hardfault_args, stack->pc, stack->lr);

    DM("\r\n=== SYSTEM WILL RESET ===\r\n");

    // 출력 완료를 위한 대기
    for(volatile int i = 0; i < 2000000; i++);

    // 시스템 리셋
    NVIC_SystemReset();
}

/**
 * @brief 콜 스택을 추적하여 출력하는 함수
 * @param stack_ptr 현재 스택 포인터
 * @param pc 프로그램 카운터
 * @param lr 링크 레지스터
 */
void print_call_stack(uint32_t *stack_ptr, uint32_t pc, uint32_t lr)
{
    DM("\r\nCall Stack Trace:\r\n");
    DM("  PC: 0x%08lX\r\n", pc);
    DM("  LR: 0x%08lX\r\n", lr);

    // 스택에서 추가 주소들을 찾아 출력
    DM("\r\nStack Contents (first 16 words):\r\n");
    for(int i = 0; i < 16; i++)
    {
        uint32_t addr = (uint32_t)(stack_ptr + i);
        uint32_t value = 0;

        // 메모리 접근이 안전한지 확인 (간단한 범위 체크)
        if(addr >= 0x20000000 && addr < 0x20080000) // SRAM 범위
        {
            value = stack_ptr[i];
            DM("  [0x%08lX]: 0x%08lX", addr, value);

            // 코드 영역의 주소인지 확인 (0x08000000 ~ 0x081FFFFF)
            if(value >= 0x08000000 && value <= 0x081FFFFF)
            {
                DM(" <-- Possible return address");
            }
            DM("\r\n");
        }
        else
        {
            DM("  [0x%08lX]: <Invalid address>\r\n", addr);
        }
    }
}

/**
 * @brief Fault 원인을 분석하여 출력하는 함수
 * @param cfsr Configurable Fault Status Register
 * @param hfsr HardFault Status Register
 */
void analyze_fault_cause(uint32_t cfsr, uint32_t hfsr)
{
    DM("\r\nFault Analysis:\r\n");

    // HardFault Status Register 분석
    if(hfsr & (1 << 30)) // FORCED
    {
        DM("  FORCED: HardFault escalated from configurable fault\r\n");
    }
    if(hfsr & (1 << 1)) // VECTTBL
    {
        DM("  VECTTBL: Vector table read fault\r\n");
    }

    // MemManage Fault 분석 (CFSR[7:0])
    if(cfsr & (1 << 7)) // MMARVALID
    {
        DM("  MMARVALID: MemManage fault address valid\r\n");
    }
    if(cfsr & (1 << 5)) // MLSPERR
    {
        DM("  MLSPERR: MemManage fault during lazy FP state preservation\r\n");
    }
    if(cfsr & (1 << 4)) // MSTKERR
    {
        DM("  MSTKERR: MemManage fault on stacking\r\n");
    }
    if(cfsr & (1 << 3)) // MUNSTKERR
    {
        DM("  MUNSTKERR: MemManage fault on unstacking\r\n");
    }
    if(cfsr & (1 << 1)) // DACCVIOL
    {
        DM("  DACCVIOL: Data access violation\r\n");
    }
    if(cfsr & (1 << 0)) // IACCVIOL
    {
        DM("  IACCVIOL: Instruction access violation\r\n");
    }

    // BusFault 분석 (CFSR[15:8])
    if(cfsr & (1 << 15)) // BFARVALID
    {
        DM("  BFARVALID: BusFault address valid\r\n");
    }
    if(cfsr & (1 << 13)) // LSPERR
    {
        DM("  LSPERR: BusFault during lazy FP state preservation\r\n");
    }
    if(cfsr & (1 << 12)) // STKERR
    {
        DM("  STKERR: BusFault on stacking\r\n");
    }
    if(cfsr & (1 << 11)) // UNSTKERR
    {
        DM("  UNSTKERR: BusFault on unstacking\r\n");
    }
    if(cfsr & (1 << 10)) // IMPRECISERR
    {
        DM("  IMPRECISERR: Imprecise data access error\r\n");
    }
    if(cfsr & (1 << 9)) // PRECISERR
    {
        DM("  PRECISERR: Precise data access error\r\n");
    }
    if(cfsr & (1 << 8)) // IBUSERR
    {
        DM("  IBUSERR: Instruction bus error\r\n");
    }

    // UsageFault 분석 (CFSR[25:16])
    if(cfsr & (1 << 25)) // DIVBYZERO
    {
        DM("  DIVBYZERO: Divide by zero\r\n");
    }
    if(cfsr & (1 << 24)) // UNALIGNED
    {
        DM("  UNALIGNED: Unaligned access\r\n");
    }
    if(cfsr & (1 << 19)) // NOCP
    {
        DM("  NOCP: No coprocessor\r\n");
    }
    if(cfsr & (1 << 18)) // INVPC
    {
        DM("  INVPC: Invalid PC load\r\n");
    }
    if(cfsr & (1 << 17)) // INVSTATE
    {
        DM("  INVSTATE: Invalid state\r\n");
    }
    if(cfsr & (1 << 16)) // UNDEFINSTR
    {
        DM("  UNDEFINSTR: Undefined instruction\r\n");
    }
}

/* USER CODE END 1 */
