// @file     net_user.h
// @brief    UDP(User Datagram Protocol) & TCP(Transmission Control Protocol) user application function code.
///<AUTHOR> (<EMAIL>)
///@date     2024-08-17

#ifndef NET_USER_H
#define NET_USER_H

#include "platform.h"

#if (USE_LINUX == 1)
#include <netdb.h>
#else
#include "lwip/netdb.h"

#ifndef NI_MAXHOST
#define NI_MAXHOST IPADDR_STRLEN_MAX
#endif
#endif

#define SOCK_TYPE_TCP       0   /// TCP socket type define.
#define SOCK_TYPE_UDP       1   /// UDP socket type define.

#define SOCK_OPT_NONE       0   /// Socket option. NONE.
#define SOCK_OPT_MCAST      1   /// Socket option. Multicast.
#define SOCK_OPT_BCAST      2   /// Socket option. Broadcast.

/// @brief UDP configration struct.
struct net_targ_s {
    int d;                      /// Debugging flag.
    int fd;                     /// Using UDP socket file descriptor.
    int port;                   /// Using UDP prot.
    char ip[NI_MAXHOST];        /// Using UDP IP address.
    struct addrinfo ai;         /// Network address info. For Socket function.
    struct addrinfo *ai_ret;    /// Network address info. For Socket function.
};

typedef struct net_targ_s net_targ_t;

extern int     init_sock(net_targ_t *targ, int type, int sock_opt);
extern void    free_sock(net_targ_t *targ);
extern int     set_mcast_sock(net_targ_t* targ, char* nic_ip, int ttl);
extern ssize_t write_sock(net_targ_t *targ, char* buf, int len);
extern ssize_t read_sock(net_targ_t *targ, char* buf, int len, int tout);

#endif /* NET_USER_H */
