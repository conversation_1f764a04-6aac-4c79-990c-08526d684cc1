{"folders": [{"name": "Receiver_Navtex", "path": "."}, {"name": "receiver", "path": "../receiver"}], "settings": {"files.associations": {"targetnorflash.h": "c", "targetmac.h": "c", "common.h": "c", "targetboard.h": "c", "targetuart.h": "c", "targetisr.h": "c", "tcp.h": "c", "system_config.h": "c", "model.h": "c", "targetconfig.h": "c", "targetadc.h": "c", "common_include.h": "c", "common_def.h": "c", "common_config.h": "c", "navmodem.h": "c", "allconst.h": "c", "ld8a.h": "c", "dtx.h": "c", "targetg729.h": "c", "g729a.h": "c", "LPCFUNC.C": "cpp", "CODER.C": "cpp", "etharp.h": "c", "netif.h": "c", "sys.h": "c", "lwipopts.h": "c", "udp.h": "c", "targetfsk.h": "c", "mt25ql128aba.h": "c", "internal.h": "c", "external.h": "c", "keyconst.h": "c", "fskmodem.h": "c", "system_status.h": "c", "platform.h": "c", "sockets.h": "c", "typedef.h": "c", "massvariable.h": "c", "icomm.h": "c", "arm_math.h": "c", "task.h": "c"}}}